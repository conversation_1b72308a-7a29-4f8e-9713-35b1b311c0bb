namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// 修改头像页面数据模型
    /// </summary>
    public class ModifyHeadPageModel
    {
        public ModifyHeadPageModel()
        {
            // 初始化属性
            Message = new YaoHuo.Plugin.Template.Models.MessageModel();
            Avatar = new AvatarModel();
            SiteInfo = new YaoHuo.Plugin.Template.Models.SiteInfoModel();
        }

        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; }

        /// <summary>
        /// 消息模型（成功/错误提示）
        /// </summary>
        public YaoHuo.Plugin.Template.Models.MessageModel Message { get; set; }

        /// <summary>
        /// 头像相关数据
        /// </summary>
        public AvatarModel Avatar { get; set; }

        /// <summary>
        /// 站点信息
        /// </summary>
        public YaoHuo.Plugin.Template.Models.SiteInfoModel SiteInfo { get; set; }
    }

    /// <summary>
    /// 头像相关数据模型
    /// </summary>
    public class AvatarModel
    {
        /// <summary>
        /// 当前头像URL
        /// </summary>
        public string CurrentImageUrl { get; set; }

        /// <summary>
        /// 当前头像完整URL（含域名）
        /// </summary>
        public string CurrentFullImageUrl { get; set; }

        /// <summary>
        /// 当前系统头像编号
        /// </summary>
        public int CurrentSystemImageNumber { get; set; }

        /// <summary>
        /// 男性头像总数
        /// </summary>
        public int MaleAvatarCount { get; set; }

        /// <summary>
        /// 女性头像总数
        /// </summary>
        public int FemaleAvatarCount { get; set; }

        /// <summary>
        /// 每页显示的头像数量
        /// </summary>
        public int PerPage { get; set; }

        /// <summary>
        /// 系统头像基础URL
        /// </summary>
        public string SystemImageBaseUrl { get; set; }

        /// <summary>
        /// 相册上传URL
        /// </summary>
        public string AlbumUploadUrl { get; set; }

        /// <summary>
        /// 表单提交地址
        /// </summary>
        public string FormAction { get; set; }
    }
} 