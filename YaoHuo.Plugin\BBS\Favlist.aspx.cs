﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.ExUtility;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Web;

namespace YaoHuo.Plugin.BBS
{
	public class FavList : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		/// <summary>
		/// 数据库连接字符串
		/// </summary>
		public string KelinkWAP_Check = PubConstant.GetConnectionString("kelinkWAP_Check");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string key = "";

		public string friendtype = "";

		public string backurl = "";

		public string linkTOP = "";

		public string favtypeid = "";

		public List<favdetail_Model> listVo = null;

		public favsubject_Model bookVo = null;

		public long long_0 = 1L;

		public long index = 0L;

		public long total = 0L;

		public long pageSize = 10L;

		public long CurrentPage = 1L;

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			favtypeid = GetRequestValue("favtypeid");
			if (favtypeid == "")
			{
				favtypeid = "0";
			}
			IsLogin(userid, backurl);

			// 优先处理Ajax删除请求，不受UI版本影响
			System.Diagnostics.Debug.WriteLine($"FavList: Page_Load - action={action}");
			if (action == "delete")
			{
				System.Diagnostics.Debug.WriteLine("FavList: 进入删除单个收藏处理");
				HandleDeleteFavorite();
				return;
			}
			if (action == "deleteall")
			{
				System.Diagnostics.Debug.WriteLine("FavList: 进入删除所有收藏处理");
				HandleDeleteAllFavorites();
				return;
			}

			try
			{
				// 检查用户UI偏好并处理版本切换
				bool newVersionRendered = CheckAndHandleUIPreference();
				if (newVersionRendered)
				{
					// 新版渲染成功，直接返回，不再执行后续的旧版代码
					return;
				}
			}
			catch (System.Threading.ThreadAbortException)
			{
				// ThreadAbortException 是 Response.End() 的正常行为，直接重新抛出
				System.Diagnostics.Debug.WriteLine("FavList: Page_Load 收到 ThreadAbortException，新版渲染成功");
				throw;
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"FavList: Page_Load 异常: {ex.ToString()}");
				ERROR = WapTool.ErrorToString(ex.ToString());
			}

			switch (action)
			{
				case "class":
					showclass();
					break;
				default:
					showclass();
					break;
			}
		}

		public void showclass()
		{
			// 兼容旧版UI的数据加载方法（统一使用安全的查询服务）
			this.LoadDataForNewUI();
		}

		/// <summary>
		/// 处理删除单个收藏
		/// </summary>
		private void HandleDeleteFavorite()
		{
			try
			{
				string id = GetRequestValue("id");
				System.Diagnostics.Debug.WriteLine($"FavList: HandleDeleteFavorite - id={id}, siteid={siteid}, userid={userid}");

				if (!WapTool.IsNumeric(id))
				{
					System.Diagnostics.Debug.WriteLine($"FavList: HandleDeleteFavorite - 无效的收藏ID: {id}");
					WriteJsonResponse(false, "无效的收藏ID");
					return;
				}

				// 使用参数化查询执行删除操作
				string sql = "DELETE FROM favdetail WHERE siteid = @siteid AND userid = @userid AND id = @id";
				SqlParameter[] parameters = {
					new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
					new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
					new SqlParameter("@id", SqlDbType.BigInt) { Value = long.Parse(id) }
				};

				System.Diagnostics.Debug.WriteLine($"FavList: 执行删除SQL - siteid={siteid}, userid={userid}, id={id}");
				int result = DbHelperSQL.ExecuteNonQuery(KelinkWAP_Check, CommandType.Text, sql, parameters);
				System.Diagnostics.Debug.WriteLine($"FavList: 删除结果 - 影响行数: {result}");

				if (result > 0)
				{
					WriteJsonResponse(true, "删除成功");
				}
				else
				{
					WriteJsonResponse(false, "删除失败，收藏不存在或已被删除");
				}
			}
			catch (System.Threading.ThreadAbortException)
			{
				// ThreadAbortException 是 Response.End() 的正常行为，不需要处理
				System.Diagnostics.Debug.WriteLine("FavList: HandleDeleteFavorite - ThreadAbortException 正常，删除操作已完成");
				throw; // 重新抛出，让它正常终止
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"FavList: HandleDeleteFavorite 异常: {ex.ToString()}");
				WriteJsonResponse(false, "删除失败：" + ex.Message);
			}
		}

		/// <summary>
		/// 处理删除所有收藏
		/// </summary>
		private void HandleDeleteAllFavorites()
		{
			try
			{
				string sql;
				SqlParameter[] parameters;

				if (favtypeid == "0")
				{
					// 删除用户的所有收藏
					sql = "DELETE FROM favdetail WHERE siteid = @siteid AND userid = @userid";
					parameters = new SqlParameter[] {
						new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
						new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
					};
				}
				else
				{
					// 删除指定分类的收藏
					sql = "DELETE FROM favdetail WHERE siteid = @siteid AND userid = @userid AND favtypeid = @favtypeid";
					parameters = new SqlParameter[] {
						new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
						new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
						new SqlParameter("@favtypeid", SqlDbType.BigInt) { Value = long.Parse(favtypeid) }
					};
				}

				int result = DbHelperSQL.ExecuteNonQuery(KelinkWAP_Check, CommandType.Text, sql, parameters);
				WriteJsonResponse(true, $"成功删除 {result} 条收藏");
			}
			catch (System.Threading.ThreadAbortException)
			{
				// ThreadAbortException 是 Response.End() 的正常行为，不需要处理
				System.Diagnostics.Debug.WriteLine("FavList: HandleDeleteAllFavorites - ThreadAbortException 正常，删除操作已完成");
				throw; // 重新抛出，让它正常终止
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"FavList: HandleDeleteAllFavorites 异常: {ex.ToString()}");
				WriteJsonResponse(false, "清空失败：" + ex.Message);
			}
		}

		/// <summary>
		/// 输出JSON响应
		/// </summary>
		private void WriteJsonResponse(bool success, string message)
		{
			Response.Clear();
			Response.ContentType = "application/json; charset=utf-8";
			// 转义消息中的引号，防止JSON格式错误
			string escapedMessage = message.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "");
			string json = $"{{\"success\": {success.ToString().ToLower()}, \"message\": \"{escapedMessage}\"}}";
			Response.Write(json);
			Response.Flush();
			Response.End();
		}

		/// <summary>
		/// 检查用户UI偏好并处理版本切换
		/// </summary>
		private bool CheckAndHandleUIPreference()
		{
			string uiPreference = "";
			if (Request.Cookies["ui_preference"] != null)
			{
				uiPreference = Request.Cookies["ui_preference"].Value;
			}
			if (string.IsNullOrEmpty(uiPreference))
			{
				uiPreference = "old";
			}

			System.Diagnostics.Debug.WriteLine($"FavList: UI偏好={uiPreference}");

			if (uiPreference == "new")
			{
				return TryRenderWithHandlebars();
			}
			return false;
		}

		/// <summary>
		/// 尝试使用Handlebars模板渲染页面
		/// </summary>
		private bool TryRenderWithHandlebars()
		{
			try
			{
				// 使用反射检查TemplateService可用性
				var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");

				if (templateServiceType != null)
				{
					var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
					var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

					if (getViewModeMethod != null && renderPageMethod != null)
					{
						string viewMode = (string)getViewModeMethod.Invoke(null, null);

						if (viewMode == "new")
						{
							System.Diagnostics.Debug.WriteLine("FavList: 开始新版渲染");
							RenderWithHandlebars();
							return true;
						}
					}
				}

				ERROR = "Handlebars模板服务不可用";
				System.Diagnostics.Debug.WriteLine("FavList: Handlebars模板服务不可用");
				return false;
			}
			catch (System.Threading.ThreadAbortException)
			{
				// ThreadAbortException 是 Response.End() 的正常行为
				System.Diagnostics.Debug.WriteLine("FavList: 新版渲染成功，ThreadAbortException 正常传播");
				throw; // 重新抛出异常，让它传播到上层
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"FavList: TryRenderWithHandlebars 异常: {ex.ToString()}");
				ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
				return false;
			}
		}

	/// <summary>
	/// 统一的数据加载方法（支持新旧版本UI）
	/// </summary>
	private void LoadDataForNewUI()
	{
		this.key = base.GetRequestValue("key");

		try
		{
			this.pageSize = Convert.ToInt32(base.siteVo.MaxPerPage_Default);

			if (base.GetRequestValue("page") != "")
			{
				this.CurrentPage = long.Parse(base.GetRequestValue("page"));
			}

			// 使用 FavQueryService 进行安全的参数化查询
			var favQueryService = new YaoHuo.Plugin.WebSite.Services.FavQueryService(this.KelinkWAP_Check);

			var queryResult = favQueryService.SearchFavorites(
				base.siteid,
				base.userid,
				this.favtypeid,
				this.key,
				(int)this.pageSize,
				(int)this.CurrentPage);

			// 检查查询结果
			if (queryResult.HasError)
			{
				this.ERROR = queryResult.ErrorMessage;
				System.Diagnostics.Debug.WriteLine($"FavList: 查询出错 - {queryResult.ErrorMessage}");
				return;
			}

			// 设置查询结果
			this.listVo = queryResult.Favorites;
			this.total = queryResult.Total;
			this.CurrentPage = queryResult.CurrentPage;

			// 构建链接URL（保持与原版兼容）
			this.linkURL = base.http_start + "bbs/favlist.aspx?action=class&amp;siteid=" + base.siteid + "&amp;classid=" + base.classid + "&amp;favtypeid=" + this.favtypeid + "&amp;key=" + HttpUtility.UrlEncode(this.key) + "&amp;backurl=" + HttpUtility.UrlEncode(this.backurl) + "&amp;getTotal=" + this.total;
			this.linkTOP = WapTool.GetPageLinkShowTOP(base.ver, base.lang, this.total, this.pageSize, this.CurrentPage, this.linkURL);
			this.linkURL = WapTool.GetPageLink(base.ver, base.lang, Convert.ToInt32(this.total), this.pageSize, this.CurrentPage, this.linkURL);

			// 处理收藏分类信息
			if (this.favtypeid != "0")
			{
				favsubject_BLL favsubject_BLL = new favsubject_BLL(this.string_10);
				this.bookVo = favsubject_BLL.GetModel(long.Parse(this.favtypeid));
			}

			System.Diagnostics.Debug.WriteLine($"FavList: LoadDataForNewUI 完成 - 总数:{this.total}, 当前页:{this.CurrentPage}, 数据量:{this.listVo.Count}");
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"FavList: LoadDataForNewUI 异常 - {ex.ToString()}");
			this.ERROR = ex.ToString();
		}
	}

	/// <summary>
	/// 获取页面标题
	/// </summary>
	private string GetPageTitle()
	{
		if (this.favtypeid == "0")
		{
			return "我的收藏";
		}
		else if (this.bookVo != null)
		{
			return this.bookVo.subjectname;
		}
		return "收藏夹";
	}

	/// <summary>
	/// 构建FavList页面数据模型
	/// </summary>
	private FavListPageModel BuildFavListPageModel()
	{
		var model = new FavListPageModel
		{
			PageTitle = GetPageTitle(),
			FavTypeId = this.favtypeid,
			SearchKey = this.key ?? "",
			ShowSearchBox = false, // 先设为false，后面根据是否有收藏内容来决定
			Error = this.ERROR ?? "",
			Info = "", // 收藏页面暂时没有INFO消息
			BackUrl = this.backurl ?? "",
			UserId = base.userid ?? "",
			SiteId = base.siteid ?? "",
			ClassId = base.classid ?? "",
			HasOperations = true // 显示清空收藏按钮
		};

		// 构建收藏列表
		if (this.listVo != null)
		{
			foreach (var fav in this.listVo)
			{
				var favItem = new FavItemModel
				{
					Id = fav.id,
					Title = fav.title ?? "",
					Url = fav.url ?? "",
					AddDate = fav.adddate,
					FavTypeId = fav.favtypeid,
					IsExternalLink = !string.IsNullOrEmpty(fav.url) && fav.url.IndexOf("http://") >= 0,
					DeleteUrl = $"{base.http_start}bbs/favlist.aspx?action=delete&siteid={base.siteid}&favtypeid={this.favtypeid}&id={fav.id}",
					FullUrl = GetFullUrl(fav.url)
				};

				model.FavoritesList.Add(favItem);
			}
		}

		// 只有当有收藏内容时才显示搜索框
		model.ShowSearchBox = model.FavoritesList.Count > 0;

		// 处理分页信息
		var paginationModel = new PaginationModel();
		if (this.linkURL != null)
		{
			// 从现有的分页数据中提取信息
			paginationModel.CurrentPage = (int)this.CurrentPage;
			paginationModel.Total = (int)this.total;
			paginationModel.PageSize = (int)this.pageSize;
			paginationModel.TotalPages = paginationModel.PageSize > 0 ?
				(int)Math.Ceiling((double)paginationModel.Total / paginationModel.PageSize) : 0;

			paginationModel.ShowPagination = paginationModel.Total > paginationModel.PageSize;
			paginationModel.IsFirstPage = paginationModel.CurrentPage <= 1;
			paginationModel.IsLastPage = paginationModel.CurrentPage >= paginationModel.TotalPages;
		}
		else
		{
			paginationModel.ShowPagination = false;
			paginationModel.CurrentPage = 1;
			paginationModel.TotalPages = 1;
			paginationModel.Total = model.FavoritesList.Count;
			paginationModel.PageSize = model.FavoritesList.Count;
			paginationModel.IsFirstPage = true;
			paginationModel.IsLastPage = true;
		}

		model.Pagination = paginationModel;

		return model;
	}

	/// <summary>
	/// 获取完整的URL
	/// </summary>
	private string GetFullUrl(string url)
	{
		if (string.IsNullOrEmpty(url))
		{
			return "";
		}

		if (url.IndexOf("http://") >= 0)
		{
			return url; // 外部链接
		}
		else
		{
			return base.http_start + url; // 内部链接
		}
	}

	/// <summary>
	/// 使用Handlebars模板渲染页面
	/// </summary>
	private void RenderWithHandlebars()
	{
		try
		{
			// 在新版渲染中直接处理数据加载
			this.LoadDataForNewUI();

			// 构建页面数据模型
			var pageModel = BuildFavListPageModel();

			// 获取页面标题
			string pageTitle = GetPageTitle();
			System.Diagnostics.Debug.WriteLine($"FavList: 渲染页面 - {pageTitle}，数据量: {pageModel.FavoritesList.Count}");

			// 调用 TemplateService.RenderPageWithLayout 方法
			var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
			if (templateServiceType == null)
			{
				throw new InvalidOperationException("无法获取 TemplateService 类型");
			}

			var renderMethod = templateServiceType.GetMethod("RenderPageWithLayout");
			if (renderMethod == null)
			{
				throw new InvalidOperationException("无法获取 RenderPageWithLayout 方法");
			}

			// 创建 HeaderOptionsModel
			var headerOptions = new HeaderOptionsModel { ShowViewModeToggle = false };

			string finalHtml = (string)renderMethod.Invoke(null, new object[]
			{
				"~/Template/Pages/FavList.hbs",
				pageModel,
				pageTitle,
				headerOptions,
				null,
				"~/Template/Layouts/MainLayout.hbs"
			});

			// 输出渲染结果
			Response.Clear();
			Response.ContentType = "text/html; charset=utf-8";
			Response.Write(finalHtml);
			Response.End();
		}
		catch (System.Threading.ThreadAbortException)
		{
			// Response.End() 的正常行为
			System.Diagnostics.Debug.WriteLine("FavList: Response.End() 正常终止");
		}
		catch (Exception ex)
		{
			// 错误处理
			System.Diagnostics.Debug.WriteLine($"FavList: RenderWithHandlebars 严重错误: {ex.ToString()}");
			Response.Clear();
			Response.ContentType = "text/html; charset=utf-8";
			Response.Write($"<div style='color:red; padding:20px; border:2px solid red;'>页面渲染时发生严重错误: {ex.Message}<br><br>详细信息:<br><pre>{ex.ToString()}</pre></div>");
			HttpContext.Current.ApplicationInstance.CompleteRequest();
		}
	}
	}
}