# TemplateService 重新设计与现有页面适配指南

## 1. 背景与目标

在 `YaoHuo.Plugin` 项目中集成 Handlebars.NET 进行 UI 现代化的过程中，我们遇到了 Helper 无法在运行时被正确解析的顽固问题。尽管尝试了多种修复方案，包括将 Helper 注册移至静态构造函数，问题依然存在。日志分析表明，Helper 注册代码行并未按预期执行，或者模板渲染时使用的 Handlebars 环境与注册时的环境不一致。

本文档旨在基于这些经验，提出一个新的 `TemplateService` 设计方案，并指导如何将之前已改造的页面 (`MyFile.aspx`, `EditProfile.aspx`, `ModifyPW.aspx`, `ModifyHead.aspx`) 适配到这个新的服务层。

**核心目标：**

*   建立一个稳定、可预测、易于调试的 Handlebars.NET 模板服务。
*   确保 Helper 和 Partial 的注册在整个应用程序生命周期中只执行一次，并且在模板编译和渲染时始终可用。
*   统一所有 Handlebars 页面的渲染模式，提高代码一致性和可维护性。

## 2. 新 `TemplateService` 设计

核心思想是：**在 `TemplateService` 内部显式创建和管理一个全局唯一的 `IHandlebars` 实例，并确保所有 Handlebars 相关操作（注册、编译、渲染）都通过此实例进行。**

### 2.1. `TemplateService.cs` 核心结构

```csharp
// YaoHuo.Plugin/WebSite/Tool/TemplateService.cs
using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using HandlebarsDotNet;
using System.Diagnostics; // 用于 Debug.WriteLine

namespace YaoHuo.Plugin.WebSite.Tool
{
    public static class TemplateService
    {
        // 显式管理的、全局唯一的 Handlebars 环境实例
        private static readonly IHandlebars _handlebarsEnvironment;

        // 模板缓存
        private static readonly Dictionary<string, HandlebarsTemplate<object, object>> _templateCache =
            new Dictionary<string, HandlebarsTemplate<object, object>>();
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 静态构造函数，在类首次被访问时执行一次。
        /// 用于创建 IHandlebars 实例并注册全局 Helpers 和 Partials。
        /// </summary>
        static TemplateService()
        {
            Debug.WriteLine("**************************************************************");
            Debug.WriteLine("TemplateService: STATIC CONSTRUCTOR - Initializing...");
            Debug.WriteLine("**************************************************************");

            try
            {
                var config = new HandlebarsConfiguration();
                // 可在此处对 config进行自定义配置，例如：
                // config.ThrowOnUnresolvedBindingExpression = true; // 更严格的绑定检查
                // config.BlockHelpersMissing = (writer, options, context, arguments) => { /* 自定义处理 */ };

                _handlebarsEnvironment = Handlebars.Create(config);
                Debug.WriteLine("TemplateService: IHandlebars environment CREATED.");

                RegisterHelpersInternal(_handlebarsEnvironment);
                RegisterPartialsInternal(_handlebarsEnvironment);

                Debug.WriteLine("TemplateService: STATIC CONSTRUCTOR - Initialization COMPLETE.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                Debug.WriteLine($"TemplateService: CRITICAL ERROR in Static Constructor: {ex.ToString()}");
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                // 抛出异常可能会阻止应用程序正常启动，但这有助于暴露初始化问题。
                // 在生产环境中，可能需要更优雅的错误处理或回退机制。
                throw;
            }
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Helpers 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterHelpersInternal(IHandlebars handlebarsInstance)
        {
            Debug.WriteLine("TemplateService: RegisterHelpersInternal - STARTING Helper Registration.");

            // 示例：eq Helper
            handlebarsInstance.RegisterHelper("eq", (writer, options, context, parameters) =>
            {
                Debug.WriteLine("%%%% TemplateService: EXECUTING 'eq' HELPER LOGIC %%%%");
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'eq' REGISTERED.");

            // 示例：ne Helper
            handlebarsInstance.RegisterHelper("ne", (writer, options, context, parameters) =>
            {
                Debug.WriteLine("%%%% TemplateService: EXECUTING 'ne' HELPER LOGIC %%%%");
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (!string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'ne' REGISTERED.");
            
            // 示例：formatNumber Helper
            handlebarsInstance.RegisterHelper("formatNumber", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    if (long.TryParse(parameters[0].ToString(), out long number))
                    {
                        writer.WriteSafeString(number.ToString("N0"));
                    }
                    else
                    {
                        writer.WriteSafeString(parameters[0].ToString());
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'formatNumber' REGISTERED.");

            // 示例：hasPermission Helper (块助手)
            handlebarsInstance.RegisterHelper("hasPermission", (writer, options, context, parameters) =>
            {
                bool conditionMet = false;
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string permission = parameters[0].ToString();
                    conditionMet = (permission != "普通");
                }

                if (conditionMet)
                {
                    options.Template(writer, context);
                }
                else
                {
                    options.Inverse(writer, context);
                }
            });
            Debug.WriteLine("TemplateService: Helper 'hasPermission' REGISTERED.");
            
            // 添加其他项目中需要的 Helpers...

            Debug.WriteLine("TemplateService: RegisterHelpersInternal - Helper Registration COMPLETE.");
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Partials 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterPartialsInternal(IHandlebars handlebarsInstance)
        {
            Debug.WriteLine("TemplateService: RegisterPartialsInternal - STARTING Partial Registration.");
            try
            {
                string partialsPath = HttpContext.Current.Server.MapPath("~/Template/Partials");
                if (Directory.Exists(partialsPath))
                {
                    foreach (string file in Directory.GetFiles(partialsPath, "*.hbs"))
                    {
                        string partialName = Path.GetFileNameWithoutExtension(file);
                        string content = File.ReadAllText(file);
                        handlebarsInstance.RegisterTemplate(partialName, content); // 注册到指定实例
                        Debug.WriteLine($"TemplateService: Partial '{partialName}' REGISTERED.");
                    }
                }
                else
                {
                    Debug.WriteLine($"TemplateService: Partials directory NOT FOUND: {partialsPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TemplateService: ERROR during RegisterPartialsInternal: {ex.ToString()}");
                // 根据需要处理或重新抛出异常
            }
            Debug.WriteLine("TemplateService: RegisterPartialsInternal - Partial Registration COMPLETE.");
        }

        /// <summary>
        /// 编译 Handlebars 模板并缓存。
        /// 所有编译操作都使用内部的 _handlebarsEnvironment 实例。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径 (例如 "~/Template/Pages/MyPage.hbs")</param>
        /// <returns>编译后的模板委托</returns>
        public static HandlebarsTemplate<object, object> CompileTemplate(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
            {
                throw new ArgumentNullException(nameof(templatePath));
            }

            string物理路径 = HttpContext.Current.Server.MapPath(templatePath);
            Debug.WriteLine($"TemplateService: CompileTemplate - Requesting template: '{templatePath}' (Physical: '{物理路径}')");

            lock (_cacheLock)
            {
                if (_templateCache.TryGetValue(物理路径, out var cachedTemplate))
                {
                    Debug.WriteLine($"TemplateService: CompileTemplate - Cache HIT for '{物理路径}'.");
                    return cachedTemplate;
                }

                Debug.WriteLine($"TemplateService: CompileTemplate - Cache MISS for '{物理路径}'. Compiling...");
                if (!File.Exists(物理路径))
                {
                    Debug.WriteLine($"TemplateService: CompileTemplate - ERROR: File NOT FOUND at '{物理路径}'.");
                    throw new FileNotFoundException("模板文件未找到。", 物理路径);
                }

                string templateContent = File.ReadAllText(物理路径);
                var compiledTemplate = _handlebarsEnvironment.Compile(templateContent); // 使用内部实例编译
                _templateCache[物理路径] = compiledTemplate;

                Debug.WriteLine($"TemplateService: CompileTemplate - COMPILED and CACHED '{物理路径}'.");
                return compiledTemplate;
            }
        }

        /// <summary>
        /// 渲染指定的 Handlebars 模板（通常是页面级模板或布局模板）。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径</param>
        /// <param name="model">传递给模板的数据模型</param>
        /// <returns>渲染后的 HTML 字符串</returns>
        public static string RenderTemplate(string templatePath, object model)
        {
            Debug.WriteLine($"TemplateService: RenderTemplate - Rendering '{templatePath}'.");
            try
            {
                var compiledTemplate = CompileTemplate(templatePath); // 获取编译后的模板
                return compiledTemplate(model); // 执行渲染
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                Debug.WriteLine($"TemplateService: ERROR during RenderTemplate for '{templatePath}': {ex.ToString()}");
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                // 返回一个清晰的错误信息，便于调试。生产环境可能需要更通用的错误提示。
                return $"<div style='border:2px solid red; padding:10px; background-color:#ffe0e0; color:red;'>模板渲染错误 ({Path.GetFileName(templatePath)}): {HttpUtility.HtmlEncode(ex.Message)}<br><pre>{HttpUtility.HtmlEncode(ex.StackTrace)}</pre></div>";
            }
        }

        /// <summary>
        /// 渲染完整的页面，包括页面主体和主布局。
        /// 这是推荐的页面渲染入口点。
        /// </summary>
        /// <param name="pageTemplatePath">页面主体模板的路径 (例如 "~/Template/Pages/MyFile.hbs")</param>
        /// <param name="pageModel">传递给页面主体模板的数据模型</param>
        /// <param name="pageTitle">页面标题，将用于布局</param>
        /// <param name="headerOptions">头部选项模型，用于布局</param>
        /// <param name="pageSpecificCss">页面专属CSS文件路径 (可选)</param>
        /// <param name="mainLayoutPath">主布局模板的路径 (默认为 "~/Template/Layouts/MainLayout.hbs")</param>
        /// <returns>渲染后的完整 HTML 页面</returns>
        public static string RenderPageWithLayout(
            string pageTemplatePath,
            object pageModel,
            string pageTitle,
            HeaderOptionsModel headerOptions,
            string pageSpecificCss = null,
            string mainLayoutPath = "~/Template/Layouts/MainLayout.hbs")
        {
            Debug.WriteLine($"TemplateService: RenderPageWithLayout - Rendering page '{pageTemplatePath}' with layout '{mainLayoutPath}'.");

            // 1. 渲染页面主体内容
            string pageContentHtml = RenderTemplate(pageTemplatePath, pageModel);

            // 2. 构建布局模型
            var layoutModel = new
            {
                PageTitle = pageTitle,
                Content = pageContentHtml, // 将渲染好的页面主体作为 Content 传递给布局
                HeaderOptions = headerOptions ?? new HeaderOptionsModel(),
                PageSpecificCss = pageSpecificCss
                // 可以根据需要添加其他布局所需的数据，如 SiteInfo, FooterInfo 等
            };

            // 3. 渲染主布局
            return RenderTemplate(mainLayoutPath, layoutModel);
        }

        /// <summary>
        /// 获取用户当前的UI偏好设置 ("new" 或 "old")。
        /// </summary>
        public static string GetViewMode()
        {
            var request = HttpContext.Current?.Request;
            if (request?.Cookies["ui_preference"] != null)
            {
                return request.Cookies["ui_preference"].Value;
            }
            return "new"; // 默认新版，或根据项目配置调整
        }

        /// <summary>
        /// 清除模板缓存（主要用于开发调试）。
        /// </summary>
        public static void ClearCache()
        {
            lock (_cacheLock)
            {
                _templateCache.Clear();
                Debug.WriteLine("TemplateService: Template Cache CLEARED.");
            }
        }
    }

    /// <summary>
    /// 传递给布局模板的头部配置选项。
    /// (应与项目中实际的 HeaderOptionsModel.cs 定义保持一致)
    /// </summary>
    public class HeaderOptionsModel // 确保这个模型在项目中实际存在且可访问
    {
        public bool ShowViewModeToggle { get; set; } = true; // 默认为true，显示切换按钮
        public string CustomButtonText { get; set; }
        public string CustomButtonLink { get; set; }
        public string CustomButtonIcon { get; set; }
        // 可以根据需要添加更多属性
    }
}
```

### 2.2. 关键设计点说明

*   **`_handlebarsEnvironment` (IHandlebars)**：
    *   在静态构造函数中**仅创建一次**。
    *   所有 Helper 和 Partial 的注册都发生在这个实例上。
    *   所有模板的编译 (`_handlebarsEnvironment.Compile()`) 也都使用这个实例。
    *   这从根本上保证了注册、编译、渲染的环境一致性。
*   **静态构造函数 `static TemplateService()`**：
    *   CLR 保证它在类的任何静态成员被访问或任何实例被创建之前自动执行，并且只执行一次。这是进行一次性初始化的理想场所。
    *   内部包含了详细的日志，便于追踪初始化过程和排查问题。
    *   如果初始化失败（例如 Helper 注册时发生异常），异常会被记录并重新抛出，这有助于在开发早期发现问题。
*   **`RegisterHelpersInternal` 和 `RegisterPartialsInternal`**：
    *   私有静态方法，接收 `IHandlebars` 实例作为参数，确保注册到正确的环境。
    *   内部有详细的日志，记录每个 Helper/Partial 的注册情况。
*   **`CompileTemplate(string templatePath)`**：
    *   公共静态方法，负责加载、编译模板文件，并进行缓存。
    *   编译操作明确使用 `_handlebarsEnvironment.Compile()`。
*   **`RenderTemplate(string templatePath, object model)`**：
    *   公共静态方法，获取编译后的模板并执行渲染。
*   **`RenderPageWithLayout(...)`**：
    *   **推荐的页面渲染入口**。它封装了先渲染页面主体，然后将主体内容作为数据传递给主布局模板进行二次渲染的通用模式。
    *   这样可以统一页面渲染逻辑，简化各页面 `.aspx.cs` 文件中的代码。
*   **日志记录**：
    *   在关键步骤（初始化、注册、编译、渲染、缓存操作、错误）添加了 `System.Diagnostics.Debug.WriteLine` 日志。这些日志对于追踪问题至关重要。
*   **`HeaderOptionsModel`**：
    *   在 `TemplateService.cs` 内部提供了一个示例定义。请确保您的项目中有一个实际的、可访问的 `HeaderOptionsModel.cs` 文件，并且其定义与此处一致或兼容。如果它在 `YaoHuo.Plugin.BBS.Models` 命名空间下，请确保 `TemplateService.cs` 可以引用到。

## 3. 初始集成与验证步骤

在将现有页面适配到新的 `TemplateService` 之前，强烈建议进行一次极简的集成验证：

1.  **彻底清理环境**：
    *   关闭 Visual Studio。
    *   手动删除项目 `bin` 和 `obj` 文件夹。
    *   确保所有 `iisexpress.exe` 进程已终止。
    *   清理 ASP.NET 临时文件 (`%SystemRoot%\Microsoft.NET\Framework[64]\v4.0.30319\Temporary ASP.NET Files`)。
2.  **应用新的 `TemplateService.cs` 代码**。
3.  **创建测试页面 (`TestHandlebars.aspx`)**：
    *   **`TestHandlebars.aspx` (Markup):**
        ```html
        <%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TestHandlebars.aspx.cs" Inherits="YaoHuo.Plugin.TestHandlebars" %>
        <!DOCTYPE html>
        <html><head><title>Handlebars Test</title></head><body>
            <asp:Literal ID="ltlOutput" runat="server"></asp:Literal>
        </body></html>
        ```
    *   **`TestHandlebars.aspx.cs` (Code-behind):**
        ```csharp
        using System;
        using System.Web.UI.WebControls;
        using YaoHuo.Plugin.WebSite.Tool; // 引用新的 TemplateService

        namespace YaoHuo.Plugin
        {
            public partial class TestHandlebars : System.Web.UI.Page
            {
                protected void Page_Load(object sender, EventArgs e)
                {
                    try
                    {
                        // 1. 直接编译和渲染一个包含已知 Helper (如 eq) 的字符串模板
                        string testTemplateString = "<div>Test eq: {{#if (eq Status 'Active')}}<b>Active</b>{{else}}<i>Inactive</i>{{/if}}</div>";
                        var compiledStringTemplate = TemplateService._handlebarsEnvironment.Compile(testTemplateString); // 直接访问内部实例进行测试
                        string result1 = compiledStringTemplate(new { Status = "Active" });
                        string result2 = compiledStringTemplate(new { Status = "Pending" });

                        ltlOutput.Text = $"<p>String Test 1 (Active): {result1}</p>";
                        ltlOutput.Text += $"<p>String Test 2 (Pending): {result2}</p>";

                        // 2. 测试通过 RenderTemplate 渲染一个简单的 .hbs 文件（如果需要）
                        //    首先在 ~/Template/Pages/ 创建一个 TestPage.hbs: <div>Helper Test: {{eq Status "Test"}}Success{{else}}Fail{{/eq}}</div>
                        //    string fileTestResult = TemplateService.RenderTemplate("~/Template/Pages/TestPage.hbs", new { Status = "Test" });
                        //    ltlOutput.Text += $"<p>File Test: {fileTestResult}</p>";


                        // 3. 测试 RenderPageWithLayout (可选，但推荐)
                        //    确保 TestPage.hbs 和 MainLayout.hbs 存在且 MainLayout.hbs 能处理 Content
                        //    string fullPageResult = TemplateService.RenderPageWithLayout(
                        //        "~/Template/Pages/TestPage.hbs",
                        //        new { Status = "Test" },
                        //        "Test Full Page",
                        //        new HeaderOptionsModel { ShowViewModeToggle = false }
                        //    );
                        //    ltlOutput.Text += $"<hr/>Full Page Test:<br/>{fullPageResult}";


                        System.Diagnostics.Debug.WriteLine("TestHandlebars.aspx: SUCCESSFULLY EXECUTED TEST RENDERS.");
                    }
                    catch (Exception ex)
                    {
                        ltlOutput.Text = $"<p style='color:red;'>TEST FAILED: {ex.ToString()}</p>";
                        System.Diagnostics.Debug.WriteLine($"TestHandlebars.aspx: TEST FAILED: {ex.ToString()}");
                    }
                }
            }
        }
        ```
        *注意：在 `TestHandlebars.aspx.cs` 中直接访问 `TemplateService._handlebarsEnvironment.Compile` 仅用于初始验证，常规页面应使用 `TemplateService.RenderTemplate` 或 `RenderPageWithLayout`。*
4.  **运行 `TestHandlebars.aspx`**。
5.  **检查页面输出和调试日志**：
    *   页面应正确显示 "Active" 和 "Inactive"。
    *   调试输出窗口应显示 `TemplateService` 的静态构造函数日志、Helper 注册日志（特别是 `eq` 的注册日志）以及 `eq` Helper 内部的执行日志。
    *   如果一切正常，这表明新的 `TemplateService` 核心机制是工作的。

## 4. 适配现有页面

在新的 `TemplateService` 通过了上述的初步验证后，可以开始适配之前已改造的页面。核心思路是让它们都使用新的 `TemplateService.RenderPageWithLayout` 方法。

以下是针对每个页面的适配指南：

### 通用修改模式 (`TryRenderWithHandlebars` 方法)：

对于所有页面的 `.aspx.cs` 文件中的 `TryRenderWithHandlebars` (或类似名称) 方法，将其修改为如下模式：

```csharp
// 示例：在 MyFile.aspx.cs 中
private void TryRenderWithHandlebars() // 或 RenderWithHandlebars
{
    try
    {
        // 1. 如果页面处理表单提交，先执行表单处理逻辑
        //    string action = base.Request.Form.Get("action");
        //    if (action == "gomod") { ProcessFormSubmission(); }

        // 2. 构建页面主体所需的数据模型
        var pageModel = BuildUserPageModel(); // (这是您已有的方法)

        // 3. 调用新的 RenderPageWithLayout 方法
        string finalHtml = TemplateService.RenderPageWithLayout(
            "~/Template/Pages/MyFile.hbs",    // 页面模板路径
            pageModel,                         // 页面数据模型
            "个人中心",                        // 页面标题
            new HeaderOptionsModel()           // 头部选项 (按需配置)
            // pageSpecificCss: "/Template/CSS/MyFile.css" // 如果有页面专属CSS
        );

        // 4. 输出渲染结果
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write(finalHtml);
        // Response.End(); // 通常由 CompleteRequest 替代，或根据项目需要决定是否使用
        HttpContext.Current.ApplicationInstance.CompleteRequest(); // 推荐，以避免 ThreadAbortException
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 ({this.GetType().Name}): {ex.ToString()}");
        // 决定是回退到旧版，还是显示错误信息
        // ERROR = "新版界面加载失败，请刷新或联系管理员。";
        // RenderOldVersionLogic(); // 如果有回退逻辑
        // 或者直接让错误被捕获并显示 TemplateService.RenderTemplate 返回的错误HTML
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
        HttpContext.Current.ApplicationInstance.CompleteRequest();
    }
}
```

### 4.1. `MyFile.aspx.cs`

*   **主要修改**：
    *   定位 `MyFile.aspx.cs` 中的 `RenderWithHandlebars` 方法 (或 `TryRenderWithHandlebars` 内实际执行渲染的部分)。
    *   将其中的渲染逻辑替换为调用 `TemplateService.RenderPageWithLayout`。
    *   `BuildUserPageModel()` 方法保持不变，它构建的是传递给 `MyFile.hbs` 的模型。
    *   `pageTitle` 为 "个人中心"。
    *   `HeaderOptions` 可以使用默认的 `new HeaderOptionsModel()`，或者根据需要进行配置（例如，如果该页面不应显示UI切换按钮，则 `new HeaderOptionsModel { ShowViewModeToggle = false }`）。

*   **原 `RenderWithHandlebars` 中的双重 `RenderPage` 调用将被替换**：
    ```csharp
    // 旧的渲染方式（将被替换）:
    // string renderedHtml = (string)renderPageMethod.Invoke(null, new object[]
    // {
    //     "~/Template/Layouts/MainLayout.hbs",
    //     new
    //     {
    //         pageTitle = "个人中心",
    //         content = (string)renderPageMethod.Invoke(null, new object[] // <--- 嵌套调用
    //         {
    //             "~/Template/Pages/MyFile.hbs",
    //             model
    //         }),
    //         HeaderOptions = new HeaderOptionsModel()
    //     }
    // });
    ```

### 4.2. `EditProfile.aspx.cs`

*   **主要修改**：
    *   定位 `TryRenderWithHandlebars` 方法。
    *   修改渲染逻辑以使用 `TemplateService.RenderPageWithLayout`。
    *   `BuildEditProfilePageModel()` 方法保持不变。
    *   `pageTitle` 为 "编辑资料"。
    *   `HeaderOptions` 通常在此页面不显示切换按钮，所以使用 `new HeaderOptionsModel { ShowViewModeToggle = false }`。

*   **原 `TryRenderWithHandlebars` 中的渲染逻辑将被替换**：
    ```csharp
    // 旧的渲染方式（将被替换）:
    // string htmlOutput = TemplateService.RenderPage("~/Template/Pages/EditProfile.hbs", model);
    // var layoutModel = new { /* ... */ };
    // string finalHtml = TemplateService.RenderPage("~/Template/Layouts/MainLayout.hbs", layoutModel);
    ```

### 4.3. `ModifyPW.aspx.cs`

*   **假设的结构** (因为未提供此文件，但通常与 `EditProfile` 类似)：
    *   您会有一个 `TryRenderWithHandlebars` 方法。
    *   一个 `BuildModifyPasswordPageModel()` (或类似名称) 的方法来构建数据。
*   **主要修改**：
    *   修改 `TryRenderWithHandlebars` 方法中的渲染逻辑以使用 `TemplateService.RenderPageWithLayout`。
    *   `pageTitle` 为 "修改密码"。
    *   `HeaderOptions` 通常为 `new HeaderOptionsModel { ShowViewModeToggle = false }`。

### 4.4. `ModifyHead.aspx.cs`

*   **主要修改**：
    *   定位 `TryRenderWithHandlebars` 方法。
    *   修改渲染逻辑以使用 `TemplateService.RenderPageWithLayout`。
    *   `BuildModifyHeadPageModel()` 方法保持不变。
    *   `pageTitle` 为 "更换头像"。
    *   `HeaderOptions` 通常为 `new HeaderOptionsModel { ShowViewModeToggle = false }`。

*   **原 `TryRenderWithHandlebars` 中的渲染逻辑将被替换**（与 `EditProfile.aspx.cs` 类似）。

### 4.5. `MainLayout.hbs` 的兼容性

确保您的 `~/Template/Layouts/MainLayout.hbs` 文件能够正确处理从 `RenderPageWithLayout` 传递过来的数据模型，特别是：

*   `{{PageTitle}}`
*   `{{{Content}}}` (用三个大括号输出已渲染的页面主体HTML)
*   `{{HeaderOptions}}` (如果布局内部的 `Header.hbs` partial 需要它)
*   `{{PageSpecificCss}}` (如果需要动态加载页面专属CSS)

例如，在 `MainLayout.hbs` 中：

```handlebars
<head>
    <title>{{PageTitle}} - 你的站点名称</title>
    {{#if PageSpecificCss}}
        <link rel="stylesheet" href="{{PageSpecificCss}}">
    {{/if}}
    <!-- 其他 head 内容 -->
</head>
<body>
    {{> Header PageTitle=PageTitle HeaderOptions=HeaderOptions}}
    <div class="main-content">
        {{{Content}}}
    </div>
    {{> Footer}}
    <!-- 其他 script -->
</body>
```

## 5. 测试与验证

1.  **环境清理**：每次重大修改后，务必彻底清理环境。
2.  **逐个页面测试**：
    *   修改完一个页面的 `.aspx.cs`后，立即测试该页面。
    *   检查新版 UI 是否按预期渲染。
    *   检查所有功能（表单提交、链接等）是否正常。
    *   查看调试日志，确认没有 Handlebars 相关的错误，并且 Helper 注册和执行日志符合预期。
3.  **回归测试**：修改完所有页面后，对所有已改造的 Handlebars 页面进行一次完整的回归测试，确保它们之间没有相互影响。
4.  **新旧 UI 切换**：测试 `ui_preference` Cookie 控制的新旧 UI 切换功能是否在所有页面上都正常工作。

## 6. 总结

通过采用显式 `IHandlebars` 实例管理和统一的 `RenderPageWithLayout` 渲染模式，新的 `TemplateService` 设计旨在提供一个更稳定、更可预测的 Handlebars 集成基础。虽然这需要对现有已改造页面进行一些适配工作，但从长远来看，这将显著提高项目的可维护性和未来进行更多 UI 现代化的效率。

在整个过程中，**细致的日志记录** 和 **彻底的环境清理** 是排查问题的关键辅助手段。 