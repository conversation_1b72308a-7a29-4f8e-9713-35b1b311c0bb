using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// FavList 收藏页面数据模型
    /// </summary>
    public class FavListPageModel
    {
        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; }

        /// <summary>
        /// 收藏类型ID (0=全部收藏，其他=特定分类)
        /// </summary>
        public string FavTypeId { get; set; }

        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string SearchKey { get; set; }

        /// <summary>
        /// 是否显示搜索框
        /// </summary>
        public bool ShowSearchBox { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Error { get; set; }

        /// <summary>
        /// 提示信息
        /// </summary>
        public string Info { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(Error);

        /// <summary>
        /// 是否有提示信息
        /// </summary>
        public bool HasInfo => !string.IsNullOrEmpty(Info);

        /// <summary>
        /// 收藏列表
        /// </summary>
        public List<FavItemModel> FavoritesList { get; set; } = new List<FavItemModel>();

        /// <summary>
        /// 分页导航数据
        /// </summary>
        public PaginationModel Pagination { get; set; }

        /// <summary>
        /// 返回 URL
        /// </summary>
        public string BackUrl { get; set; }

        /// <summary>
        /// 当前用户 ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 站点 ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// 栏目 ID
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 是否有操作权限（显示清空按钮等）
        /// </summary>
        public bool HasOperations { get; set; }
    }

    /// <summary>
    /// 单个收藏项的数据模型
    /// </summary>
    public class FavItemModel
    {
        /// <summary>
        /// 收藏ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 收藏标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 收藏链接
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 完整的URL（包含域名）
        /// </summary>
        public string FullUrl { get; set; }

        /// <summary>
        /// 是否为外部链接
        /// </summary>
        public bool IsExternalLink { get; set; }

        /// <summary>
        /// 收藏类型ID
        /// </summary>
        public long FavTypeId { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDate { get; set; }

        /// <summary>
        /// 删除链接
        /// </summary>
        public string DeleteUrl { get; set; }

        /// <summary>
        /// 友好的时间显示
        /// </summary>
        public string FriendlyAddDate
        {
            get
            {
                var timeSpan = DateTime.Now - AddDate;
                if (timeSpan.TotalDays >= 1)
                {
                    return $"{(int)timeSpan.TotalDays}天前";
                }
                else if (timeSpan.TotalHours >= 1)
                {
                    return $"{(int)timeSpan.TotalHours}小时前";
                }
                else if (timeSpan.TotalMinutes >= 1)
                {
                    return $"{(int)timeSpan.TotalMinutes}分钟前";
                }
                else
                {
                    return "刚刚";
                }
            }
        }

        /// <summary>
        /// 链接图标类型
        /// </summary>
        public string LinkIcon
        {
            get
            {
                if (IsExternalLink)
                {
                    return "external-link";
                }
                else
                {
                    // 根据URL判断内部链接类型
                    if (!string.IsNullOrEmpty(Url))
                    {
                        if (Url.Contains("book_view") || Url.Contains("book_"))
                        {
                            return "book-open";
                        }
                        else if (Url.Contains("bbs") || Url.Contains("forum"))
                        {
                            return "message-square";
                        }
                        else if (Url.Contains("user") || Url.Contains("profile"))
                        {
                            return "user";
                        }
                    }
                    return "link";
                }
            }
        }
    }
} 
