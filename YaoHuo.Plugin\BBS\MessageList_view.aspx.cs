﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList_view : MyPageWap
    {

        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string types = "";

        public string id = "";

        public string backurl = "";

        public string INFO = "";

        public string page = "";

        public string needpwFlag = "";

        public string needpw = "";

        public string issystem = "";

        public string isclose = "0";

        public string touserNickname = "";

        public wap_message_Model bookVo = new wap_message_Model();

        public List<wap_message_Model> listVo = null;

        public string senderNickname = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            issystem = GetRequestValue("issystem");
            isclose = GetRequestValue("isclose");
            backurl = base.Request.QueryString.Get("backurl");
            id = base.Request.QueryString.Get("id");
            page = base.Request.QueryString.Get("page");
            types = base.Request.QueryString.Get("types");
            backurl = base.Request.QueryString.Get("backurl");
            needpw = GetRequestValue("needpw");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            if (!WapTool.IsNumeric(id))
            {
                id = "0";
            }
            IsLogin(userid, backurl);
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            if (isclose == "")
            {
                if (base.Request.Cookies["KL_MESSAGE_TIMES"] != null)
                {
                    isclose = base.Request.Cookies["KL_MESSAGE_TIMES"].Value;
                }
            }
            else
            {
                base.Response.Cookies["KL_MESSAGE_TIMES"].Expires = DateTime.Now.AddYears(1);
                base.Response.Cookies["KL_MESSAGE_TIMES"].Value = isclose;
            }
            wap_message_BLL wap_message_BLL = new wap_message_BLL(a);
            bookVo = wap_message_BLL.GetModel(long.Parse(id));
            if (bookVo.userid.ToString() != userid && bookVo.touserid.ToString() != userid)
            {
                ShowTipInfo(GetLang("你没有权限！|你沒有權限|You do not have permission"), "");
            }
            needpwFlag = WapTool.GetArryString(siteVo.Version, '|', 31);
            if (bookVo.isnew == 1L)
            {
                MainBll.UpdateSQL("update wap_message set isnew=0 where id=" + long.Parse(id));
            }
            condition = " siteid=" + siteid + " and isnew < 2 and issystem<> 2 and   ((touserid=" + userid + " and userid=" + bookVo.userid + ") or (touserid=" + bookVo.userid + " and userid=" + userid + ") ) ";
            if (isclose != "1")
            {
                listVo = wap_message_BLL.GetListVo(20L, 1L, condition, "*", "id", 1000L, 1L);
            }

            // 获取发件人昵称（消息发送方，应该用 bookVo.touserid）
            user_BLL userBll = new user_BLL(a);
            user_Model senderUser = userBll.getUserInfo(bookVo.touserid.ToString(), siteid);
            if (senderUser != null && !string.IsNullOrEmpty(senderUser.nickname))
            {
                this.senderNickname = senderUser.nickname;
            }
            else
            {
                this.senderNickname = bookVo.touserid.ToString();
            }

            // 获取收件人昵称（消息接收方，应该用 bookVo.userid）
            touserNickname = "";
            user_Model touser = userBll.getUserInfo(bookVo.userid.ToString(), siteid);
            if (touser != null && !string.IsNullOrEmpty(touser.nickname))
            {
                touserNickname = touser.nickname;
            }
            else
            {
                touserNickname = bookVo.userid.ToString();
            }

            // 重要说明：
            // bookVo.touserid 表示消息发送方（发件人，谁发的消息）
            // bookVo.userid   表示消息接收方（收件人，谁收到的消息）
            // senderNickname  是发件人昵称（通过 bookVo.touserid 查得）
            // touserNickname  是收件人昵称（通过 bookVo.userid 查得）
            // 这些 public 字段都可以在 aspx 前端页面通过 <%= senderNickname %>、<%= touserNickname %>、<%= bookVo.touserid %>、<%= bookVo.userid %> 直接调用
            //
            // 例如：
            // <b>发件人：</b><a href="...touserid=<%= bookVo.touserid %>..."><%= senderNickname %></a>
            // <b>收件人：</b><a href="...touserid=<%= bookVo.userid %>..."><%= touserNickname %></a>
            //
            // 切记不要混淆字段含义！
        }
    }
}