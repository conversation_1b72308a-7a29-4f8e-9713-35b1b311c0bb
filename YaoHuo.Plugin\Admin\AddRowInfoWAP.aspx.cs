﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Admin
{
    public class AddRowInfoWAP : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string string_11 = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/BaseSiteModifyWML.aspx?siteid=" + siteid);
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("path");
                requestValue = WapTool.URLtoWAP(requestValue);
                if (!(classid == "0"))
                {
                    MainBll.UpdateSQL("update [class] set siterowremark='" + requestValue + "' where userid=" + siteid + " and classid=" + classid);
                }
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}