﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="RMBtoMoney.aspx.cs" Inherits="YaoHuo.Plugin.chinabank_WAP.RMBtoMoney" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("购买" + siteVo.sitemoneyname + "|购买" + siteVo.sitemoneyname + "|Money to change"), wmlVo));
    strhtml.Append("<div class=\"tip\">");
    strhtml.Append(this.ERROR);
    if (this.INFO == "OK")
    {
        strhtml.Append("<b>");
        strhtml.Append(this.GetLang("操作成功！|操作成功！|Successfully Update"));
        strhtml.Append("</b><br/>");
    }
    else if (this.INFO == "CLOSE")
    {
        strhtml.Append("<b>站长关闭此功能！</b><br/>");
    }
    else if (this.INFO == "PWERR")
    {
        strhtml.Append("<b>密码错误！</b><br/>");
    }
    else if (this.INFO == "NUM")
    {
        strhtml.Append("<b>金额需要数字！</b><br/>");
    }
    else if (this.INFO == "NOTUSER")
    {
        strhtml.Append("<b>用户不存！</b><br/>");
    }
    else if (this.INFO == "NOTMONEY")
    {
        strhtml.Append("<b>你的RMB只有" + userVo.RMB.ToString("f2") + "！</b><br/>");
    }
    else if (this.INFO == "MAXMONEY")
    {
        strhtml.Append("<b>每次不能大于￥:1000.00！</b><br/>");
    }
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"title\">" + this.GetLang("购买" + siteVo.sitemoneyname + "|购买" + siteVo.sitemoneyname + "|Money to change") + "</div>");

    if (this.INFO != "OK" && this.INFO != "CLOSE")
    {
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "chinabank_wap/rmbtomoney.aspx\" method=\"post\">");
        strhtml.Append("我的RMB￥" + userVo.RMB.ToString("f2") + " [<a href=\"" + http_start + "chinabank_wap/selbank_wap.aspx?siteid=" + siteid + "\">充值</a>]<br/>当前" + siteVo.sitemoneyname + "：" + userVo.money + "<br/>");
        strhtml.Append("<b>￥ 1 元 = " + STATE + " " + siteVo.sitemoneyname + "</b><br/>");
        strhtml.Append("输入金额<br/>");
        strhtml.Append("<input type=\"number\" placeholder=\"请输入RMB数字\" style=\"width:90%;max-width:205px;\" class=\"txt\" name=\"tomoney\" value=\"" + this.tomoney + "\"/><br/>");
        strhtml.Append("我的密码<br/>");
        strhtml.Append("<input type=\"text\" style=\"width:90%;max-width:205px;\" class=\"txt\" name=\"changePW\" value=\"\"/><br/>");
        strhtml.Append("<input type=\"hidden\"  name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\"  name=\"backurl\" value=\"" + backurl + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"add\"/>");
        strhtml.Append("<input type=\"submit\" class=\"btn\" name=\"g\" value=\"" + this.GetLang("确定兑换|确定|submit") + "\" /><br/>");
        strhtml.Append("</form>");
        strhtml.Append("</div>");
    }
    string isWebHtml = this.ShowWEB_view(this.classid);
    strhtml.Append("<div class=\"btBox\"><div class=\"bt1\">");
    strhtml.Append("<a class=\"noafter\" href=\"" + this.http_start + (backurl) + "" + "\">返回源来页</a> ");
    if (isWebHtml == "")
    {
        strhtml.Append("<a href=\"" + this.http_start + "wapindex.aspx?siteid=" + siteid + "&amp;classid=0" + "\">返回首页</a>");
        strhtml.Append(WapTool.GetVS(wmlVo));
    }
    strhtml.Append("</div></div>");
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>