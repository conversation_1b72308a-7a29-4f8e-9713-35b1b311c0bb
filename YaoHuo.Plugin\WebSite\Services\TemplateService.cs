using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using HandlebarsDotNet;
using System.Diagnostics; // 用于 Debug.WriteLine
using System.Text;
using YaoHuo.Plugin.BBS.Models; // 引用HeaderOptionsModel定义

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// Handlebars模板渲染服务
    /// </summary>
    public static class TemplateService
    {
        // 显式管理的、全局唯一的 Handlebars 环境实例
        private static readonly IHandlebars _handlebarsEnvironment;

        // 模板缓存
        private static readonly Dictionary<string, HandlebarsTemplate<object, object>> _templateCache =
            new Dictionary<string, HandlebarsTemplate<object, object>>();
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 静态构造函数，在类首次被访问时执行一次。
        /// 用于创建 IHandlebars 实例并注册全局 Helpers 和 Partials。
        /// </summary>
        static TemplateService()
        {
            Debug.WriteLine("**************************************************************");
            Debug.WriteLine("TemplateService: STATIC CONSTRUCTOR - Initializing...");
            Debug.WriteLine("**************************************************************");

            try
            {
                var config = new HandlebarsConfiguration();
                // 可在此处对 config进行自定义配置，例如：
                // config.ThrowOnUnresolvedBindingExpression = true; // 更严格的绑定检查
                // config.BlockHelpersMissing = (writer, options, context, arguments) => { /* 自定义处理 */ };

                _handlebarsEnvironment = Handlebars.Create(config);
                Debug.WriteLine("TemplateService: IHandlebars environment CREATED.");

                RegisterHelpersInternal(_handlebarsEnvironment);
                RegisterPartialsInternal(_handlebarsEnvironment);

                Debug.WriteLine("TemplateService: STATIC CONSTRUCTOR - Initialization COMPLETE.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                Debug.WriteLine($"TemplateService: CRITICAL ERROR in Static Constructor: {ex.ToString()}");
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                // 抛出异常可能会阻止应用程序正常启动，但这有助于暴露初始化问题。
                // 在生产环境中，可能需要更优雅的错误处理或回退机制。
                throw;
            }
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Helpers 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterHelpersInternal(IHandlebars handlebarsInstance)
        {
            Debug.WriteLine("TemplateService: RegisterHelpersInternal - STARTING Helper Registration.");

            // 示例：eq Helper
            handlebarsInstance.RegisterHelper("eq", (writer, options, context, parameters) =>
            {
                Debug.WriteLine("%%%% TemplateService: EXECUTING 'eq' HELPER LOGIC %%%%");
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'eq' REGISTERED.");

            // 示例：ne Helper
            handlebarsInstance.RegisterHelper("ne", (writer, options, context, parameters) =>
            {
                Debug.WriteLine("%%%% TemplateService: EXECUTING 'ne' HELPER LOGIC %%%%");
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (!string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'ne' REGISTERED.");
            
            // 示例：formatNumber Helper
            handlebarsInstance.RegisterHelper("formatNumber", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    if (long.TryParse(parameters[0].ToString(), out long number))
                    {
                        writer.WriteSafeString(number.ToString("N0"));
                    }
                    else
                    {
                        writer.WriteSafeString(parameters[0].ToString());
                    }
                }
            });
            Debug.WriteLine("TemplateService: Helper 'formatNumber' REGISTERED.");

            // 示例：hasPermission Helper (块助手)
            handlebarsInstance.RegisterHelper("hasPermission", (writer, options, context, parameters) =>
            {
                bool conditionMet = false;
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string permission = parameters[0].ToString();
                    conditionMet = (permission != "普通");
                }

                if (conditionMet)
                {
                    options.Template(writer, context);
                }
                else
                {
                    options.Inverse(writer, context);
                }
            });
            Debug.WriteLine("TemplateService: Helper 'hasPermission' REGISTERED.");

            // URL生成助手
            handlebarsInstance.RegisterHelper("url", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string path = parameters[0].ToString();
                    // 这里可以添加基础URL处理逻辑
                    writer.WriteSafeString(path);
                }
            });
            Debug.WriteLine("TemplateService: Helper 'url' REGISTERED.");

            // firstChar Helper - 获取字符串的第一个字符
            handlebarsInstance.RegisterHelper("firstChar", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string str = parameters[0].ToString();
                    string result = (str != null && str.Length > 0) ? str.Substring(0, 1) : "?";
                    writer.WriteSafeString(result);
                }
                else
                {
                    writer.WriteSafeString("?");
                }
            });
            Debug.WriteLine("TemplateService: Helper 'firstChar' REGISTERED.");

            // formatDate Helper - 格式化日期
            handlebarsInstance.RegisterHelper("formatDate", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    if (DateTime.TryParse(parameters[0].ToString(), out DateTime date))
                    {
                        writer.WriteSafeString(date.ToString("yyyy-MM-dd"));
                    }
                    else
                    {
                        writer.WriteSafeString(parameters[0].ToString());
                    }
                }
                else
                {
                    writer.WriteSafeString("");
                }
            });
            Debug.WriteLine("TemplateService: Helper 'formatDate' REGISTERED.");
            
            // 添加其他项目中需要的 Helpers...

            Debug.WriteLine("TemplateService: RegisterHelpersInternal - Helper Registration COMPLETE.");
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Partials 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterPartialsInternal(IHandlebars handlebarsInstance)
        {
            Debug.WriteLine("TemplateService: RegisterPartialsInternal - STARTING Partial Registration.");
            try
            {
                string partialsPath = HttpContext.Current.Server.MapPath("~/Template/Partials");
                if (Directory.Exists(partialsPath))
                {
                    foreach (string file in Directory.GetFiles(partialsPath, "*.hbs"))
                    {
                        string partialName = Path.GetFileNameWithoutExtension(file);
                        string content = File.ReadAllText(file);
                        handlebarsInstance.RegisterTemplate(partialName, content); // 注册到指定实例
                        Debug.WriteLine($"TemplateService: Partial '{partialName}' REGISTERED.");
                    }
                }
                else
                {
                    Debug.WriteLine($"TemplateService: Partials directory NOT FOUND: {partialsPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TemplateService: ERROR during RegisterPartialsInternal: {ex.ToString()}");
                // 根据需要处理或重新抛出异常
            }
            Debug.WriteLine("TemplateService: RegisterPartialsInternal - Partial Registration COMPLETE.");
        }

        /// <summary>
        /// 编译 Handlebars 模板并缓存。
        /// 所有编译操作都使用内部的 _handlebarsEnvironment 实例。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径 (例如 "~/Template/Pages/MyPage.hbs")</param>
        /// <returns>编译后的模板委托</returns>
        public static HandlebarsTemplate<object, object> CompileTemplate(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
            {
                throw new ArgumentNullException(nameof(templatePath));
            }

            string 物理路径 = HttpContext.Current.Server.MapPath(templatePath);
            Debug.WriteLine($"TemplateService: CompileTemplate - Requesting template: '{templatePath}' (Physical: '{物理路径}')");

            lock (_cacheLock)
            {
                if (_templateCache.TryGetValue(物理路径, out var cachedTemplate))
                {
                    Debug.WriteLine($"TemplateService: CompileTemplate - Cache HIT for '{物理路径}'.");
                    return cachedTemplate;
                }

                Debug.WriteLine($"TemplateService: CompileTemplate - Cache MISS for '{物理路径}'. Compiling...");
                if (!File.Exists(物理路径))
                {
                    Debug.WriteLine($"TemplateService: CompileTemplate - ERROR: File NOT FOUND at '{物理路径}'.");
                    throw new FileNotFoundException("模板文件未找到。", 物理路径);
                }

                string templateContent = File.ReadAllText(物理路径);
                var compiledTemplate = _handlebarsEnvironment.Compile(templateContent); // 使用内部实例编译
                _templateCache[物理路径] = compiledTemplate;

                Debug.WriteLine($"TemplateService: CompileTemplate - COMPILED and CACHED '{物理路径}'.");
                return compiledTemplate;
            }
        }

        /// <summary>
        /// 渲染指定的 Handlebars 模板（通常是页面级模板或布局模板）。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径</param>
        /// <param name="model">传递给模板的数据模型</param>
        /// <returns>渲染后的 HTML 字符串</returns>
        public static string RenderTemplate(string templatePath, object model)
        {
            Debug.WriteLine($"TemplateService: RenderTemplate - Rendering '{templatePath}'.");
            try
            {
                var compiledTemplate = CompileTemplate(templatePath); // 获取编译后的模板
                return compiledTemplate(model); // 执行渲染
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                Debug.WriteLine($"TemplateService: ERROR during RenderTemplate for '{templatePath}': {ex.ToString()}");
                Debug.WriteLine($"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                // 返回一个清晰的错误信息，便于调试。生产环境可能需要更通用的错误提示。
                return $"<div style='border:2px solid red; padding:10px; background-color:#ffe0e0; color:red;'>模板渲染错误 ({Path.GetFileName(templatePath)}): {HttpUtility.HtmlEncode(ex.Message)}<br><pre>{HttpUtility.HtmlEncode(ex.StackTrace)}</pre></div>";
            }
        }

        /// <summary>
        /// 获取用户当前的UI偏好设置 ("new" 或 "old")。
        /// </summary>
        public static string GetViewMode()
        {
            var request = HttpContext.Current?.Request;
            if (request?.Cookies["ui_preference"] != null)
            {
                return request.Cookies["ui_preference"].Value;
            }
            return "new"; // 默认新版，或根据项目配置调整
        }

        /// <summary>
        /// 清除模板缓存（主要用于开发调试）。
        /// </summary>
        public static void ClearCache()
        {
            lock (_cacheLock)
            {
                _templateCache.Clear();
                Debug.WriteLine("TemplateService: Template Cache CLEARED.");
            }
        }

        /// <summary>
        /// 调试方法：检查TemplateService的初始化状态
        /// </summary>
        public static string GetDebugInfo()
        {
            var info = new StringBuilder();
            info.AppendLine("=== TemplateService Debug Info ===");
            info.AppendLine($"_handlebarsEnvironment is null: {_handlebarsEnvironment == null}");
            info.AppendLine($"Template cache count: {_templateCache.Count}");
            
            // 尝试编译一个简单的测试模板来验证Helper
            try
            {
                var testTemplate = _handlebarsEnvironment.Compile("{{#eq 'test' 'test'}}SUCCESS{{else}}FAIL{{/eq}}");
                var result = testTemplate(new { });
                info.AppendLine($"EQ Helper test result: {result}");
            }
            catch (Exception ex)
            {
                info.AppendLine($"EQ Helper test FAILED: {ex.Message}");
            }
            
            info.AppendLine("=== End Debug Info ===");
            return info.ToString();
        }

        /// <summary>
        /// 渲染页面
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="model">数据模型</param>
        /// <returns>渲染后的HTML</returns>
        public static string RenderPage(string templatePath, object model)
        {
            // 保持向后兼容的方法
            return RenderTemplate(templatePath, model);
        }

        /// <summary>
        /// 渲染完整的页面，包括页面主体和主布局。
        /// 这是推荐的页面渲染入口点。
        /// </summary>
        /// <param name="pageTemplatePath">页面主体模板的路径 (例如 "~/Template/Pages/MyFile.hbs")</param>
        /// <param name="pageModel">传递给页面主体模板的数据模型</param>
        /// <param name="pageTitle">页面标题，将用于布局</param>
        /// <param name="headerOptions">头部选项模型，用于布局</param>
        /// <param name="pageSpecificCss">页面专属CSS文件路径 (可选)</param>
        /// <param name="mainLayoutPath">主布局模板的路径 (默认为 "~/Template/Layouts/MainLayout.hbs")</param>
        /// <returns>渲染后的完整 HTML 页面</returns>
        public static string RenderPageWithLayout(
            string pageTemplatePath,
            object pageModel,
            string pageTitle,
            HeaderOptionsModel headerOptions,
            string pageSpecificCss = null,
            string mainLayoutPath = "~/Template/Layouts/MainLayout.hbs")
        {
            Debug.WriteLine($"TemplateService: RenderPageWithLayout - Rendering page '{pageTemplatePath}' with layout '{mainLayoutPath}'.");

            // 1. 渲染页面主体内容
            string pageContentHtml = RenderTemplate(pageTemplatePath, pageModel);

            // 2. 构建布局模型
            var layoutModel = new
            {
                PageTitle = pageTitle,
                Content = pageContentHtml, // 将渲染好的页面主体作为 Content 传递给布局
                HeaderOptions = headerOptions ?? new HeaderOptionsModel(),
                PageSpecificCss = pageSpecificCss
                // 可以根据需要添加其他布局所需的数据，如 SiteInfo, FooterInfo 等
            };

            // 3. 渲染主布局
            return RenderTemplate(mainLayoutPath, layoutModel);
        }
    }
} 