<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Turnstile 显示方式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .form-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        /* 模拟原生 Turnstile 样式 */
        .cf-turnstile {
            margin: 15px 0;
            display: flex;
            justify-content: center;
        }
        
        /* 模拟 GoCaptcha 容器样式 */
        #gocaptcha-wrap {
            margin: 15px 0;
            display: none;
        }
        
        #gocaptcha-wrap.show-turnstile {
            display: block;
        }
        
        .mock-turnstile {
            width: 300px;
            height: 65px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        
        .switch-btn {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .switch-btn:hover {
            background: #218838;
        }
        
        .description {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <h1>Turnstile 验证码显示方式对比测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 原生启用 Cloudflare Turnstile 验证码</h2>
        <div class="description">
            <strong>特点：</strong>
            <ul>
                <li>直接嵌入在表单中，作为表单的自然组成部分</li>
                <li>无弹窗，无遮罩层</li>
                <li>使用标准的 .cf-turnstile 类</li>
                <li>页面加载时自动显示</li>
            </ul>
        </div>
        
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label for="username1">用户名</label>
                    <input type="text" id="username1" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label for="password1">密码</label>
                    <input type="password" id="password1" placeholder="请输入密码">
                </div>
                
                <!-- 原生 Turnstile 显示方式 -->
                <div class="cf-turnstile">
                    <div class="mock-turnstile">
                        🔒 Cloudflare Turnstile 验证码<br>
                        <small>(原生内联显示)</small>
                    </div>
                </div>
                
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">2. GoCaptcha 备用切换到 Turnstile 验证码</h2>
        <div class="description">
            <strong>修改后的特点：</strong>
            <ul>
                <li>同样直接嵌入在表单中，与原生方式一致</li>
                <li>无弹窗，无遮罩层</li>
                <li>使用相同的 .cf-turnstile 类和属性</li>
                <li>通过 JavaScript 动态切换显示</li>
                <li>显示效果与原生方式完全一致</li>
            </ul>
        </div>
        
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label for="username2">用户名</label>
                    <input type="text" id="username2" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label for="password2">密码</label>
                    <input type="password" id="password2" placeholder="请输入密码">
                </div>
                
                <!-- GoCaptcha 容器，可以切换显示 Turnstile -->
                <div id="gocaptcha-wrap">
                    <div class="mock-turnstile">
                        🔒 Cloudflare Turnstile 验证码<br>
                        <small>(备用方式，内联显示)</small>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 10px 0;">
                    <button type="button" class="switch-btn" onclick="showGoCaptcha()">显示 GoCaptcha</button>
                    <button type="button" class="switch-btn" onclick="showTurnstile()">切换到 Turnstile</button>
                </div>
                
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">3. 修改前的备用方式（弹窗显示）</h2>
        <div class="description">
            <strong>修改前的特点：</strong>
            <ul>
                <li>在模态弹窗中显示</li>
                <li>有半透明黑色遮罩层</li>
                <li>白色圆角背景，带阴影效果</li>
                <li>显示"备用验证码"标题</li>
                <li>用户体验与原生方式差异较大</li>
            </ul>
        </div>
        
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label for="username3">用户名</label>
                    <input type="text" id="username3" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label for="password3">密码</label>
                    <input type="password" id="password3" placeholder="请输入密码">
                </div>
                
                <div style="text-align: center; margin: 15px 0;">
                    <button type="button" class="switch-btn" onclick="showModal()">显示弹窗验证码</button>
                </div>
                
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <!-- 模拟弹窗 -->
    <div id="modal-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999; align-items: center; justify-content: center;">
        <div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.4); max-width: 420px; width: 95%;">
            <div style="padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0;">备用验证码</h3>
                <button onclick="hideModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
            </div>
            <div style="padding: 20px; text-align: center;">
                <div class="mock-turnstile">
                    🔒 Cloudflare Turnstile 验证码<br>
                    <small>(修改前的弹窗显示)</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showGoCaptcha() {
            const wrap = document.getElementById('gocaptcha-wrap');
            wrap.classList.remove('show-turnstile');
            wrap.innerHTML = '<div class="mock-turnstile">🎯 GoCaptcha 滑动验证<br><small>(模拟 GoCaptcha 显示)</small></div>';
            wrap.style.display = 'block';
        }
        
        function showTurnstile() {
            const wrap = document.getElementById('gocaptcha-wrap');
            wrap.classList.add('show-turnstile');
            wrap.innerHTML = '<div class="mock-turnstile">🔒 Cloudflare Turnstile 验证码<br><small>(备用方式，内联显示)</small></div>';
            wrap.style.display = 'block';
        }
        
        function showModal() {
            document.getElementById('modal-overlay').style.display = 'flex';
        }
        
        function hideModal() {
            document.getElementById('modal-overlay').style.display = 'none';
        }
        
        // 初始化显示 GoCaptcha
        showGoCaptcha();
    </script>
</body>
</html>
