/**
 * 身份购买/充值页面通用脚本
 * 适用于妖晶购买和RMB购买两个页面
 */
document.addEventListener("DOMContentLoaded", function () {
    // 处理后端传入的变量
    // 如果是字符串类型，转换为数字
    if (typeof currentRank === 'string') {
        currentRank = parseFloat(currentRank) || 0;
    } else {
        currentRank = currentRank || 0;
    }

    if (typeof remainingDays === 'string') {
        remainingDays = parseInt(remainingDays) || 0;
    } else {
        remainingDays = remainingDays || 0;
    }

    if (typeof targetRank === 'string') {
        targetRank = parseFloat(targetRank) || 0;
    } else {
        targetRank = targetRank || 0;
    }

    if (typeof currentId === 'string') {
        currentId = parseInt(currentId) || 0;
    } else {
        currentId = currentId || 0;
    }

    if (typeof toid === 'string') {
        toid = parseInt(toid) || 0;
    } else {
        toid = toid || 0;
    }

    // 设置身份颜色映射
    const colorMap = {
        '绿色昵称': '#25a444',
        '红色昵称': '#FF0000',
        '蓝色昵称': '#228aff',
        '紫色昵称': '#c000ff',
        '粉色昵称': '#ff6363',
        '粉紫昵称': '#ff00c0',
        '/netimages/vip.gif': { name: '红名VIP', color: '#FF0000' },
        '/netimages/年费vip.gif': { name: '年费VIP', color: '#c000ff' },
        '/netimages/靓号.gif': { name: '靓', color: '#FF7F00' },
        '/netimages/帅.gif': { name: '帅', color: '#228aff' },
        '/netimages/newvip.gif': { name: '金名VIP', color: '#fa6700' }
    };

    // 处理身份显示
    function processIdentityElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const identityText = element.innerHTML;
        const regex = /^(绿色昵称|红色昵称|蓝色昵称|紫色昵称|粉色昵称|粉紫昵称)/;
        const match = identityText.match(regex);

        if (match && colorMap[match[0]]) {
            const coloredText = '<span style="color: ' + colorMap[match[0]] + ';">' + match[0] + '</span>';
            element.innerHTML = identityText.replace(match[0], coloredText);
        }

        const imgElement = element.querySelector('img');
        if (imgElement) {
            const imgSrc = imgElement.getAttribute('src').toLowerCase();
            if (colorMap[imgSrc]) {
                const name = colorMap[imgSrc].name;
                const color = colorMap[imgSrc].color;
                imgElement.insertAdjacentHTML('afterend', '<span style="color: ' + color + '; margin-left: 3px;">' + name + '</span>');
            }
        }
    }

    // 处理身份显示
    processIdentityElement('currentIdentity');
    processIdentityElement('targetIdentity');

    // 处理当前身份的到期日期格式，并提取剩余天数
    const currentExpiryDateElement = document.getElementById('currentExpiryDateDisplay');
    let isCurrentIdentityPermanent = false; // 标记当前身份是否为无期限
    let originalExpiryText = ''; // 存储原始文本

    if (currentExpiryDateElement) {
        originalExpiryText = currentExpiryDateElement.textContent; // 在修改前捕获原始文本

        // 检查是否包含"无期限"
        if (originalExpiryText.includes('无期限')) {
            currentExpiryDateElement.textContent = '无期限';
            isCurrentIdentityPermanent = true; // 设置标记
        } else {
            // 否则，尝试提取日期
            const dateMatch = originalExpiryText.match(/\d{4}-\d{2}-\d{2}/);
            if (dateMatch && dateMatch[0]) {
                currentExpiryDateElement.textContent = dateMatch[0];
            }
        }
    }

    // 从原始文本中提取并显示剩余天数
    const remainingDaysDisplay = document.getElementById('remainingDaysDisplay');
    if (remainingDaysDisplay && originalExpiryText && !isCurrentIdentityPermanent) {
        const remainingMatch = originalExpiryText.match(/还有:(\d+)天/);
        if (remainingMatch && remainingMatch[1]) {
            remainingDaysDisplay.textContent = remainingMatch[1]; // 设置为提取到的数字
        } else {
            // 如果提取失败，但不是无期限，可以使用后端传递的默认值作为fallback
            // 注意：remainingDays 是后端通过 <%= RemainingDays %> 传递的变量，可能需要确保其可用性
            // remainingDaysDisplay.textContent = remainingDays; 
            // 考虑到我们优先从字符串提取，这里可以不设置fallback或根据实际情况决定
            // 目前先不设置fallback，依赖字符串提取
        }
    }

    // 根据条件隐藏可折抵天数图标
    const convertibleDaysIconAsset = currentExpiryDateElement?.closest('.card-row')?.querySelector('.info-icon');
    const convertibleDaysIconPlan = document.getElementById('convertibleDaysInfo')?.closest('.info-icon');

    if (toid === currentId || isCurrentIdentityPermanent) {
        if (convertibleDaysIconAsset) {
            convertibleDaysIconAsset.style.display = 'none';
        }
        if (convertibleDaysIconPlan) {
            convertibleDaysIconPlan.style.display = 'none';
        }
    } else {
        // 确保在需要显示时它是可见的 (默认可能是flex)
        if (convertibleDaysIconAsset) {
            convertibleDaysIconAsset.style.display = '';
        }
        if (convertibleDaysIconPlan) {
            convertibleDaysIconPlan.style.display = '';
        }
    }

    // 根据当前身份是否为无期限隐藏日历图标 (我的资产卡片)
    const calendarIconElement = currentExpiryDateElement?.previousElementSibling; // 获取 calendar-icon 元素
    if (calendarIconElement && calendarIconElement.classList.contains('calendar-icon')) {
        if (isCurrentIdentityPermanent) {
            calendarIconElement.style.display = 'none';
        } else {
            calendarIconElement.style.display = ''; // 确保日期情况下显示
        }
    }

    // 月数增减按钮
    const monthCount = document.getElementById('month-count');
    const numInput = document.getElementById('numInput');
    const hiddenNum = document.getElementById('hiddenNum');
    const decreaseButton = document.querySelector('.decrease-month');
    const increaseButton = document.querySelector('.increase-month');
    const minMonths = toid === 105 ? 12 : 1;

    if (decreaseButton) {
        decreaseButton.addEventListener('click', function () {
            let count = parseInt(monthCount.textContent);
            if (count > minMonths) {
                count--;
                monthCount.textContent = count;
                numInput.value = count;
                hiddenNum.value = count;
                updatePrice();
            }
        });
    }

    if (increaseButton) {
        increaseButton.addEventListener('click', function () {
            let count = parseInt(monthCount.textContent);
            count++;
            monthCount.textContent = count;
            numInput.value = count;
            hiddenNum.value = count;
            updatePrice();
        });
    }

    // 密码显示切换
    const passwordInput = document.getElementById('password');
    const togglePassword = document.getElementById('toggle-password');
    const eyeIcon = document.getElementById('eye-icon');

    if (togglePassword && passwordInput && eyeIcon) {
        togglePassword.addEventListener('click', function () {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>';
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = '<path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle>';
            }
        });
    }

    // 更新价格和转换信息
    updatePrice();
    updateConversionInfo();
});

/**
 * 更新价格显示
 * 根据不同页面显示不同格式（妖晶页面显示整数，RMB页面显示带￥的两位小数）
 */
function updatePrice() {
    const monthCount = document.getElementById('month-count');
    const priceElement = document.getElementById('price');

    if (!monthCount || !priceElement) return;

    const num = parseInt(monthCount.textContent) || 0;
    const initialPrice = parseFloat(priceElement.getAttribute('data-initial-price'));
    const totalPrice = num * initialPrice;

    // 检测是否为RMB购买页面（通过检查价格元素的文本是否包含￥）
    const isRmbPage = priceElement.textContent.includes('￥');

    if (isRmbPage) {
        priceElement.textContent = '￥' + totalPrice.toFixed(2);
    } else {
        priceElement.textContent = totalPrice.toFixed(0);
    }

    // 更新转换信息
    updateConversionInfo();
}

/**
 * 更新转换信息和有效期
 */
function updateConversionInfo() {
    // 使用后端传递的变量
    var convertibleDays = 0;
    var purchasedDays = (parseInt(document.getElementById('month-count')?.textContent) || 0) * 30;
    var totalDays = 0;

    // 获取当前身份是否为无期限的状态（从DOM读取，或假定如果当前身份和购买身份不同且当前排名为0，可能表示无期限或其他无法折算的情况）
    // 更准确的做法是在后端传递一个标识，但基于现有变量，我们可以做一个推断
    const currentExpiryDateElement = document.getElementById('currentExpiryDateDisplay');
    const isCurrentIdentityPermanent = currentExpiryDateElement?.textContent === '无期限';

    if (toid === currentId) {
        // 同一身份，直接延长有效期
        convertibleDays = 0; // 不需要折算
        totalDays = remainingDays + purchasedDays;
    } else if (currentRank > 0 && targetRank > 0) {
        // 不同身份，进行转换
        var currentValue = (currentRank / 30) * remainingDays;
        var newIdentityDaysDecimal = (currentValue / targetRank) * 30;
        convertibleDays = Math.floor(newIdentityDaysDecimal);
        totalDays = convertibleDays + purchasedDays;
    } else {
        // 无法转换（包括无期限、排名为0等情况）
        convertibleDays = 0;
        totalDays = purchasedDays;
    }

    // 根据条件隐藏可折抵天数图标
    const convertibleDaysIconAsset = document.getElementById('convertibleDays')?.closest('.info-icon');
    const convertibleDaysIconPlan = document.getElementById('convertibleDaysInfo')?.closest('.info-icon');

    if (toid === currentId || isCurrentIdentityPermanent) {
        if (convertibleDaysIconAsset) {
            convertibleDaysIconAsset.style.display = 'none';
        }
        if (convertibleDaysIconPlan) {
            convertibleDaysIconPlan.style.display = 'none';
        }
    } else {
        // 确保在需要显示时它是可见的 (默认可能是flex)
        if (convertibleDaysIconAsset) {
            convertibleDaysIconAsset.style.display = '';
        }
        if (convertibleDaysIconPlan) {
            convertibleDaysIconPlan.style.display = '';
        }
    }

    // 更新页面显示
    const convertibleDaysElement = document.getElementById('convertibleDays');
    const convertibleDaysInfoElement = document.getElementById('convertibleDaysInfo');
    const totalDaysElement = document.getElementById('totalDays');
    const expiryDateElement = document.getElementById('expiryDate');

    if (convertibleDaysElement) {
        convertibleDaysElement.textContent = convertibleDays;
    }

    if (convertibleDaysInfoElement) {
        convertibleDaysInfoElement.textContent = convertibleDays;
    }

    if (totalDaysElement) {
        totalDaysElement.textContent = totalDays;
    }

    // 计算并显示到期日期
    if (expiryDateElement) {
        const today = new Date();
        today.setDate(today.getDate() + totalDays);
        const expiryDate = today.toISOString().split('T')[0];
        expiryDateElement.textContent = expiryDate;

        // 保存购买信息到页面中，用于成功购买后显示
        window.purchaseInfo = {
            currentIdentity: document.getElementById('currentIdentity')?.textContent || '',
            targetIdentity: document.getElementById('targetIdentity')?.textContent || '',
            monthCount: document.getElementById('month-count')?.textContent || '0',
            // 确保价格格式正确（包括货币符号）
            price: document.getElementById('price')?.textContent || '0',
            expiryDate: expiryDate,
            // 额外添加转换信息
            convertibleDays: convertibleDays,
            totalDays: totalDays
        };

        // 调试信息
        console.log('Purchase info saved:', window.purchaseInfo);
    }
} 