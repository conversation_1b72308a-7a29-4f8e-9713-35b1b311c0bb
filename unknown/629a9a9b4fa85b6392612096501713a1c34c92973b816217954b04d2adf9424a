﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Games.Chat
{
    public class Admin_WAPdel : MyPageWap
    {
        private readonly string string_0 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            page = GetRequestValue("page");
            IsCheckManagerLvl("|00|01|03|", classVo.adminusername, GetUrlQueryString());
            if (action == "godel")
            {
                try
                {
                    MainBll.UpdateSQL("delete from [wap2_games_chat] where id=" + id + " and siteid=" + siteid);
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    INFO = ex.ToString();
                }
            }
        }
    }
}