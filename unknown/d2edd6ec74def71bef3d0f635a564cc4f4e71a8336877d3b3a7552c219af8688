﻿using KeLin.ClassManager.BLL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_List_Search : BaseBBSListPage
    {
        // 常量定义
        private const string SEARCH_TYPE_CONTENT = "content";
        private const string SEARCH_TYPE_TITLE = "title";
        private const string SEARCH_TYPE_AUTHOR = "author";
        private const string SEARCH_TYPE_DAYS = "days";
        private const string SEARCH_TYPE_PUB = "pub";

        // 搜索特定属性
        private static bool EnableMultiKeywordSearch = true;
        private static Dictionary<string, DateTime> lastSearchTime = new Dictionary<string, DateTime>();
        private static readonly TimeSpan searchLogInterval = TimeSpan.FromMinutes(2);

        // 添加公共属性
        public string key = "";
        public string type = "";
        public string titlecolor = "";

        public static void ToggleMultiKeywordSearch()
        {
            EnableMultiKeywordSearch = !EnableMultiKeywordSearch;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            ShowSearch();
        }

        private void ShowSearch()
        {
            try
            {
                var searchParams = GetAndValidateSearchParams();
                if (searchParams == null) return;

                // 设置公共属性，以便前端访问
                this.key = searchParams.Key;
                this.type = searchParams.Type;

                SetupClassAndCondition(searchParams);
                AddSearchConditions(searchParams);
                ProcessSearch(searchParams);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        private class SearchParameters
        {
            public string Key { get; set; }
            public string Type { get; set; }
            public string Pub { get; set; }
            public string DisplayText { get; set; }
            public string DisplayValue { get; set; }
        }

        private SearchParameters GetAndValidateSearchParams()
        {
            var key = GetRequestValue("key");
            var type = GetRequestValue("type");
            var pub = GetRequestValue("pub");

            // 处理关键词
            if (!string.IsNullOrEmpty(key))
            {
                key = ProcessKeyword(key);
            }

            // 处理用户ID
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
            {
                key = key?.TrimStart('0');
                pub = pub?.TrimStart('0');
            }

            // 验证搜索参数
            if (key.IsNull() && pub.IsNull())
            {
                ShowTipInfo("禁止空白搜索", "");
                return null;
            }

            // 检查登录要求
            if (WapTool.GetSiteDefault(siteVo.Version, 60) == "1")
            {
                IsLogin(userid, GetUrlQueryString());
            }

            return new SearchParameters
            {
                Key = key,
                Type = type,
                Pub = pub,
                DisplayText = GetDisplayText(type, pub),
                DisplayValue = GetDisplayValue(key, pub, type)
            };
        }

        private string ProcessKeyword(string key)
        {
            // 限制关键词数量和长度
            var keywords = key.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (keywords.Length > 5)
            {
                keywords = keywords.Take(5).ToArray();
            }
            key = string.Join(" ", keywords);
            if (key.Length > 20)
            {
                key = key.Substring(0, 20);
            }

            // URL解码和特殊字符处理
            key = HttpUtility.UrlDecode(key);
            return key.Replace("[", "［")
                     .Replace("]", "］")
                     .Replace("%", "％")
                     .Replace("_", "——");
        }

        private void SetupClassAndCondition(SearchParameters searchParams)
        {
            if (classid == "0")
            {
                SetupEmptyClass(searchParams);
            }
            else
            {
                SetupExistingClass(searchParams);
            }
        }

        private void SetupEmptyClass(SearchParameters searchParams)
        {
            condition = BuildBaseCondition();
            classVo.classid = 0L;
            classVo.position = "left";
            classVo.classname = searchParams.Type == SEARCH_TYPE_DAYS
                ? $"查询天数:{searchParams.DisplayValue}"
                : $"{searchParams.DisplayText}:{searchParams.DisplayValue}";
            classVo.siteimg = "NetImages/no.gif";
            classVo.introduce = "";
        }

        private void SetupExistingClass(SearchParameters searchParams)
        {
            condition = BuildBaseCondition();
            condition += " and book_date >= DATEADD(year, -5, GETDATE()) ";

            if (searchParams.Type == SEARCH_TYPE_DAYS)
            {
                classVo.classname = $"{classVo.classname}>查询天数:{searchParams.DisplayValue}";
            }
            else if (searchParams.Type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(searchParams.Pub))
            {
                classVo.classname = $"{classVo.classname}>查询用户:{searchParams.DisplayValue}";
            }
            else
            {
                classVo.classname = $"{classVo.classname}>{searchParams.DisplayText}:{searchParams.DisplayValue}";
            }
        }

        private void AddSearchConditions(SearchParameters searchParams)
        {
            if (!string.IsNullOrEmpty(searchParams.Key))
            {
                AddKeywordCondition(searchParams);
            }

            if (!string.IsNullOrEmpty(searchParams.Pub) && WapTool.IsNumeric(searchParams.Pub))
            {
                condition += $" and book_pub='{searchParams.Pub}'";
            }

            // 特殊用户搜索限制
            if (IsUser1000Search(searchParams) && userid != "1000")
            {
                condition += " and book_date >= DATEADD(year, -2, GETDATE())";
            }
        }

        private void AddKeywordCondition(SearchParameters searchParams)
        {
            switch (searchParams.Type)
            {
                case SEARCH_TYPE_TITLE:
                    AddTitleSearchCondition(searchParams.Key);
                    break;
                case SEARCH_TYPE_CONTENT:
                    AddContentSearchCondition(searchParams.Key);
                    break;
                case SEARCH_TYPE_AUTHOR:
                    condition += $" and book_author like '%{searchParams.Key}%' ";
                    break;
                case SEARCH_TYPE_DAYS:
                    AddDaysSearchCondition(searchParams.Key);
                    break;
                case SEARCH_TYPE_PUB:
                    if (WapTool.IsNumeric(searchParams.Key))
                    {
                        condition += $" and book_pub='{searchParams.Key}'";
                    }
                    break;
            }
        }

        private void ProcessSearch(SearchParameters searchParams)
        {
            pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
            var bll = new wap_bbs_BLL(a);

            HandlePaging(bll);
            UpdateUserBBSCount(searchParams);
            BuildSearchPageLinks(searchParams);

            // 获取搜索结果
            listVo = bll.GetListVo(pageSize, CurrentPage, condition,
                "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin",
                "id", total, 1);

            // 加载广告
            LoadAdvertisement();

            // 获取搜索结果后加载用户信息
            LoadUserInfo();

            // 记录搜索日志
            LogSearchActivity(searchParams);
        }

        private void UpdateUserBBSCount(SearchParameters searchParams)
        {
            if (CurrentPage == 1L && searchParams.Type == SEARCH_TYPE_PUB && WapTool.IsNumeric(searchParams.Key))
            {
                MainBll.UpdateSQL($"update [user] set bbsCount={total} where siteid={siteid} and userid={searchParams.Key}");
            }
        }

        private void BuildSearchPageLinks(SearchParameters searchParams)
        {
            var baseUrl = $"bbs/book_list_search.aspx?action=search&amp;siteid={siteid}&amp;classid={classid}&amp;type={searchParams.Type}&amp;key={HttpUtility.UrlEncode(searchParams.Key)}";
            if (!string.IsNullOrEmpty(searchParams.Pub))
            {
                baseUrl += $"&amp;pub={searchParams.Pub}";
            }
            BuildPageLinks(baseUrl);
        }

        private bool ShouldLogSearch(string searchKey)
        {
            var now = DateTime.Now;
            if (!lastSearchTime.ContainsKey(searchKey) || (now - lastSearchTime[searchKey]) > searchLogInterval)
            {
                lastSearchTime[searchKey] = now;
                return true;
            }
            return false;
        }

        private void LogSearchActivity(SearchParameters searchParams)
        {
            var (logMessage, searchKey) = GetSearchLogInfo(searchParams);
            if (ShouldLogSearch(searchKey))
            {
                VisiteCount(logMessage);
            }
        }

        private (string logMessage, string searchKey) GetSearchLogInfo(SearchParameters searchParams)
        {
            var currentClassName = classVo.classname.Split(':')[0];

            // 处理特殊情况
            if (!string.IsNullOrEmpty(searchParams.Pub) && searchParams.Type == SEARCH_TYPE_TITLE)
            {
                return ($"正在论坛查询{searchParams.Pub}的帖子:{searchParams.Key}",
                        $"title_{searchParams.Pub}_{searchParams.Key}");
            }

            if (searchParams.Type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(searchParams.Pub))
            {
                string userValue = !string.IsNullOrEmpty(searchParams.Pub) ? searchParams.Pub : searchParams.Key;
                return ($"正在论坛查询用户:{userValue}",
                        $"user_{userValue}");
            }

            // 使用传统的 switch 语句替代 switch expression
            string logMessage;
            string searchKey;

            switch (searchParams.Type)
            {
                case SEARCH_TYPE_TITLE:
                    logMessage = $"正在论坛查询标题:{searchParams.Key}";
                    searchKey = $"title_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_CONTENT:
                    logMessage = $"正在论坛查询内容:{searchParams.Key}";
                    searchKey = $"content_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_AUTHOR:
                    logMessage = $"正在论坛查询作者:{searchParams.Key}";
                    searchKey = $"author_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_DAYS:
                    logMessage = $"正在查看{currentClassName}最近{searchParams.Key}天的帖子";
                    searchKey = $"days_{searchParams.Key}";
                    break;
                default:
                    logMessage = $"正在论坛查询关键字:{searchParams.Key}";
                    searchKey = $"keyword_{searchParams.Key}";
                    break;
            }

            return (logMessage, searchKey);
        }

        private bool IsUser1000Search(SearchParameters searchParams)
        {
            return (searchParams.Type == SEARCH_TYPE_PUB && searchParams.Key == "1000") ||
                   (!string.IsNullOrEmpty(searchParams.Pub) && searchParams.Pub == "1000");
        }

        private string GetDisplayText(string type, string pub)
        {
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
                return "查询用户";
            if (type == SEARCH_TYPE_TITLE)
                return "查询标题";
            return "查询内容";
        }

        private string GetDisplayValue(string key, string pub, string type)
        {
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
                return !string.IsNullOrEmpty(pub) ? pub : key;
            return key;
        }

        private string EscapeForContains(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            // 先转义双引号，再转义单引号，确保 CONTAINS 谓词安全
            return input.Replace("\"", "\"\"").Replace("'", "''");
        }

        private void AddTitleSearchCondition(string key)
        {
            // 分割关键词，支持多关键词搜索（空格分隔，含全角空格）
            var keywords = key.Split(new[] { ' ', '　' }, StringSplitOptions.RemoveEmptyEntries);

            // 支持2到5个关键词的全文索引搜索
            if (keywords.Length >= 2 && keywords.Length <= 5 && EnableMultiKeywordSearch)
            {
                // 构建 N'"keyword1" AND "keyword2" AND ...' 形式的表达式
                string containsTerms = string.Join(" AND ", keywords.Select(k => "\"" + EscapeForContains(k) + "\""));
                condition += " and id IN (SELECT id FROM dbo.wap_bbs WHERE ischeck = 0 AND CONTAINS(book_title, N'" + containsTerms + "')) ";
            }
            else
            {
                // 单关键词或超出5个关键词（或未启用多关键词搜索）时，恢复LIKE模糊匹配
                condition += " and book_title like N'%" + key.Replace("'", "''") + "%' ";
            }
        }

        private void AddContentSearchCondition(string key)
        {
            var keywords = key.Split(new[] { ' ', '　' }, StringSplitOptions.RemoveEmptyEntries);

            // 支持2到5个关键词的全文索引搜索
            if (keywords.Length >= 2 && keywords.Length <= 5 && EnableMultiKeywordSearch)
            {
                string containsTerms = string.Join(" AND ", keywords.Select(k => "\"" + EscapeForContains(k) + "\""));
                condition += " and id IN (SELECT id FROM dbo.wap_bbs WHERE ischeck = 0 AND CONTAINS(book_content, N'" + containsTerms + "')) ";
            }
            else
            {
                // 单关键词或超出5个关键词（或未启用多关键词搜索）时，使用单关键词的CONTAINS（内容搜索倾向于精确）
                string singleKeywordForContent = keywords.Length > 0 ? keywords.First() : key; // Use first keyword or original key
                condition += " and id IN (SELECT id FROM dbo.wap_bbs WHERE ischeck = 0 AND CONTAINS(book_content, N'\"" + EscapeForContains(singleKeywordForContent) + "\"')) ";
            }
        }

        private void AddDaysSearchCondition(string key)
        {
            if (!WapTool.IsNumeric(key))
            {
                key = "0";
            }
            condition += $" and (DATEDIFF(dd, book_date, GETDATE()) < {key}) ";
        }
    }
}