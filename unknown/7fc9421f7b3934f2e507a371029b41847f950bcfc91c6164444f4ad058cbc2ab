'use strict';

// SettingsManager模块 - 统一管理设置页面的所有功能
var SettingsManager = (function () {
    // 私有配置常量
    var CONFIG = {
        MIN_SUBMIT_INTERVAL: 3000, // 3秒，与服务器端保持一致
        BUTTON_RESET_DELAY: 3000,
        AJAX_TIMEOUT: 10000
    };

    // 私有状态变量
    var _state = {
        lastSubmitTime: 0,
        statusTimeout: null,
        initialSettings: {},
        initialSettingsForIframe: {} // 用于iframe模式下记录所有相关设置的初始状态
    };

    // 缓存的DOM元素
    var _elements = {};

    // 私有资源：SVG图标
    var _icons = {
        save: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="18" height="18" style="vertical-align: text-bottom; margin-right: 5px;"><path d="M4 3H18L20.7071 5.70711C20.8946 5.89464 21 6.149 21 6.41421V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3ZM7 4V9H16V4H7ZM6 12V19H18V12H6ZM13 5H15V8H13V5Z"></path></svg>',
        timer: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="18" height="18" style="vertical-align: text-bottom; margin-right: 5px;"><path d="M6.38231 5.9681C7.92199 4.73647 9.87499 4 12 4C14.125 4 16.078 4.73647 17.6177 5.9681L19.0711 4.51472L20.4853 5.92893L19.0319 7.38231C20.2635 8.92199 21 10.875 21 13C21 17.9706 16.9706 22 12 22C7.02944 22 3 17.9706 3 13C3 10.875 3.73647 8.92199 4.9681 7.38231L3.51472 5.92893L4.92893 4.51472L6.38231 5.9681ZM12 20C15.866 20 19 16.866 19 13C19 9.13401 15.866 6 12 6C8.13401 6 5 9.13401 5 13C5 16.866 8.13401 20 12 20ZM13 12H16L11 18.5V14H8L13 7.4952V12ZM8 1H16V3H8V1Z"></path></svg>',
        success: '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 24 24" style="vertical-align: text-bottom; margin-right: 0px;"><path d="M4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12ZM12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2ZM17.4571 9.45711L16.0429 8.04289L11 13.0858L8.20711 10.2929L6.79289 11.7071L11 15.9142L17.4571 9.45711Z"></path></svg>'
    };

    //=====================================================
    // 私有工具方法
    //=====================================================

    /**
     * 缓存常用DOM元素
     */
    function _cacheElements() {
        _elements.saveBtn = document.getElementById('saveSettingsBtn');
        _elements.cb1 = document.getElementById('cb1');
        _elements.cbHideUselessReplies = document.getElementById('cbHideUselessReplies');
        _elements.toggleContainer = document.querySelector('.toggle-container');
    }

    /**
     * 设置按钮状态
     * @param {HTMLElement} button 按钮元素
     * @param {string} state 状态类型 (default, saving, success, error)
     * @param {string} [text] 可选的状态文本
     */
    function _setButtonState(button, state, text) {
        if (!button) return;

        // 确保按钮有原始文本属性
        if (!button.getAttribute('data-original-text')) {
            button.setAttribute('data-original-text', _icons.save + '保存设置');
        }

        // 重置所有可能的类
        button.classList.remove('saving', 'btn-success', 'btn-error');

        // 设置按钮是否禁用
        button.disabled = (state === 'saving');

        // 根据状态应用相应的类和文本
        switch (state) {
            case 'saving':
                button.classList.add('saving');
                button.innerHTML = _icons.timer + (text || '正在保存');
                break;
            case 'success':
                button.classList.add('btn-success');
                button.innerHTML = text || (_icons.success + ' 设置成功');
                break;
            case 'error':
                button.classList.add('btn-error');
                button.innerHTML = text || '保存失败';
                break;
            default: // 'default'
                button.innerHTML = button.getAttribute('data-original-text');
                break;
        }
    }

    /**
     * 简化消息用于按钮显示
     * @param {string} message 原始消息
     * @param {string} type 消息类型 (success, error, info)
     * @returns {string} 简化后的消息
     */
    function _simplifyMessage(message, type) {
        if (type === 'success') {
            if (message === '设置已保存') return '';
        } else if (type === 'error') {
            if (message.includes('请先登录')) return '请先登录';
            if (message.includes('找不到设置元素')) return '找不到设置';
            if (message.includes('请求过于频繁') || message.includes('请求频繁')) return '请求频繁，请稍候';
            if (message.includes('表单已过期')) return '请刷新重试';
            if (message.includes('保存出错') || message.includes('保存失败')) {
                var coreMessage = message.replace('保存出错: ', '').replace('保存失败: ', '');
                return coreMessage.length > 10 ? coreMessage.substring(0, 8) + '...' : coreMessage;
            }
        }
        return message.length > 15 ? message.substring(0, 12) + '...' : message;
    }

    /**
     * 确保按钮状态重置
     */
    function _ensureButtonReset() {
        if (_elements.saveBtn && (_elements.saveBtn.classList.contains('saving') || _elements.saveBtn.disabled)) {
            _setButtonState(_elements.saveBtn, 'default');
        }
    }

    /**
     * 收集当前所有设置状态
     * @returns {Object} 当前所有设置的快照
     */
    function _collectCurrentSettings() {
        var fontChecked = document.querySelector('input[name="font"]:checked');
        var medalChecked = document.querySelector('input[name="medalDisplay"]:checked');

        return {
            newReplyUI: _elements.cb1 ? _elements.cb1.checked : true,
            hideUselessReplies: _elements.cbHideUselessReplies ? _elements.cbHideUselessReplies.checked : false,
            font: fontChecked ? fontChecked.id : null,
            medalDisplay: medalChecked ? medalChecked.id : null
        };
    }

    /**
     * 初始化保存按钮
     */
    function _initializeSaveButton() {
        if (_elements.saveBtn) {
            var defaultText = _icons.save + '保存设置';
            _elements.saveBtn.innerHTML = defaultText;
            _elements.saveBtn.setAttribute('data-original-text', defaultText);
        }
    }

    /**
     * 读取cookie
     * @param {string} name cookie名称
     * @returns {string|undefined} cookie值或undefined
     */
    function _getCookie(name) {
        let matches = document.cookie.match(new RegExp(
            "(?:^|; )" + name.replace(/([.$?*|{}()\[\]\\\/\+^])/g, '\\$1') + "=([^;]*)"
        ));
        return matches ? decodeURIComponent(matches[1]) : undefined;
    }

    /**
     * 设置cookie
     * @param {string} name cookie名称
     * @param {string} value cookie值
     * @param {number} days 过期天数
     */
    function _setCookie(name, value, days) {
        let expires = "";
        if (days) {
            let date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + encodeURIComponent(String(value)) + expires + "; path=/";
    }

    /**
     * 检查是否为iframe模式
     * @returns {boolean} 是否为iframe模式
     */
    function _isIframeMode() {
        return new URLSearchParams(window.location.search).has('iframe');
    }

    /**
     * 获取设置页面URL
     * @returns {string} 设置页面的URL
     */
    function _getSettingsUrl() {
        return "/BBS/Settings.aspx";
    }

    /**
     * 将对象转换为URL编码的表单数据
     * @param {Object} data 要转换的数据对象
     * @returns {string} URL编码的字符串
     */
    function _objectToFormData(data) {
        return Object.keys(data)
            .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))
            .join('&');
    }

    /**
     * 检查响应的内容类型是否为HTML
     * @param {Response} response Fetch API返回的响应对象
     * @returns {boolean} 是否为HTML响应
     */
    function _isHtmlResponse(response) {
        const contentType = response.headers.get('content-type');
        return contentType && contentType.includes('text/html');
    }

    //=====================================================
    // 核心功能方法
    //=====================================================

    /**
     * 显示状态消息
     * @param {string} message 状态消息内容
     * @param {string} type 消息类型 (success, error, info)
     */
    function _showStatus(message, type) {
        if (!_elements.saveBtn) {
            alert(message); // 找不到按钮则回退到alert
            return;
        }

        var simplifiedMessage = _simplifyMessage(message, type);
        _setButtonState(_elements.saveBtn, type, simplifiedMessage);

        if (_state.statusTimeout) {
            clearTimeout(_state.statusTimeout);
        }

        _state.statusTimeout = setTimeout(function () {
            _state.statusTimeout = null;
            _setButtonState(_elements.saveBtn, 'default');
        }, CONFIG.BUTTON_RESET_DELAY);
    }

    /**
     * 使用Fetch API发送AJAX请求
     * @param {Object} options 请求选项
     * @returns {Promise} 请求Promise
     */
    function _sendFetchRequest(url, data) {
        return new Promise((resolve, reject) => {
            // 创建AbortController用于超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), CONFIG.AJAX_TIMEOUT);

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: _objectToFormData(data),
                credentials: 'include',
                signal: controller.signal
            })
                .then(response => {
                    clearTimeout(timeoutId);

                    // 检查是否为HTML响应（可能是错误页面或登录重定向）
                    if (_isHtmlResponse(response)) {
                        // 返回特殊对象表示HTML响应
                        if (response.status === 401 || response.status === 403) {
                            throw new Error('请先登录');
                        }
                        throw new Error('服务器返回了HTML而非预期的JSON');
                    }

                    // 检查响应状态
                    if (!response.ok) {
                        throw new Error(`HTTP错误：${response.status}`);
                    }

                    // 解析JSON响应
                    return response.json();
                })
                .then(resolve)
                .catch(error => {
                    clearTimeout(timeoutId);
                    if (error.name === 'AbortError') {
                        reject(new Error('请求超时'));
                    } else {
                        reject(error);
                    }
                });
        });
    }

    /**
     * 保存设置的核心方法
     * @param {Object} options 选项
     * @returns {boolean} 是否发送了请求
     */
    function _performSaveRequest(options) {
        options = options || {};
        options.beforeSend = options.beforeSend || function () { };
        options.onSuccess = options.onSuccess || function () { };
        options.onError = options.onError || function () { };

        // 执行前置回调
        options.beforeSend();

        // 处理不需要AJAX的情况
        function handleNoAjaxRequest(message, type) {
            setTimeout(function () {
                _showStatus(type === 'success' ? '设置已保存' : message, type);
            }, 400);
            return false;
        }

        // 前置验证
        if (typeof currentUserId === 'undefined' || currentUserId <= 0) {
            return handleNoAjaxRequest("用户未登录，无法保存设置", 'error');
        }

        if (!_elements.cb1) {
            return handleNoAjaxRequest("找不到设置元素", 'error');
        }

        // 频率限制
        var now = new Date().getTime();
        if (now - _state.lastSubmitTime < CONFIG.MIN_SUBMIT_INTERVAL) {
            _showStatus('请求频繁，请稍候', 'error');
            return false;
        }

        // 检查设置是否有变化
        if (typeof serverSideNewReplyUIEnabled !== 'undefined' &&
            serverSideNewReplyUIEnabled === _elements.cb1.checked) {
            _showStatus('设置已保存', 'success');
            options.onSuccess({ d: "no_change_but_success" });
            return false;
        }

        // 更新请求时间
        _state.lastSubmitTime = now;

        // 构建数据
        var data = {
            action: "saveSettings",
            newReplyUIEnabled: _elements.cb1.checked
        };

        // 发送请求
        _sendFetchRequest(_getSettingsUrl(), data)
            .then(response => {
                if (response && response.d === "success") {
                    serverSideNewReplyUIEnabled = _elements.cb1.checked;
                    _showStatus('设置已保存', 'success');
                    options.onSuccess(response);
                } else {
                    var serverMessage = (response && response.d) ? response.d : "未知服务器响应";
                    _showStatus('保存失败: ' + serverMessage, 'error');
                    options.onError(null, 'server_logic_error', serverMessage);
                }
            })
            .catch(error => {
                var errorMessage = '保存出错';

                if (error.message.includes('超时') || error.message.includes('timeout')) {
                    errorMessage += ': 请求超时';
                } else if (error.message.includes('登录')) {
                    errorMessage = '请先登录';
                } else {
                    errorMessage += ': ' + error.message;
                }

                options.onError(null, 'ajax_error', error.message);
                _showStatus(errorMessage, 'error');
            })
            .finally(() => {
                setTimeout(_ensureButtonReset, CONFIG.BUTTON_RESET_DELAY + 100);
            });

        return true;
    }

    //=====================================================
    // iframe模式方法
    //=====================================================

    /**
     * 初始化iframe模式
     */
    function _initIframeMode() {
        document.body.classList.add('iframe-mode');

        if (_elements.toggleContainer) {
            _elements.toggleContainer.style.display = 'block';

            // 添加标题
            var title = document.createElement('div');
            title.className = 'title';
            title.textContent = '论坛设置';
            _elements.toggleContainer.insertBefore(title, _elements.toggleContainer.firstChild);
        }

        _initSettingStates();
        _setupIframeHeightAdjustment();

        if (_elements.saveBtn) {
            _initializeSaveButton();
            _elements.saveBtn.addEventListener('click', _saveSettingsForIframe);
        }
    }

    /**
     * 设置iframe高度调整
     */
    function _setupIframeHeightAdjustment() {
        try {
            var heightSourceElement = document.querySelector('.settings-popup-styled-container') || document.body;

            if (!heightSourceElement || !window.parent || window.parent === window) {
                return;
            }

            var currentHeight = heightSourceElement.scrollHeight;
            if (currentHeight < 100 && document.body.scrollHeight > currentHeight) {
                currentHeight = document.body.scrollHeight;
            }

            window.parent.postMessage({
                type: 'resize',
                height: currentHeight
            }, '*');
        } catch (e) {
            // 静默失败
        }
    }

    /**
     * iframe模式下的保存设置方法
     */
    function _saveSettingsForIframe() {
        return _performSaveRequest({
            beforeSend: function () {
                _setButtonState(_elements.saveBtn, 'saving');
            },
            onSuccess: function (response) {
                if (_elements.saveBtn) {
                    _elements.saveBtn.disabled = false;
                }

                try {
                    var currentSettingsSnapshot = _collectCurrentSettings();
                    var overallChangeFromInitialLoad = JSON.stringify(_state.initialSettingsForIframe) !== JSON.stringify(currentSettingsSnapshot);

                    // 关键通信点：通知父窗口设置已变更
                    window.parent.postMessage({
                        type: 'settingsChanged',
                        changed: true,
                        doNotRefresh: !overallChangeFromInitialLoad
                    }, '*');
                } catch (e) {
                    // 静默失败
                }
            },
            onError: function () {
                if (_elements.saveBtn) {
                    _elements.saveBtn.disabled = false;
                }
            }
        });
    }

    //=====================================================
    // 常规模式方法
    //=====================================================

    /**
     * 初始化常规模式
     */
    function _initNormalMode() {
        _initSettingStates();
        _setupSettingsChangeTracking();

        if (_elements.saveBtn) {
            _initializeSaveButton();

            if (typeof currentUserId === 'undefined') {
                _showStatus('无法保存设置：无法获取用户信息', 'error');
                return;
            }

            _elements.saveBtn.addEventListener('click', _saveSettingsForNormal);
        }

        window.addEventListener('beforeunload', function () {
            if (_hasSettingsChanged()) {
                sessionStorage.setItem('settingsChanged', 'true');
            }
            _ensureButtonReset();
        });
    }

    /**
     * 常规模式的保存设置方法
     */
    function _saveSettingsForNormal() {
        return _performSaveRequest({
            beforeSend: function () {
                _setButtonState(_elements.saveBtn, 'saving');
            },
            onSuccess: function (response) {
                if (_elements.saveBtn) {
                    _elements.saveBtn.disabled = false;
                }

                if (_elements.cb1) {
                    _state.initialSettings.newReplyUI = _elements.cb1.checked;
                }
            },
            onError: function () {
                if (_elements.saveBtn) {
                    _elements.saveBtn.disabled = false;
                }
            }
        });
    }

    /**
     * 设置变更追踪
     */
    function _setupSettingsChangeTracking() {
        _state.initialSettings = _collectCurrentSettings();

        // 全局API用于检查设置变化
        window.hasSettingsChanged = _hasSettingsChanged;

        // 如果在iframe中，注册回调
        if (window.parent !== window) {
            window.parent.postMessage({
                action: 'registerSettingsCallback',
                callback: 'hasSettingsChanged'
            }, '*');
        }
    }

    /**
     * 检查设置是否有变化
     * @returns {boolean} 设置是否有变化
     */
    function _hasSettingsChanged() {
        var currentSettings = _collectCurrentSettings();
        return JSON.stringify(_state.initialSettings) !== JSON.stringify(currentSettings);
    }

    //=====================================================
    // 共享设置方法
    //=====================================================

    /**
     * 初始化各种设置状态
     */
    function _initSettingStates() {
        // 新版回帖设置
        if (_elements.cb1) {
            if (typeof serverSideNewReplyUIEnabled !== 'undefined') {
                _elements.cb1.checked = serverSideNewReplyUIEnabled;
            } else {
                var newReplyUI = _getCookie('NewReplyUI');
                _elements.cb1.checked = (newReplyUI === undefined || newReplyUI === '1');
            }

            _elements.cb1.addEventListener('change', function () {
                _setCookie('NewReplyUI', _elements.cb1.checked ? '1' : '0', 365);
            });
        }

        // 隐藏吃肉设置
        if (_elements.cbHideUselessReplies) {
            var val = _getCookie('hideUseless');
            _elements.cbHideUselessReplies.checked = (val === '1');

            _elements.cbHideUselessReplies.addEventListener('change', function () {
                _setCookie('hideUseless', _elements.cbHideUselessReplies.checked ? '1' : '0', 365);
            });
        }

        // 字体切换设置
        var fontSwitched = localStorage.getItem('bodyfontswitched');
        var songRadio = document.getElementById('song');
        var otherRadio = document.getElementById('other');

        if (songRadio && otherRadio) {
            if (!fontSwitched || fontSwitched !== 'true') {
                songRadio.checked = true;
            } else {
                otherRadio.checked = true;
            }

            document.querySelectorAll('input[name="font"]').forEach(function (input) {
                input.addEventListener('change', function () {
                    localStorage.setItem('bodyfontswitched', this.id === 'other' ? 'true' : 'false');
                });
            });
        }

        // 勋章显示设置
        var shadowSwitched = localStorage.getItem('hideMedalImages');
        var medalAllRadio = document.getElementById('medalAll');
        var medalPartialRadio = document.getElementById('medalPartial');

        if (medalAllRadio && medalPartialRadio) {
            if (!shadowSwitched || shadowSwitched !== 'true') {
                medalAllRadio.checked = true;
            } else {
                medalPartialRadio.checked = true;
            }

            document.querySelectorAll('input[name="medalDisplay"]').forEach(function (input) {
                input.addEventListener('change', function () {
                    if (this.id === 'medalPartial') {
                        localStorage.setItem('hideMedalImages', 'true');
                        localStorage.setItem('hidebbsqianming', 'true');
                    } else {
                        localStorage.removeItem('hideMedalImages');
                        localStorage.removeItem('hidebbsqianming');
                    }
                });
            });
        }

        // iframe模式下记录初始状态
        if (_isIframeMode()) {
            _state.initialSettingsForIframe = _collectCurrentSettings();
        }
    }

    //=====================================================
    // 公开API
    //=====================================================

    // 模块初始化
    function _init() {
        _cacheElements();

        if (_elements.saveBtn) {
            _initializeSaveButton();
        }

        if (_isIframeMode()) {
            _initIframeMode();
        } else {
            _initNormalMode();
        }
    }

    // 在所有资源加载完成后调整高度
    function _setupLoadHandler() {
        window.addEventListener('load', function () {
            if (_isIframeMode()) {
                setTimeout(_setupIframeHeightAdjustment, 500);
            }
        });
    }

    // 返回公开接口
    return {
        // 初始化方法
        init: function () {
            document.addEventListener('DOMContentLoaded', _init);
            _setupLoadHandler();
        }
    };
})();

// 启动应用
SettingsManager.init();