---
description: 
globs: *.cs,*.hbs
alwaysApply: false
---
# Handlebars.NET 集成开发规则

## 使用场景
当需要在 ASP.NET Web Forms 项目中集成 Handlebars.NET 进行 UI 现代化时使用此规则。适用于页面适配、模板开发、Helper 扩展等场景。

## 核心规则

### 必须遵循 (MUST)
1. **使用全局唯一 TemplateService**：所有 Handlebars 操作必须通过 `TemplateService.RenderPageWithLayout()` 方法
2. **正确处理 ThreadAbortException**：在 `TryRenderWithHandlebars()` 和 `RenderWithHandlebars()` 中正确处理，避免使用 `Thread.ResetAbort()`
3. **统一页面适配模式**：按照标准模式实现 `CheckAndHandleUIPreference()` → `TryRenderWithHandlebars()` → `RenderWithHandlebars()` 流程
4. **安全的数据库操作**：所有 SQL 操作必须使用参数化查询，严禁字符串拼接
5. **统一命名空间**：HeaderOptionsModel 必须使用 `YaoHuo.Plugin.BBS.Models` 命名空间
6. **文件路径一致性**：代码中的模板路径与实际 `.hbs` 文件名必须完全匹配
7. **Tailwind 构建路径**：执行 Tailwind CSS 构建命令时，确保输入文件 (`-i`) 指向 `./build-tools/style.css`，输出文件 (`-o`) 指向 `../Template/CSS/output.css` (相对于 `build-tools` 目录)。
8. **同步更新 DOM 选择器**：在修改 Handlebars 模板（.hbs 文件）中的 HTML 结构或 CSS 类名时，必须同步检查并更新所有相关的 JavaScript 代码中用于查找这些元素的 DOM 选择器，确保选择器（如 `.closest()`, `querySelector()`, `querySelectorAll()` 中的参数）与新的结构和类名一致。
9. **避免 CSS 类名冲突**：自定义 CSS 类名时，避免使用与浏览器默认样式（如 `list-item`）冲突的名称。

### 应该遵循 (SHOULD)
1. **错误恢复机制**：新版渲染失败时应优雅回退到旧版 UI
2. **分层错误处理**：在不同层次捕获和处理异常，提供详细日志
3. **模板缓存利用**：依赖 TemplateService 内置的模板缓存机制
4. **项目文件管理**：新创建的 `.hbs`、`.css` 文件应添加到 `.csproj` 项目文件中
5. **前端JS状态同步**：处理异步DOM更新时，确保JavaScript状态和事件管理同步，避免因DOM替换导致JS功能失效。

### 禁止操作 (MUST NOT)
1. **创建新的 IHandlebars 实例**：禁止在页面代码中直接创建 Handlebars 实例
2. **使用 Thread.ResetAbort()**：会导致新旧内容拼接问题
3. **SQL 字符串拼接**：存在注入风险，必须使用参数化查询
4. **忽略 ThreadAbortException**：这是 Response.End() 的正常行为，不应作为错误处理
5. **Tailwind 构建路径错误**：禁止在构建命令中使用错误的输入/输出文件路径。
6. **不必要的构建操作**：避免执行如更新 browserslist 数据库等不必要的构建步骤。

## 标准代码模式

### 页面适配标准结构
遵循 CheckAndHandleUIPreference -> TryRenderWithHandlebars -> RenderWithHandlebars 流程，详情见参考文档。

### 正确的 ThreadAbortException 处理
正确处理 Response.End() 引起的 ThreadAbortException，详情见参考文档。

### 安全的数据库操作
所有SQL操作必须参数化，严禁拼接，详情见参考文档。

## 文件组织规范

### 目录结构
```
Template/
├── Layouts/MainLayout.hbs
├── Pages/PageName.hbs
├── Partials/ComponentName.hbs
└── CSS/output.css
```

**前端构建文件 (build-tools 目录)**
```
build-tools/
├── package.json
├── package-lock.json
├── postcss.config.js
├── tailwind.config.js
├── style.css
└── node_modules/
```

### 命名映射
页面类名 -> 模板文件 -> 数据模型，特殊情况需代码指定，详情见参考文档。

## 调试检查清单
开发时必须验证：
- [ ] 反射类型名称是否正确
- [ ] SQL 查询是否使用参数化
- [ ] ThreadAbortException 是否正确处理
- [ ] 模板文件路径是否与代码引用匹配
- [ ] 新文件是否添加到项目 (.csproj) 中
- [ ] 分页解析逻辑是否正常工作

## 参考文档
详细的技术实现、最佳实践、问题解决方案请参考：
**`.cursor/rules/handlebars-integration-reference.md`**

该文档包含完整的：
- TemplateService 架构设计
- Helper 系统使用
- 数据模型设计模式  
- 性能优化策略
- 实战问题解决方案
- 部署维护指南

## 10. 常见问题与解决方案

### 10.1 ThreadAbortException 处理
正确区分 Response.End() 和异常，详情见参考文档。

### 10.2 Helper 注册失效
确保使用全局唯一的 IHandlebars 实例，详情见参考文档。

### 10.3 内容拼接问题
使用 bool 返回值控制执行流程，详情见参考文档。

### 10.4 CSS 样式冲突
使用 CSS 作用域和命名空间，详情见参考文档。

### 10.5 CSS 类名冲突问题
避免与浏览器默认样式冲突的类名，必要时重命名，详情见参考文档。

### 10.6 图片加载失败处理问题
完善JS错误处理，强制重绘，详情见参考文档。

### 10.7 下拉菜单定位和样式问题
调整CSS规则和元素样式，详情见参考文档。

### 10.8 Handlebars Helper 解析失败问题（嵌套 Helper 或老版本兼容性）
将判断逻辑移至C#模型计算属性，避免复杂Helper嵌套，详情见参考文档。

### 10.9 Handlebars.Net 版本问题
考虑升级或优先基础语法，详情见参考文档。