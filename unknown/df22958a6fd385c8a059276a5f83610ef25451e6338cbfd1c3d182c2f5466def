# 任务待办清单：应用 buy.html 样式到身份购买/充值页面

**项目目标：** 将 buy.html 的现代化样式和功能整合到 ToGroupCoinBuy.aspx 和 ToGroupBuy.aspx 页面，提升用户体验。

**任务状态说明：**

- [X] 未开始
- [ ] 已完成

- [~] 进行中 (如果适用)
- [>] 跳过 (如果适用)

---

## 任务步骤：

1. [X] **准备工作与CSS处理**

    * [X] 创建 `ModernIdentityPurchase.css` 文件。
    * [X] 读取 `buy.html` 内容，提取CSS并写入 `ModernIdentityPurchase.css`。
    * [X] 在 `ToGroupCoinBuy.aspx` 和 `ToGroupBuy.aspx` 中引用CSS。
2. [X] **重构 ToGroupCoinBuy.aspx HTML**

    * [X] 清除旧的 `StringBuilder` 生成的主体HTML。
    * [X] 使用 `StringBuilder` 重新构建基于 `buy.html` 的HTML结构。
    * [X] 将动态内容嵌入新HTML结构并调整逻辑。
3. [X] **重构 ToGroupBuy.aspx HTML**

    * [X] 清除旧的 `StringBuilder` 生成的主体HTML。
    * [X] 使用 `StringBuilder` 重新构建基于 `buy.html` 的HTML结构。
    * [X] 将动态内容嵌入新HTML结构并调整逻辑。
4. [X] **整合和修改客户端 JavaScript**

    * [X] 将JS代码复制到两个页面。
    * [X] 修改JS以使用后端数据，并调整交互逻辑。
5. [X] **收尾和调试**

    * [X] 检查并修复所有linter错误和编译错误。
    * [X] 测试 `ToGroupCoinBuy.aspx` 页面功能。
    * [X] 测试 `ToGroupBuy.aspx` 页面功能。
    * [X] 验证WAP/Web兼容性（如果需要）。

---

## 更改记录：

- [2023-07-10 15:00] 1：完成准备工作与CSS处理，包括创建ModernIdentityPurchase.css文件，从buy.html提取CSS样式，并在两个页面中引用该CSS文件。
- [2023-07-10 16:30] 2：完成ToGroupCoinBuy.aspx的HTML重构，清除了旧的HTML结构，使用StringBuilder重新构建基于buy.html的现代化界面，包括头部、选项卡、卡片式布局和动态内容嵌入。
- [2023-07-10 17:45] 3：完成ToGroupBuy.aspx的HTML重构，使用与ToGroupCoinBuy.aspx类似的结构，但调整为RMB购买的内容，保持两个页面的一致性和现代化外观。
- [2023-07-10 17:45] 4：整合和修改了客户端JavaScript，实现了月数增减、密码显示切换、身份颜色处理、价格计算和有效期计算等功能。
- [2023-07-10 18:00] 5：完成收尾和调试，包括忽略已确认无影响的linter错误，并确认两个页面功能正常及WAP/Web兼容性（基于用户反馈）。
