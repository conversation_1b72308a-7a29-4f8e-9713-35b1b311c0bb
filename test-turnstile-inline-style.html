<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Turnstile 内联样式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        
        .test-btn {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        /* 模拟原生 Turnstile 样式 */
        .cf-turnstile {
            margin: 15px 0;
            display: flex;
            justify-content: center;
        }
        
        .mock-turnstile {
            width: 300px;
            height: 65px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        
        /* 模拟新的弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            align-items: center;
            justify-content: center;
        }
        
        .modal-overlay.show {
            display: flex;
            opacity: 1;
        }
        
        /* 新的 Turnstile 内联样式 */
        .modal-overlay.turnstile-inline {
            background-color: transparent;
            position: absolute;
            z-index: 1;
        }
        
        .modal-wrapper {
            position: relative;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }
        
        .modal-overlay.show .modal-wrapper {
            transform: scale(1);
        }
        
        .modal-turnstile {
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            max-width: 320px;
            width: auto;
        }
        
        .modal-body {
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #gocaptcha-wrap {
            margin: 15px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 4px;
            text-align: center;
            color: #666;
            min-height: 50px;
            position: relative;
        }
    </style>
</head>
<body>
    <h1>Turnstile 内联样式效果测试</h1>
    
    <div class="test-section">
        <h2>原生 Cloudflare Turnstile 验证码</h2>
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" placeholder="请输入密码">
                </div>
                
                <div class="cf-turnstile">
                    <div class="mock-turnstile">
                        🔒 原生 Cloudflare Turnstile<br>
                        <small>内联显示</small>
                    </div>
                </div>
                
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <div class="test-section">
        <h2>新的备用 Turnstile 验证码（模拟内联效果）</h2>
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" placeholder="请输入密码">
                </div>
                
                <div id="gocaptcha-wrap">
                    GoCaptcha 容器位置
                </div>
                
                <button type="button" class="test-btn" onclick="showInlineTurnstile()">显示内联样式 Turnstile</button>
                <button type="button" class="test-btn" onclick="hideInlineTurnstile()">隐藏</button>
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <div class="test-section">
        <h2>对比：修改前的弹窗样式</h2>
        <div class="form-container">
            <form>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" placeholder="请输入密码">
                </div>
                
                <button type="button" class="test-btn" onclick="showOldModal()">显示旧式弹窗</button>
                <button type="button" class="btn">登录</button>
            </form>
        </div>
    </div>
    
    <!-- 内联样式弹窗 -->
    <div id="inline-modal" class="modal-overlay">
        <div class="modal-wrapper">
            <div class="modal-turnstile">
                <div class="modal-body">
                    <div class="mock-turnstile">
                        🔒 备用 Turnstile（内联样式）<br>
                        <small>无背景、无标题</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 旧式弹窗 -->
    <div id="old-modal" class="modal-overlay">
        <div class="modal-wrapper">
            <div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0,0,0,0.4); max-width: 420px; width: 95%;">
                <div style="padding: 15px 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; background: #f8f9fa; border-radius: 12px 12px 0 0;">
                    <h3 style="margin: 0; font-size: 16px; color: #333;">备用验证码</h3>
                    <button onclick="hideOldModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>
                <div style="padding: 20px; text-align: center;">
                    <div class="mock-turnstile">
                        🔒 备用 Turnstile（旧样式）<br>
                        <small>有背景、有标题</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showInlineTurnstile() {
            const gocaptchaWrap = document.getElementById('gocaptcha-wrap');
            const modal = document.getElementById('inline-modal');
            
            // 获取原始容器位置
            const rect = gocaptchaWrap.getBoundingClientRect();
            
            // 设置弹窗位置和样式
            modal.classList.add('turnstile-inline');
            modal.style.position = 'absolute';
            modal.style.top = (rect.top + window.scrollY) + 'px';
            modal.style.left = rect.left + 'px';
            modal.style.width = rect.width + 'px';
            modal.style.height = 'auto';
            
            modal.classList.add('show');
        }
        
        function hideInlineTurnstile() {
            const modal = document.getElementById('inline-modal');
            modal.classList.remove('show');
            modal.classList.remove('turnstile-inline');
        }
        
        function showOldModal() {
            const modal = document.getElementById('old-modal');
            modal.classList.add('show');
        }
        
        function hideOldModal() {
            const modal = document.getElementById('old-modal');
            modal.classList.remove('show');
        }
        
        // 点击遮罩关闭弹窗
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
                event.target.classList.remove('show');
                event.target.classList.remove('turnstile-inline');
            }
        });
    </script>
</body>
</html>
