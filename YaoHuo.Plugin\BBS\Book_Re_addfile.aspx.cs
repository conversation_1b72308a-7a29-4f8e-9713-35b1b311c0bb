﻿using System;
using System.Collections.Generic;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class Book_Re_addfile : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckBBSreCount = PubConstant.GetAppString("KL_CheckBBSreCount");

        public string KL_NotDownAndUpload = PubConstant.GetAppString("KL_NotDownAndUpload");

        public wap_bbs_Model bbsVo = new wap_bbs_Model();

        public string action = "";

        public string lpage = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string book_title = "";

        public string book_content = "";

        public string face = "";

        public string stype = "";

        public string viewtype = "";

        public string viewmoney = "";

        public string reshow = "";

        private string string_11 = "";

        private string string_12 = "";

        public string sendmoney = "";

        public string[] facelist;

        public string[] facelistImg;

        public string[] stypelist;

        public bool isadmin = false;

        public long getid;

        public int num = 1;

        public string id = "";

        public wap_bbs_Model bookVo = null;

        public string addtext = "";

        public string getmoney = "0";

        public string getexpr = "0";

        public string needmoney = "0";

        public string needexpr = "0";

        public string contentmax = "2";

        public string getmoney2 = "";

        public bool isNeedSecret = false;

        public string orderby = "0";

        private readonly FileUploadHelper _fileUploadHelper;

        public Book_Re_addfile()
        {
            _fileUploadHelper = new FileUploadHelper(this);
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID对应非论坛模块，请联系站长处理。", "");
            }
            KL_NotDownAndUpload += WapTool.KL_NotDownAndUpload_SYS;
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");

            if (string.IsNullOrEmpty(id))
            {
                ShowTipInfo("参数错误：未指定帖子ID", "");
                return;
            }

            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 6)))
            {
                ShowTipInfo("回文件贴功能已关闭！【版务】→【更多栏目属性】中设置。", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            orderby = WapTool.GetArryString(classVo.smallimg, '|', 43);
            if (!WapTool.IsNumeric(orderby))
            {
                orderby = "0";
            }
            if (classVo.topicID != "" && classVo.topicID != "0" && IsCheckManagerLvl("|00|01|03|04|", ""))
            {
                isNeedSecret = true;
            }
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(string_10);
            bookVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bookVo.islock == 1L)
            {
                ShowTipInfo("此贴已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            else if (bookVo.islock == 2L)
            {
                ShowTipInfo("此贴已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            if (!string.IsNullOrEmpty(GetRequestValue("num")))
            {
                this.num = int.Parse(GetRequestValue("num"));
            }
            try
            {
                if (classVo.bbsFace.IndexOf('_') < 0)
                {
                    classVo.bbsFace = "_";
                }
                facelist = classVo.bbsFace.Split('_')[0].Split('|');
                facelistImg = classVo.bbsFace.Split('_')[1].Split('|');
            }
            catch (Exception)
            {
            }
            IsLogin(userid, "bbs/book_re_addfile.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;page=" + page);
            long num = Convert.ToInt64(WapTool.GetSiteDefault(siteVo.Version, 15));
            if (num > 0L)
            {
                long num2 = WapTool.DateDiff(DateTime.Now, userVo.RegTime, "MM");
                if (num2 < num)
                {
                    ShowTipInfo("请再过:" + (num - num2) + "分钟才能回贴！", "bbs/book_re.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                }
            }
            needmoney = WapTool.GetSiteDefault(siteVo.moneyregular, 5);
            needexpr = WapTool.GetSiteDefault(siteVo.lvlRegular, 5);
            if (!WapTool.IsNumeric(needmoney))
            {
                needmoney = "0";
            }
            if (!WapTool.IsNumeric(needexpr))
            {
                needexpr = "0";
            }
            if (userVo.money < long.Parse(needmoney))
            {
                ShowTipInfo("上传文件需要" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, lang) + "大于:" + needmoney, "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
            }
            if (userVo.expr < long.Parse(needexpr))
            {
                ShowTipInfo("上传文件需要经验大于:" + needexpr, "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
            }
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                book_content = GetRequestValue("book_content");
                face = GetRequestValue("face");
                sendmoney = GetRequestValue("sendmoney");

                string[] values = base.Request.Form.GetValues("book_file_info");

                var uploadResult = _fileUploadHelper.ProcessFileUpload(
                    Request.Files,
                    values,
                    siteid,
                    userid,
                    "论坛回复",
                    siteVo.UpFileType,
                    KL_NotDownAndUpload,
                    siteVo.myspace.ToString(),
                    siteVo.sitespace.ToString(),
                    siteVo.MaxFileSize.ToString(),
                    siteVo.isCheck.ToString(),
                    string_11,  // book_width
                    string_12,  // book_height
                    WapTool.GetSiteDefault(siteVo.Version, 4),  // addWatermark
                    WapTool.GetSiteDefault(siteVo.Version, 12), // watermarkPath
                    WapTool.GetSiteDefault(siteVo.Version, 13), // watermarkFontSize
                    WapTool.GetSiteDefault(siteVo.Version, 16)  // watermarkText
                );

                if (!string.IsNullOrEmpty(uploadResult.INFO))
                {
                    INFO = uploadResult.INFO;
                    return;
                }

                List<wap2_attachment_Model> list2 = uploadResult.Attachments;

                string text = WapTool.GetArryString(siteVo.Version, '|', 22);
                if (!WapTool.IsNumeric(sendmoney))
                {
                    sendmoney = "0";
                }
                if (!WapTool.IsNumeric(text))
                {
                    text = "0";
                }
                if (long.Parse(text) < 2L)
                {
                    text = "1000";
                }
                if (long.Parse(sendmoney) > long.Parse(text))
                {
                    sendmoney = text;
                }

                if (userVo.money < long.Parse(sendmoney))
                {
                    INFO = "SENDMONEY";
                    return;
                }
                if (getmoney2.IndexOf('-') == 0 && userVo.money + long.Parse(getmoney2) < 0L)
                {
                    INFO = "NOMONEY";
                    return;
                }

                Session["content"] = book_content;
                face = face.Replace("表情", "");
                if (face.Trim() != "" && face.EndsWith(".gif"))
                {
                    book_content = "[img]face/" + face + "[/img]" + book_content;
                }

                wap_bbsre_Model wap_bbsre_Model = new wap_bbsre_Model();
                wap_bbsre_Model.devid = siteid;
                wap_bbsre_Model.userid = long.Parse(userid);
                wap_bbsre_Model.nickname = nickname;
                wap_bbsre_Model.classid = long.Parse(classid);
                wap_bbsre_Model.bookid = long.Parse(id);
                wap_bbsre_Model.content = book_content;
                wap_bbsre_Model.redate = DateTime.Now;
                wap_bbsre_Model.ischeck = siteVo.isCheck;
                wap_bbsre_Model.isdown = list2.Count;

                wap_bbsre_BLL wap_bbsre_BLL = new wap_bbsre_BLL(string_10);
                getid = wap_bbsre_BLL.Add(wap_bbsre_Model);

                if (bookVo.isdown != 2L && !(orderby == "1") && !(orderby == "2"))
                {
                    MainBll.UpdateSQL($"update [wap_bbs] set reDate='{DateTime.Now}' where id={id}");
                }

                if (bookVo.book_re == 0L)
                {
                    MainBll.UpdateSQL($"insert into wap_message(siteid,userid,nickname,title,content,touserid,issystem)values({siteid},{userid},'{nickname}','{nickname}第一个文件回复了你的贴子','文件回复时间：{DateTime.Now}，<a href=\"{http_start}bbs/book_view.aspx?id={bookVo.id}&amp;siteid={siteid}&amp;classid={bookVo.book_classid}\">查看</a>',{bookVo.book_pub},1)");
                }

                wap_bbs_BLL wap_bbs_BLL2 = new wap_bbs_BLL(string_10);
                wap_bbs_BLL2.UpdateXiNuHan(siteid, id, "+1");

                wap2_attachment_BLL wap2_attachment_BLL = new wap2_attachment_BLL(string_10);
                for (int j = 0; j < list2.Count; j++)
                {
                    list2[j].siteid = long.Parse(siteid);
                    list2[j].userid = long.Parse(userid);
                    list2[j].book_id = getid;
                    list2[j].book_type = "bbsre";
                    wap2_attachment_BLL.Add(list2[j]);
                }

                if (list2.Count > 0)
                {
                    getmoney = WapTool.GetSiteDefault(siteVo.moneyregular, 1);
                    if (!WapTool.IsNumeric(getmoney))
                    {
                        getmoney = "0";
                    }
                    getexpr = WapTool.GetSiteDefault(siteVo.lvlRegular, 1);
                    if (!WapTool.IsNumeric(getexpr))
                    {
                        getexpr = "0";
                    }
                    string[] array2 = (WapTool.GetArryString(classVo.smallimg, '|', 37) + ",").Split(',');
                    if (WapTool.IsNumeric(array2[0].Replace("-", "")))
                    {
                        getmoney = array2[0];
                    }
                    if (WapTool.IsNumeric(array2[1].Replace("-", "")))
                    {
                        getexpr = array2[1];
                    }

                    MainBll.UpdateSQL($"update [user] set [money]=([money]+{getmoney}-{sendmoney}),expR=expR+{getexpr},bbsReCount={userVo.bbsReCount + 1L} where siteid={siteid} and userid={userid}");
                    SaveBankLog(userid, "论坛回贴", getmoney.ToString(), userid, nickname, $"文件回贴[{id}]");
                }
                else
                {
                    getmoney = "0";
                    getexpr = "0";
                }

                INFO = "OK";
                WapTool.ClearDataBBSRe("bbsRe" + siteid + id);
                Action_user_doit(2);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToStringFirstLine(ex.ToString());
            }
        }
    }
}