﻿using System;
using System.Collections.Generic;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string types = "";

        public string backurl = "";

        public string linkTOP = "";

        public string issystem = "";

        public List<wap_message_Model> listVo = null;

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            issystem = GetRequestValue("issystem");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");
            types = GetRequestValue("types");
            if (types == "")
            {
                types = "0";
            }
            if (action == "save")
            {
                string requestValue = GetRequestValue("id");
                if (WapTool.IsNumeric(requestValue))
                {
                    MainBll.UpdateSQL("update wap_message set issystem=2 where siteid=" + siteid + " and touserid=" + userid + " and id=" + requestValue);
                }
            }

            // 根据 types 构建基础查询条件
            if (types == "2") // 发件箱
            {
                condition = " siteid= " + siteid + " and touserid=" + userid + " and isnew = 2 "; // 查你是发送人
            }
            else // 收件箱 (types == "0" 或其他默认情况)
            {
                condition = " siteid= " + siteid + " and touserid=" + userid + " and isnew < 2 "; // 保持 touserid=当前用户ID

                // 收件箱的 issystem 筛选逻辑
                if (WapTool.IsNumeric(issystem))
                {
                    condition = condition + " and issystem = " + issystem;
                }
                else
                {
                    // 维持原逻辑，可能用于排除特定状态的消息
                    condition += " and issystem <> 2 ";
                }
            }

            // 追加 title 搜索条件 (适用于收/发件箱)
            if (!string.IsNullOrEmpty(key))
            {
                // 基本的 SQL 注入防护，替换单引号
                condition = condition + " and title like '%" + key.Replace("'", "''") + "%' ";
            }

            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                wap_message_BLL wap_message_BLL = new wap_message_BLL(string_10);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap_message_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/messagelist.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=" + types + "&amp;issystem=" + issystem + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
                listVo = wap_message_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "id", total, 1L);

                if (types == "2" && listVo != null)
                {
                    user_BLL userBll = new user_BLL(string_10);
                    foreach (var msg in listVo)
                    {
                        var touser = userBll.getUserInfo(msg.userid.ToString(), siteid); // userid 是收件人ID
                        if (touser != null && !string.IsNullOrEmpty(touser.nickname))
                        {
                            msg.tonickname = touser.nickname;
                        }
                        else
                        {
                            msg.tonickname = msg.userid.ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}