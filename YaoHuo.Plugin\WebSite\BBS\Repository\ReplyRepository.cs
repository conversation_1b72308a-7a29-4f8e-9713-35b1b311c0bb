﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using KeLin.ClassManager;

namespace YaoHuo.Plugin.Tool
{
    /// <summary>
    /// 用于封装从数据库查询返回的单条回复数据
    /// </summary>
    public class ReplyData
    {
        public long Id { get; set; }
        public string SiteId { get; set; } // devid
        public long UserId { get; set; }
        public string Nickname { get; set; }
        public long ClassId { get; set; } // 根据 wap_bbsre_Model 需求添加
        public long BookId { get; set; }
        public string Content { get; set; }
        public DateTime ReplyDate { get; set; } // redate
        public int MyGetMoney { get; set; }
        public short BookTop { get; set; } // book_top
        public long AttachCount { get; set; } // isdown
        public long ReplyToFloor { get; set; } // reply
        public int OriginalFloor { get; set; } // 由 SQL 计算
        /// <summary>
        /// 回复的审核状态：0=正常，1=未审核，2=用户删除，3=管理员删除
        /// 作用：
        /// 1. 状态0：正常显示的回复，参与楼层计算且内容正常显示
        /// 2. 状态1：未审核回复，参与楼层计算但不显示内容
        /// 3. 状态2：用户删除的回复，参与楼层计算但显示"已被用户删除"
        /// 4. 状态3：管理员删除的回复，参与楼层计算但显示"已被管理员删除"
        /// 
        /// 扩展isCheck字段的含义，保证楼层号稳定性并支持软删除机制
        /// </summary>
        public int CheckStatus { get; set; } // isCheck
    }

    /// <summary>
    /// 提供对 wap_bbsre 表的高效分页访问
    /// </summary>
    public class ReplyRepository
    {
        private readonly string _connectionString;
        // 集中定义初步过滤词
        private static readonly List<string> preliminaryFilterTerms = new List<string> { "吃", "吃了", "吃肉", "吃吃", "吃。" };

        public ReplyRepository(string siteInstanceName)
        {
            // 获取特定站点实例的连接字符串
            // 注意：如果 PubConstant.GetConnectionString 不可用或不适合在此处使用，
            // 可能需要调整连接字符串的获取方式（例如，通过配置或依赖注入）。
            _connectionString = PubConstant.GetConnectionString(siteInstanceName);
            if (string.IsNullOrEmpty(_connectionString))
            {
                throw new InvalidOperationException($"无法获取站点实例 '{siteInstanceName}' 的数据库连接字符串。");
            }
        }

        /// <summary>
        /// 分页获取回帖列表（现在建议调用方一次性拉取所有回复，C#层再做过滤和分页）
        /// </summary>
        /// <param name="bookId">帖子ID</param>
        /// <param name="siteId">站点ID (devid)</param>
        /// <param name="pageNumber">页码 (从1开始)</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sortOrder">排序顺序 ("ASC" 或 "DESC")</param>
        /// <param name="mainUserId">只看某用户ID (可选)</param>
        /// <param name="enableSqlFilter">已废弃，保留参数但不再生效</param>
        /// <param name="currentUserId">当前用户ID (可选)</param>
        /// <param name="includeTopReplies">是否包含置顶回帖（book_top=1），默认false。用于定位/跳转等场景。</param>
        /// <returns>当前页的回帖列表</returns>
        public List<ReplyData> GetRepliesPaged(long bookId, string siteId, int pageNumber, int pageSize, string sortOrder, string mainUserId = null, bool enableSqlFilter = false, long? currentUserId = null, bool includeTopReplies = false)
        {
            var replies = new List<ReplyData>();
            long startRow = (pageNumber - 1) * pageSize + 1;
            long endRow = pageNumber * pageSize;
            long userIdFilter = 0;
            bool useUserFilter = !string.IsNullOrEmpty(mainUserId) && long.TryParse(mainUserId, out userIdFilter);

            string orderByClause = (sortOrder?.ToUpper() == "ASC") ? "id ASC" : "id DESC";
            string rowNumOrderByClause = (sortOrder?.ToUpper() == "ASC") ? "ORDER BY r.id ASC" : "ORDER BY r.id DESC";

            // 已废弃SQL内容过滤，保留参数但不再拼接SQL
            string blacklistFilterSql = currentUserId.HasValue && currentUserId.Value != 0
                ? $"AND r.userid NOT IN (SELECT frienduserid FROM wap_friends WITH (NOLOCK) WHERE friendtype = 1 AND userid = {currentUserId.Value})"
                : string.Empty;

            // 新增：可选包含置顶回帖
            string topFilterSql = includeTopReplies ? "" : "AND r.book_top = 0";

            string sql = $@"
WITH AllChronologicalReplies AS (
    SELECT
        id,
        ROW_NUMBER() OVER (ORDER BY id ASC) AS OriginalFloor
    FROM
        wap_bbsre WITH (NOLOCK)
    WHERE
        devid = @SiteId
        AND bookid = @BookId
        AND isCheck IN (0, 1, 2, 3)
),
PagedReplies AS (
    SELECT
        r.id, r.devid, r.userid, r.nickname, r.classid, r.bookid, r.content, r.redate, r.myGetMoney, r.book_top, r.isdown, r.reply,
        cr.OriginalFloor,
        r.isCheck AS CheckStatus,
        ROW_NUMBER() OVER ({rowNumOrderByClause}) AS rn
    FROM
        wap_bbsre r WITH (NOLOCK)
    INNER JOIN
        AllChronologicalReplies cr ON r.id = cr.id
    WHERE
        r.devid = @SiteId
        AND r.bookid = @BookId
        AND r.isCheck = 0
        {topFilterSql}
        {(useUserFilter ? "AND r.userid = @MainUserId" : "")}
        {blacklistFilterSql}
)
SELECT
    id, devid, userid, nickname, classid, bookid, content, redate, myGetMoney, book_top, isdown, reply, OriginalFloor, CheckStatus
FROM
    PagedReplies
WHERE
    rn BETWEEN @StartRow AND @EndRow
ORDER BY
    rn;";

            using (var connection = new SqlConnection(_connectionString))
            {
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@SiteId", siteId);
                    command.Parameters.AddWithValue("@BookId", bookId);
                    command.Parameters.AddWithValue("@StartRow", startRow);
                    command.Parameters.AddWithValue("@EndRow", endRow);
                    if (useUserFilter)
                    {
                        command.Parameters.AddWithValue("@MainUserId", userIdFilter);
                    }
                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                replies.Add(new ReplyData
                                {
                                    Id = Convert.ToInt64(reader["id"]),
                                    SiteId = reader["devid"].ToString(),
                                    UserId = Convert.ToInt64(reader["userid"]),
                                    Nickname = reader["nickname"]?.ToString() ?? string.Empty,
                                    ClassId = Convert.ToInt64(reader["classid"]),
                                    BookId = Convert.ToInt64(reader["bookid"]),
                                    Content = reader["content"]?.ToString() ?? string.Empty,
                                    ReplyDate = Convert.ToDateTime(reader["redate"]),
                                    MyGetMoney = Convert.ToInt32(reader["myGetMoney"]),
                                    BookTop = Convert.ToInt16(reader["book_top"]),
                                    AttachCount = Convert.ToInt64(reader["isdown"]),
                                    ReplyToFloor = Convert.ToInt64(reader["reply"]),
                                    OriginalFloor = Convert.ToInt32(reader["OriginalFloor"]),
                                    CheckStatus = reader["CheckStatus"] != DBNull.Value ? Convert.ToInt32(reader["CheckStatus"]) : 0
                                });
                            }
                        }
                    }
                    catch
                    {
                        throw;
                    }
                }
            }
            return replies;
        }

        /// <summary>
        /// 获取非置顶回帖的总数（不再做内容过滤，直接统计所有正常回复）
        /// </summary>
        public int GetTotalReplyCount(long bookId, string siteId, string mainUserId = null, bool enableSqlFilter = false, bool performPreciseCount = false, long? preciseCountCurrentUserId = null, long? currentUserId = null)
        {
            int totalCount = 0;
            long userIdFilter = 0;
            bool useUserFilter = !string.IsNullOrEmpty(mainUserId) && long.TryParse(mainUserId, out userIdFilter);
            string blacklistFilterSql = currentUserId.HasValue ?
                $"AND wap_bbsre.userid NOT IN (SELECT frienduserid FROM wap_friends WHERE friendtype = 1 AND userid = {currentUserId.Value})"
                : "";
            string sql = $@"
SELECT
    COUNT_BIG(*)
FROM
    wap_bbsre WITH (NOLOCK)
WHERE
    devid = @SiteId
    AND bookid = @BookId
    AND isCheck = 0
    AND book_top = 0
    {(useUserFilter ? "AND userid = @MainUserId" : "")}
    {blacklistFilterSql}
";
            using (var connection = new SqlConnection(_connectionString))
            {
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@SiteId", siteId);
                    command.Parameters.AddWithValue("@BookId", bookId);
                    if (useUserFilter)
                    {
                        command.Parameters.AddWithValue("@MainUserId", userIdFilter);
                    }
                    try
                    {
                        connection.Open();
                        object result = command.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            totalCount = Convert.ToInt32(Convert.ToInt64(result));
                        }
                    }
                    catch
                    {
                        throw;
                    }
                }
            }
            return totalCount;
        }

        /// <summary>
        /// 根据提供的回复ID列表，获取它们对应的原始楼层号（按 id ASC 排序计算）
        /// </summary>
        /// <param name="siteId">站点ID</param>
        /// <param name="bookId">帖子ID</param>
        /// <param name="replyIds">需要查询楼层号的回复ID列表</param>
        /// <returns>一个字典，键是回复ID，值是对应的原始楼层号</returns>
        public Dictionary<long, int> GetOriginalFloorsByIds(string siteId, long bookId, List<long> replyIds)
        {
            var floorMap = new Dictionary<long, int>();
            if (replyIds == null || !replyIds.Any())
            {
                return floorMap;
            }

            // 将 long 列表转换为逗号分隔的字符串，确保ID是安全的（long类型本身是安全的）
            string idList = string.Join(",", replyIds.Select(id => id.ToString())); // Ensure IDs are strings for the IN clause

            // 使用与 GetRepliesPaged 类似的 CTE 来计算原始楼层号，但只针对指定的 ID
            // OriginalFloor is based on ALL visible replies (isCheck = 0), irrespective of book_top
            string sql = $@"
WITH AllChronologicalReplies AS (
    SELECT
        id,
        ROW_NUMBER() OVER (ORDER BY id ASC) AS OriginalFloor
    FROM
        wap_bbsre WITH (NOLOCK)
    WHERE
        devid = @SiteId
        AND bookid = @BookId
        AND ischeck IN (0, 1, 2, 3) -- 包含正常、未审核、用户删除、管理员删除
)
SELECT
    id, OriginalFloor
FROM
    AllChronologicalReplies
WHERE
    id IN ({idList}); -- 从计算好楼层的完整集合中筛选出我们需要的ID
";

            using (var connection = new SqlConnection(_connectionString))
            {
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@SiteId", siteId);
                    command.Parameters.AddWithValue("@BookId", bookId);
                    // 不需要为 idList 添加参数，因为它已直接嵌入

                    try
                    {
                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            int count = 0;
                            while (reader.Read())
                            {
                                floorMap[Convert.ToInt64(reader["id"])] = Convert.ToInt32(reader["OriginalFloor"]);
                                count++;
                            }
                        }
                    }
                    catch
                    {
                        throw;
                    }
                }
            }
            return floorMap;
        }

        /// <summary>
        /// 计算指定OriginalFloor的回帖在当前过滤和排序条件下所在的页码。
        /// isCheck = 0 (已审核), 2 (用户删除), 3 (管理员删除) 的回帖参与OriginalFloor的计算。
        /// 只有 isCheck = 0 的回帖对普通用户可见并参与分页。
        /// </summary>
        /// <returns>页码（从1开始），如果未找到或出错则返回-1。</returns>
        public int GetPageNumberForOriginalFloor(
            long bookId,
            string siteId,
            int targetOriginalFloor,
            int pageSize,
            string sortOrder,
            bool hideUselessReplies,
            long currentUserId)
        {
            if (targetOriginalFloor <= 0 || pageSize <= 0) return -1;

            string effectiveSortOrder = string.IsNullOrWhiteSpace(sortOrder) ? "ASC" : sortOrder.ToUpper();
            string sortDirection = (effectiveSortOrder == "DESC") ? "DESC" : "ASC";

            // 构建基础的 CTE，用于计算 OriginalFloor (包含 isCheck = 0, 2, 3)
            string originalFloorCte = $@"
WITH AllRepliesForFloorCalculation AS (
    SELECT
        r.id,
        ROW_NUMBER() OVER (ORDER BY r.id ASC) AS OriginalFloor
    FROM
        wap_bbsre r WITH (NOLOCK)
    WHERE
        r.bookid = @BookId 
        AND r.devid = @SiteId
        AND r.isCheck IN (0, 2, 3) -- 用户删除和管理员删除的也参与原始楼层计算
),
TargetReply AS (
    SELECT id 
    FROM AllRepliesForFloorCalculation 
    WHERE OriginalFloor = @TargetOriginalFloor
)";

            // 黑名单过滤条件
            string blacklistFilterSql = currentUserId > 0
                ? $"AND r.userid NOT IN (SELECT frienduserid FROM wap_friends WITH (NOLOCK) WHERE friendtype = 1 AND userid = {currentUserId})"
                : "";

            // ursprüngliche Logik für preliminaryFilterSql:
            string filterParamPlaceholders = string.Join(", ", preliminaryFilterTerms.Select((term, index) => $"@FilterTerm{index}"));
            string preliminaryFilterSql = hideUselessReplies && preliminaryFilterTerms.Count > 0
                ? $"AND CAST(r.content AS nvarchar(MAX)) NOT IN ({filterParamPlaceholders})"
                : string.Empty;


            string rankedVisibleRepliesCte = $@"
, VisibleAndFilteredReplies AS (
    SELECT
        r.id,
        ofc.OriginalFloor, 
        ROW_NUMBER() OVER (ORDER BY r.id {sortDirection}) as rn -- 根据实际的显示排序
    FROM
        wap_bbsre r WITH (NOLOCK)
    INNER JOIN 
        AllRepliesForFloorCalculation ofc ON r.id = ofc.id
    WHERE
        r.bookid = @BookId
        AND r.devid = @SiteId
        AND r.isCheck = 0 -- 核心：只对普通用户可见的进行分页计算
        AND r.book_top = 0 -- 不考虑置顶回复
        {blacklistFilterSql} 
        {preliminaryFilterSql} 
)";
            // 构建最终查询
            string sql = $@"
{originalFloorCte}
{rankedVisibleRepliesCte}
SELECT 
    vr.rn 
FROM 
    VisibleAndFilteredReplies vr
JOIN 
    TargetReply tr ON vr.id = tr.id;
";

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@BookId", bookId);
                        command.Parameters.AddWithValue("@SiteId", siteId);
                        command.Parameters.AddWithValue("@TargetOriginalFloor", targetOriginalFloor);

                        if (hideUselessReplies && preliminaryFilterTerms.Count > 0)
                        {
                            for (int i = 0; i < preliminaryFilterTerms.Count; i++)
                            {
                                command.Parameters.AddWithValue($"@FilterTerm{i}", preliminaryFilterTerms[i]);
                            }
                        }

                        object result = command.ExecuteScalar();

                        if (result != null && result != DBNull.Value)
                        {
                            long rowNumber = Convert.ToInt64(result);
                            int calculatedPage = (int)Math.Ceiling((double)rowNumber / pageSize);
                            return calculatedPage;
                        }
                        else
                        {
                            return -1;
                        }
                    }
                }
            }
            catch
            {
                return -1; // 发生错误
            }
        }
    }
}