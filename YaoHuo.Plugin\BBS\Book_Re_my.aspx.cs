using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace YaoHuo.Plugin.BBS
{
    public class Book_Re_My : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");
        private string _connectionString;
        private const int MaxKeywordsAllowed = 5;
        public string action = "";
        public string linkURL = "";
        public string linkTOP = "";
        public string condition = "";
        public string ERROR = "";
        public string INFO = "";
        public string touserid = "0";
        public string lpage = "";
        public string ot = "0";
        public List<wap_bbsre_Model> listVo = null;
        public StringBuilder strhtml = new StringBuilder();
        public long kk = 1L;
        public long index = 0L;
        public long total = 0L;
        public long pageSize = 10L;
        public long CurrentPage = 1L;
        public string searchKey = "";
        public bool forceCharIndex = false;

        private static ConcurrentDictionary<string, (DateTime LastSearchTime, int SearchCount, string LastSearchKey)> _searchCache = new ConcurrentDictionary<string, (DateTime, int, string)>();

        protected void Page_Load(object sender, EventArgs e)
        {
            _connectionString = PubConstant.GetConnectionString(string_10); // 获取数据库连接字符串

            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            // 继续执行原有逻辑...
            action = GetRequestValue("action");
            touserid = GetRequestValue("touserid").TrimStart('0');
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            searchKey = GetRequestValue("searchKey");

            // 检查是否强制使用CHARINDEX搜索（通过#号包裹）
            if (!string.IsNullOrEmpty(searchKey) && searchKey.StartsWith("#") && searchKey.EndsWith("#"))
            {
                forceCharIndex = true;
                searchKey = searchKey.Trim('#');
            }

            bool isAdmin = IsCheckManagerLvl("|00|01|", "");

            if (touserid == "1000" && !isAdmin)
            {
                CurrentPage = 1;
                if (ot == "1")
                {
                    string redirectUrl = $"{http_start}bbs/book_re_my.aspx?action=class&siteid={siteid}&classid={classid}&touserid={touserid}&ot=0";
                    Response.Redirect(redirectUrl);
                    return;
                }
                ot = "0";
            }
            else if (GetRequestValue("page") != "")
            {
                if (long.TryParse(GetRequestValue("page"), out long pageNumber))
                {
                    CurrentPage = pageNumber;
                }
                else
                {
                    CurrentPage = 1;
                }
            }

            if (!string.IsNullOrEmpty(searchKey) && touserid != userid && !isAdmin)
            {
                ERROR = "ERR_PERMISSION";
                return;
            }

            if (!string.IsNullOrEmpty(searchKey) && (searchKey.Length < 1 || searchKey.Length > 30)) // 注意：移除#后长度可能小于1
            {
                if (!string.IsNullOrEmpty(searchKey.Trim('#')) && searchKey.Trim('#').Length < 1)
                {
                    ERROR = "ERR_LENGTH";
                    return;
                }
                if (searchKey.Length > 30)
                {
                    ERROR = "ERR_LENGTH";
                    return;
                }
            }

            if (touserid == "3814")
            {
                Response.Redirect("/");
                return;
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                if (CanSearch() && (touserid == userid || isAdmin))
                {
                    SearchRepliesWithFullText(isAdmin);
                }
                else
                {
                    ERROR = "<div class=\"tip\">搜索频率过高，请3秒后再试!</div>";
                }
            }
            else
            {
                showclass(isAdmin);
            }
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";

            // 检查Cookie中的UI偏好
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }

            // 默认使用旧版本
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            // 如果偏好是新版本，尝试使用Handlebars模板
            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars(); // 返回是否成功渲染新版
            }

            return false; // 使用旧版
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 检查是否存在TemplateService
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    // 使用反射调用TemplateService
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true; // 成功渲染新版
                        }
                    }
                }

                // 如果Handlebars不可用，记录错误但不回退
                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("Handlebars模板服务不可用，继续使用旧版");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 RenderWithHandlebars 中 Response.End() 的正常行为
                // 表示新版渲染成功并正常终止了线程，这是期望的行为
                System.Diagnostics.Debug.WriteLine("新版渲染成功，线程正常终止");
                return true; // 实际上是成功的
            }
            catch (Exception ex)
            {
                // 记录错误但不回退到静态模板
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                System.Diagnostics.Debug.WriteLine($"模板渲染错误: {ex.Message}，继续使用旧版");
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 执行原有的数据加载逻辑
                InitializePageDataForNewUI();

                // 构建页面数据模型
                var pageModel = BuildBookReMyPageModel();

                // 调用TemplateService.RenderPageWithLayout方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/BookReMy.hbs",
                    pageModel,
                    GetPageTitle(),
                    pageModel.HeaderOptions
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End(); // 使用Response.End()确保页面执行完全终止
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，不需要处理
                // 直接重新抛出，让它正常终止线程
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 (Book_Re_my.aspx): {ex.ToString()}");
                // 记录错误但不回退到旧版，让新的TemplateService返回错误HTML信息
                ERROR = "新版界面加载失败: " + WapTool.ErrorToString(ex.ToString());
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest(); // 在异常处理中使用CompleteRequest避免嵌套异常
            }
        }

        /// <summary>
        /// 初始化页面数据（为新版UI复用原有逻辑）
        /// </summary>
        private void InitializePageDataForNewUI()
        {
            // 复用原有的数据初始化逻辑
            action = GetRequestValue("action");
            touserid = GetRequestValue("touserid").TrimStart('0');
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            searchKey = GetRequestValue("searchKey");

            // 检查是否强制使用CHARINDEX搜索
            if (!string.IsNullOrEmpty(searchKey) && searchKey.StartsWith("#") && searchKey.EndsWith("#"))
            {
                forceCharIndex = true;
                searchKey = searchKey.Trim('#');
            }

            bool isAdmin = IsCheckManagerLvl("|00|01|", "");

            // 权限和参数验证
            if (touserid == "1000" && !isAdmin)
            {
                CurrentPage = 1;
                if (ot == "1")
                {
                    string redirectUrl = $"{http_start}bbs/book_re_my.aspx?action=class&siteid={siteid}&classid={classid}&touserid={touserid}&ot=0";
                    Response.Redirect(redirectUrl);
                    return;
                }
                ot = "0";
            }
            else if (GetRequestValue("page") != "")
            {
                if (long.TryParse(GetRequestValue("page"), out long pageNumber))
                {
                    CurrentPage = pageNumber;
                }
                else
                {
                    CurrentPage = 1;
                }
            }

            // 权限检查
            if (!string.IsNullOrEmpty(searchKey) && touserid != userid && !isAdmin)
            {
                ERROR = "ERR_PERMISSION";
                return;
            }

            // 搜索关键词长度检查
            if (!string.IsNullOrEmpty(searchKey) && (searchKey.Length < 1 || searchKey.Length > 30))
            {
                if (!string.IsNullOrEmpty(searchKey.Trim('#')) && searchKey.Trim('#').Length < 1)
                {
                    ERROR = "ERR_LENGTH";
                    return;
                }
                if (searchKey.Length > 30)
                {
                    ERROR = "ERR_LENGTH";
                    return;
                }
            }

            // 特殊用户处理
            if (touserid == "3814")
            {
                Response.Redirect("/");
                return;
            }

            // 执行搜索或列表查询
            if (!string.IsNullOrEmpty(searchKey))
            {
                if (CanSearch() && (touserid == userid || isAdmin))
                {
                    SearchRepliesWithFullText(isAdmin);
                }
                else
                {
                    ERROR = "<div class=\"tip\">搜索频率过高，请3秒后再试!</div>";
                }
            }
            else
            {
                showclass(isAdmin);
            }
        }

        /// <summary>
        /// 构建页面数据模型
        /// </summary>
        /// <returns>页面数据模型</returns>
        private BookReMyPageModel BuildBookReMyPageModel()
        {
            var model = new BookReMyPageModel
            {
                PageTitle = GetPageTitle()
            };

            // 构建头部选项模型
            BuildHeaderOptionsModel(model);

            // 构建消息模型
            BuildMessageModel(model);

            // 构建搜索表单模型
            BuildSearchFormModel(model);

            // 构建排序模型
            BuildSortModel(model);

            // 构建回复列表模型
            BuildReplyListModel(model);

            // 构建分页模型
            BuildPaginationModel(model);

            // 构建站点信息模型
            BuildSiteInfoModel(model);

            // 构建权限模型
            BuildPermissionModel(model);

            // 构建管理员操作模型
            BuildAdminActionsModel(model);

            return model;
        }

        /// <summary>
        /// 构建头部选项模型
        /// </summary>
        private void BuildHeaderOptionsModel(BookReMyPageModel model)
        {
            model.HeaderOptions = new HeaderOptionsModel
            {
                ShowViewModeToggle = false
            };

            // 检查用户ID是否有效，方式为：
            // 1. 如果没有错误消息，则认为用户ID有效
            // 2. 如果有错误消息，但不包含"无效"或"PID"，也认为用户ID有效
            bool isValidUserId = string.IsNullOrEmpty(ERROR) ||
                               (!ERROR.Contains("无效") && !ERROR.Contains("PID"));

            // 仅对管理员显示右上角按钮，且用户ID有效
            if (isValidUserId && CheckManagerLvl("04", ""))
            {
                // 构建清空回复的URL
                string clearRepliesUrl = http_start + "bbs/Book_re_delmy.aspx?action=go&siteid=" + siteid +
                    "&classid=" + classid + "&lpage=" + lpage + "&page=" + CurrentPage +
                    "&touserid=" + touserid + "&ot=" + ot;

                // 使用新版按钮集合API - 只有清空回复功能
                model.HeaderOptions.CustomButtons.Add(new HeaderButtonModel
                {
                    Id = "admin-options",
                    Icon = "more-vertical",
                    Tooltip = "管理选项",
                    HasDropdown = true,
                    DropdownItems = new List<HeaderDropdownItemModel>
                    {
                        new HeaderDropdownItemModel {
                            Text = "清空回复",
                            Icon = "trash",
                            OnClick = $"confirmClearReplies('{clearRepliesUrl}')"
                        }
                    }
                });
            }
        }

        /// <summary>
        /// 获取页面标题
        /// </summary>
        /// <returns>页面标题</returns>
        private string GetPageTitle()
        {
            return this.GetLang("查看" + this.touserid + "的回复|查看" + this.touserid + "的回復|View Reply");
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(BookReMyPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;

                if (ERROR == "ERR_PERMISSION")
                {
                    model.Message.Type = "error";
                    model.Message.Content = "您只能搜索自己的回复内容!";
                }
                else if (ERROR == "ERR_LENGTH")
                {
                    model.Message.Type = "error";
                    model.Message.Content = "搜索关键词长度必须在1-30个字符之间!";
                }
                else if (ERROR.Contains("搜索频率过高"))
                {
                    model.Message.Type = "warning";
                    model.Message.Content = "搜索频率过高，请3秒后再试!";
                }
                else
                {
                    model.Message.Type = "error";
                    model.Message.Content = ERROR;
                }
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "info";
                model.Message.Content = INFO;
            }
        }

        /// <summary>
        /// 构建搜索表单模型
        /// </summary>
        private void BuildSearchFormModel(BookReMyPageModel model)
        {
            bool isAdmin = IsCheckManagerLvl("|00|01|", "");
            model.SearchForm.CanSearch = (touserid == userid || isAdmin);
            model.SearchForm.SearchKey = forceCharIndex ? "#" + searchKey + "#" : searchKey;
            model.SearchForm.IsForceCharIndex = forceCharIndex;
            model.SearchForm.SearchUrl = http_start + "bbs/book_re_my.aspx";
            model.SearchForm.HasSearchResult = !string.IsNullOrEmpty(searchKey);
            model.SearchForm.SearchHint = "支持多关键词搜索，用空格分隔（最多5个）；用#包裹关键词可强制精确搜索";
        }

        /// <summary>
        /// 构建排序模型
        /// </summary>
        private void BuildSortModel(BookReMyPageModel model)
        {
            model.Sort.CurrentSort = ot;

            string baseUrl = http_start + "bbs/book_re_my.aspx?action=class&siteid=" + siteid +
                             "&classid=" + classid + "&touserid=" + touserid +
                             "&page=" + CurrentPage + "&lpage=" + lpage;

            if (!string.IsNullOrEmpty(searchKey))
            {
                string encodedSearchKey = forceCharIndex ?
                    HttpUtility.UrlEncode("#" + searchKey + "#") :
                    HttpUtility.UrlEncode(searchKey);
                baseUrl += "&searchKey=" + encodedSearchKey;
            }

            model.Sort.NewestUrl = baseUrl + "&ot=0";
            model.Sort.OldestUrl = baseUrl + "&ot=1";
        }

        /// <summary>
        /// 构建回复列表模型
        /// </summary>
        private void BuildReplyListModel(BookReMyPageModel model)
        {
            if (listVo != null && listVo.Count > 0)
            {
                long tempKk = kk + ((CurrentPage - 1) * pageSize) - 1;

                for (int i = 0; i < listVo.Count; i++)
                {
                    long displayIndex;
                    if (ot == "1")
                    {
                        displayIndex = tempKk + 1;
                    }
                    else
                    {
                        displayIndex = total - tempKk;
                    }

                    // 处理UBB标签和emoji，使用wml对象进行UBB解析
                    string processedContent = string.Empty;
                    if (!string.IsNullOrEmpty(listVo[i].content))
                    {
                        // 使用与旧版相同的UBB处理方式，ToWML包含完整的UBB处理流程
                        processedContent = WapTool.ToWML(listVo[i].content, wmlVo);
                    }
                    
                    var replyItem = new ReplyItemModel
                    {
                        Id = listVo[i].id,
                        Index = displayIndex,
                        UserId = listVo[i].userid,
                        Nickname = listVo[i].nickname,
                        Content = processedContent, // 使用处理后的内容
                        RawContent = listVo[i].content, // 保留原始内容，以备需要
                        ReplyDate = listVo[i].redate,
                        FormattedDate = listVo[i].redate.ToString("yyyy-MM-dd HH:mm"),
                        BookId = listVo[i].bookid,
                        ViewUrl = http_start + "bbs-" + listVo[i].bookid + ".html",
                        UserInfoUrl = http_start + "bbs/userinfo.aspx?touserid=" + listVo[i].userid
                    };

                    model.ReplyList.Add(replyItem);
                    tempKk++;
                }
            }
        }

        /// <summary>
        /// 构建分页模型
        /// </summary>
        private void BuildPaginationModel(BookReMyPageModel model)
        {
            model.Pagination.HasPages = total > pageSize;
            model.Pagination.CurrentPage = (int)CurrentPage;
            model.Pagination.TotalPages = (int)Math.Ceiling((double)total / pageSize);
            model.Pagination.TotalItems = (int)total;
            model.Pagination.PageSize = (int)pageSize;
            model.Pagination.IsFirstPage = CurrentPage <= 1;
            model.Pagination.IsLastPage = CurrentPage >= model.Pagination.TotalPages;
            model.Pagination.ShowPagination = total > 0; // 添加ShowPagination属性设置
        }

        /// <summary>
        /// 构建站点信息模型
        /// </summary>
        private void BuildSiteInfoModel(BookReMyPageModel model)
        {
            model.SiteInfo.SiteId = siteid.ToString();
            model.SiteInfo.ClassId = classid.ToString();
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.BackUrl = "myfile.aspx?siteid=" + siteid;
            model.SiteInfo.UserSpaceUrl = http_start + "bbs/myfile.aspx?siteid=" + siteid;
            model.SiteInfo.HomeUrl = http_start + "default.aspx?siteid=" + siteid;
        }

        /// <summary>
        /// 构建权限模型
        /// </summary>
        private void BuildPermissionModel(BookReMyPageModel model)
        {
            bool isAdmin = IsCheckManagerLvl("|00|01|", "");
            model.Permissions.IsAdmin = isAdmin;
            model.Permissions.CanViewOthers = isAdmin;
            model.Permissions.IsViewingSelf = (touserid == userid);
            model.Permissions.CurrentUserId = userid;
            model.Permissions.TargetUserId = touserid;
            model.Permissions.CanSearch = (touserid == userid || isAdmin);
        }

        /// <summary>
        /// 构建管理员操作模型
        /// </summary>
        private void BuildAdminActionsModel(BookReMyPageModel model)
        {
            bool isAdmin = CheckManagerLvl("04", "");
            model.AdminActions.CanClearReplies = isAdmin && touserid != "1000";
            model.AdminActions.TargetUserId = touserid;
            model.AdminActions.ShowAdminActions = model.AdminActions.CanClearReplies;

            if (model.AdminActions.CanClearReplies)
            {
                model.AdminActions.ClearRepliesUrl = http_start + "bbs/Book_re_delmy.aspx?action=go&siteid=" + siteid +
                    "&classid=" + classid + "&lpage=" + lpage + "&page=" + CurrentPage +
                    "&touserid=" + touserid + "&ot=" + ot;
            }
        }

        private void SearchRepliesWithFullText(bool isAdmin)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            try
            {
                if (!int.TryParse(touserid, out int parsedTouserid))
                {
                    ERROR = "无效的用户ID";
                    return;
                }

                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                string[] keywords = GetSearchKeywords(searchKey);

                string orderDirection = (ot == "1") ? "ASC" : "DESC";
                long startRow = (CurrentPage - 1) * pageSize + 1;
                long endRow = CurrentPage * pageSize;

                StringBuilder sqlBuilder = new StringBuilder();
                List<SqlParameter> parameters = new List<SqlParameter>();

                // 构建搜索条件
                string whereClause = BuildSearchWhereClause(keywords, parsedTouserid, parameters);

                // 构建分页SQL和总数SQL
                sqlBuilder.Append(@"
WITH FilteredReplies AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (ORDER BY id " + orderDirection + @") AS RowNum
    FROM 
        wap_bbsre WITH (NOLOCK)
    WHERE 
        " + whereClause + @"
)
SELECT 
    id, devid, userid, nickname, classid, bookid, content, redate, 
    myGetMoney, book_top, isdown, reply
FROM 
    FilteredReplies
WHERE 
    RowNum BETWEEN @StartRow AND @EndRow
ORDER BY 
    RowNum;

SELECT COUNT_BIG(*) FROM wap_bbsre WITH (NOLOCK) WHERE " + whereClause);

                parameters.Add(new SqlParameter("@StartRow", startRow));
                parameters.Add(new SqlParameter("@EndRow", endRow));

                // 执行SQL查询，填充listVo和total
                ExecuteSearchQuery(sqlBuilder.ToString(), parameters);

                stopwatch.Stop();
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ERROR = "搜索错误(FT): " + ex.Message;
                showclass(isAdmin); // 出错时回退到BLL查询
            }
        }

        // 构建搜索条件，优先使用全文索引，支持多关键词，自动回退到CHARINDEX
        private string BuildSearchWhereClause(string[] keywords, int parsedTouserid, List<SqlParameter> parameters)
        {
            StringBuilder whereBuilder = new StringBuilder();
            whereBuilder.Append("userid = @UserId");
            parameters.Add(new SqlParameter("@UserId", parsedTouserid));

            if (keywords != null && keywords.Length > 0)
            {
                whereBuilder.Append(" AND ");
                if (forceCharIndex)
                {
                    // 强制使用CHARINDEX
                    whereBuilder.Append(BuildMultiKeywordCharIndexSql(keywords, parameters, "@KMCF"));
                }
                else if (keywords.Length == 1)
                {
                    try
                    {
                        // 使用双引号强制全文索引精确短语匹配，提高效率 (根据之前的日志发现的行为)
                        whereBuilder.Append("CONTAINS(content, @KeywordFts)");
                        parameters.Add(new SqlParameter("@KeywordFts", "\"" + keywords[0].Replace("\"", "") + "\"")); // 使用双引号包裹关键词
                    }
                    catch (Exception)
                    {
                        whereBuilder.Append(BuildMultiKeywordCharIndexSql(new[] { keywords[0] }, parameters, "@KCSF")); // Keyword Char Singular Fallback
                    }
                }
                else // Multiple keywords
                {
                    try
                    {
                        // 对于多关键词，优先尝试使用全文索引 AND CHARINDEX (>2匹配) 组合
                        StringBuilder ftsQueryBuilder = new StringBuilder();
                        // 构建 OR 关系的全文索引查询，每个词用双引号包裹强制短语匹配
                        for (int i = 0; i < keywords.Length; i++)
                        {
                            if (i > 0) ftsQueryBuilder.Append(" OR ");
                            ftsQueryBuilder.Append("\"" + keywords[i].Replace("\"", "") + "\"");
                        }
                        whereBuilder.Append("CONTAINS(content, @KeywordsFtsQuery)");
                        parameters.Add(new SqlParameter("@KeywordsFtsQuery", ftsQueryBuilder.ToString()));

                        // 结合CHARINDEX确保至少2个词匹配
                        whereBuilder.Append(" AND ").Append(BuildMultiKeywordCharIndexSql(keywords, parameters, "@KMCF"));
                    }
                    catch (Exception)
                    {
                        whereBuilder.Append(BuildMultiKeywordCharIndexSql(keywords, parameters, "@KMCCF")); // Keyword Multi Char Char Fallback
                    }
                }
            }
            return whereBuilder.ToString();
        }

        private void ExecuteSearchQuery(string sql, List<SqlParameter> parameters)
        {
            Stopwatch sqlStopwatch = new Stopwatch();
            sqlStopwatch.Start();

            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                connection.Open();

                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        listVo = new List<wap_bbsre_Model>();
                        int rowCount = 0;
                        while (reader.Read())
                        {
                            // 将数据库行映射为wap_bbsre_Model对象
                            listVo.Add(new wap_bbsre_Model
                            {
                                id = Convert.ToInt64(reader["id"]),
                                devid = reader["devid"].ToString(),
                                userid = Convert.ToInt64(reader["userid"]),
                                nickname = reader["nickname"].ToString(),
                                classid = Convert.ToInt64(reader["classid"]),
                                bookid = Convert.ToInt64(reader["bookid"]),
                                content = reader["content"].ToString(),
                                redate = Convert.ToDateTime(reader["redate"]),
                                myGetMoney = Convert.ToInt32(reader["myGetMoney"]),
                                book_top = Convert.ToInt16(reader["book_top"]),
                                isdown = Convert.ToInt64(reader["isdown"]),
                                reply = Convert.ToInt64(reader["reply"])
                            });
                            rowCount++;
                        }

                        if (reader.NextResult() && reader.Read())
                        {
                            total = Convert.ToInt64(reader[0]);
                        }
                    }
                }
            }

            sqlStopwatch.Stop();

            // 校正当前页码，防止越界
            CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
            index = pageSize * (CurrentPage - 1L);
            // 统一更新分页链接
            UpdatePageLinks();
        }

        // 统一分页链接生成
        private void UpdatePageLinks()
        {
            linkURL = http_start + "bbs/book_re_my.aspx?action=class&amp;siteid=" + siteid +
                      "&amp;classid=" + classid + "&amp;touserid=" + touserid +
                      "&amp;lpage=" + lpage + "&amp;getTotal=" + total + "&amp;ot=" + ot;
            if (!string.IsNullOrEmpty(searchKey))
            {
                if (forceCharIndex)
                {
                    // 保持强制CHARINDEX搜索标记 (#)
                    linkURL += "&amp;searchKey=" + HttpUtility.UrlEncode("#" + searchKey + "#");
                }
                else
                {
                    // 普通搜索关键词
                    linkURL += "&amp;searchKey=" + HttpUtility.UrlEncode(searchKey);
                }
            }
            linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
            linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);

            bool isAdmin = IsCheckManagerLvl("|00|01|", "");
            if (touserid == "1000" && !isAdmin)
            {
                linkTOP = "";
                linkURL = "";
            }
        }

        // 分割并限制关键词数量
        private string[] GetSearchKeywords(string key, int maxKeywords = MaxKeywordsAllowed)
        {
            if (string.IsNullOrEmpty(key))
                return new string[0];
            string[] allKeywords = key.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (allKeywords.Length > maxKeywords)
            {
                string[] limited = new string[maxKeywords];
                Array.Copy(allKeywords, limited, maxKeywords);
                return limited;
            }
            return allKeywords;
        }

        // 构建多关键词CHARINDEX条件，支持参数化或直接拼接
        private string BuildMultiKeywordCharIndexSql(string[] keywords, List<SqlParameter> parameters, string paramPrefix)
        {
            if (keywords == null || keywords.Length == 0) return "1=1"; // 理论上不会发生

            StringBuilder sb = new StringBuilder();
            if (keywords.Length == 1)
            {
                if (parameters != null)
                {
                    parameters.Add(new SqlParameter(paramPrefix + "0", keywords[0]));
                    sb.Append($"CHARINDEX({paramPrefix + "0"}, content) > 0");
                }
                else
                {
                    sb.Append($"CHARINDEX(N'{keywords[0].Replace("'", "''")}', content) > 0");
                }
            }
            else // 多关键词，要求至少2个匹配
            {
                sb.Append("((");
                for (int i = 0; i < keywords.Length; i++)
                {
                    if (i > 0) sb.Append(" + ");
                    sb.Append("CASE WHEN CHARINDEX(");
                    if (parameters != null)
                    {
                        parameters.Add(new SqlParameter(paramPrefix + i, keywords[i]));
                        sb.Append(paramPrefix + i);
                    }
                    else
                    {
                        sb.Append($"N'{keywords[i].Replace("'", "''")}'");
                    }
                    sb.Append(", content) > 0 THEN 1 ELSE 0 END");
                }
                sb.Append($") >= {Math.Min(2, keywords.Length)})");
            }
            return sb.ToString();
        }

        // BLL路径下的内容搜索条件构建
        private string BuildSearchCondition(string[] keywords)
        {
            return BuildMultiKeywordCharIndexSql(keywords, null, null);
        }

        // 兼容原有BLL路径的回退查询
        public void showclass(bool isAdmin)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            int parsedTouserid;
            if (!int.TryParse(touserid, out parsedTouserid))
            {
                ERROR = "无效的用户ID";
                return;
            }
            // 当前页码已在Page_Load中处理，无需重复解析

            condition = $"userid='{parsedTouserid}' AND ischeck=0";

            if (!string.IsNullOrEmpty(searchKey) && (touserid == userid || isAdmin))
            {
                string[] keywords = GetSearchKeywords(searchKey);
                if (keywords.Length > 0)
                {
                    string searchCondition = BuildSearchCondition(keywords);
                    condition += " AND " + searchCondition;
                }
                // 如果没有有效关键词则不加内容搜索条件
            }

            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                wap_bbsre_BLL wap_bbsre_BLL = new wap_bbsre_BLL(string_10);

                total = wap_bbsre_BLL.GetListCount(condition);

                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                int orderType = (ot == "1") ? 0 : 1;

                listVo = wap_bbsre_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "id", total, orderType);

                UpdatePageLinks();

                stopwatch.Stop();
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                ERROR = "获取列表错误(BLL): " + ex.Message;
            }
        }

        private bool CanSearch()
        {
            string userKey = GetUserKey();
            var now = DateTime.Now;
            if (_searchCache.TryGetValue(userKey, out var lastSearch))
            {
                if (lastSearch.LastSearchKey == searchKey) return true;
                if ((now - lastSearch.LastSearchTime).TotalSeconds < 3)
                {
                    if (lastSearch.SearchCount >= 1) return false;
                    _searchCache[userKey] = (lastSearch.LastSearchTime, lastSearch.SearchCount + 1, searchKey);
                }
                else
                {
                    _searchCache[userKey] = (now, 1, searchKey);
                }
            }
            else
            {
                _searchCache[userKey] = (now, 1, searchKey);
            }
            return true;
        }

        private string GetUserKey()
        {
            return HttpContext.Current.User.Identity.IsAuthenticated
                ? HttpContext.Current.User.Identity.Name
                : HttpContext.Current.Request.UserHostAddress;
        }
    }
}