using System;
using System.Web;
using System.Net;
using System.Text;
using System.IO;
using KeLin.ClassManager;

public class GoCaptchaProxy : IHttpHandler
{
    public bool IsReusable => false;

    public void ProcessRequest(HttpContext context)
    {
        // 从 Web.config 读取配置
        string goCaptchaServiceUrl = PubConstant.GetAppString("GoCaptchaServiceUrl");
        string goCaptchaApiKey = PubConstant.GetAppString("GoCaptchaApiKey");
        string goCaptchaEnabled = PubConstant.GetAppString("GoCaptchaEnabled");

        if (goCaptchaEnabled != "1" || string.IsNullOrEmpty(goCaptchaServiceUrl))
        {
            context.Response.StatusCode = (int)HttpStatusCode.ServiceUnavailable;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"GoCaptcha service not enabled or misconfigured.\"}");
            return;
        }

        // 确定请求路径
        string requestPath = context.Request.QueryString["path"];
        string targetUrl = "";
        
        // 根据请求类型确定目标URL
        if (requestPath == "get-data" && context.Request.HttpMethod == "GET")
        {
            targetUrl = goCaptchaServiceUrl.TrimEnd('/') + "/api/v1/public/get-data";
        }
        else if (requestPath == "check-data" && context.Request.HttpMethod == "POST")
        {
            targetUrl = goCaptchaServiceUrl.TrimEnd('/') + "/api/v1/public/check-data";
        }
        else
        {
            context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"Invalid request method or path.\"}");
            return;
        }

        // 将前端请求中的其他查询字符串参数附加到目标URL
        StringBuilder queryString = new StringBuilder();
        foreach (string key in context.Request.QueryString.AllKeys)
        {
            if (!string.IsNullOrEmpty(key) && key.ToLower() != "path")
            {
                string[] values = context.Request.QueryString.GetValues(key);
                if (values != null)
                {
                    foreach (string value in values)
                    {
                        if (queryString.Length > 0) queryString.Append("&");
                        queryString.Append(Uri.EscapeDataString(key));
                        queryString.Append("=");
                        queryString.Append(Uri.EscapeDataString(value));
                    }
                }
            }
        }

        if (queryString.Length > 0)
        {
            targetUrl += "?" + queryString.ToString();
        }

        try
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            
            // 创建请求
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(targetUrl);
            request.Method = context.Request.HttpMethod;
            
            // 设置API密钥头
            if (!string.IsNullOrEmpty(goCaptchaApiKey))
            {
                request.Headers.Add("X-API-Key", goCaptchaApiKey);
            }

            // 转发内容类型和数据
            if (context.Request.HttpMethod == "POST")
            {
                request.ContentType = context.Request.ContentType;
                using (Stream requestStream = request.GetRequestStream())
                {
                    context.Request.InputStream.Position = 0;
                    context.Request.InputStream.CopyTo(requestStream);
                }
            }

            // 获取响应
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                string responseContent;
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    responseContent = reader.ReadToEnd();
                }

                if (string.IsNullOrEmpty(responseContent))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.BadGateway;
                    context.Response.ContentType = "application/json";
                    context.Response.Write("{\"error\":\"Empty response from GoCaptcha backend service\"}");
                    return;
                }

                // 设置响应头和状态码，将后端响应转发给前端
                context.Response.Clear();
                context.Response.BufferOutput = true;
                context.Response.StatusCode = (int)response.StatusCode;
                context.Response.ContentType = response.ContentType;
                context.Response.AppendHeader("Access-Control-Allow-Origin", "*");
                context.Response.AppendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                context.Response.AppendHeader("Access-Control-Allow-Headers", "Content-Type, X-API-Key");

                byte[] responseBytes = Encoding.UTF8.GetBytes(responseContent);
                context.Response.AddHeader("Content-Length", responseBytes.Length.ToString());
                context.Response.BinaryWrite(responseBytes);

                context.Response.Flush();
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }
        catch (WebException ex)
        {
            if (ex.Response != null)
            {
                using (StreamReader reader = new StreamReader(ex.Response.GetResponseStream()))
                {
                    string errorContent = reader.ReadToEnd();
                    context.Response.StatusCode = (int)((HttpWebResponse)ex.Response).StatusCode;
                    context.Response.ContentType = "application/json";
                    context.Response.Write(errorContent);
                    return;
                }
            }
            
            context.Response.StatusCode = (int)HttpStatusCode.BadGateway;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"Failed to connect to GoCaptcha backend service or received no response: " + ex.Message + "\"}");
        }
        catch (System.Threading.ThreadAbortException)
        {
            // 正常的线程中止，忽略
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"An error occurred while processing your request: " + ex.Message + "\"}");
        }
    }
}