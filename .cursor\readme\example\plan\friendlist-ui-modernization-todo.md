# FriendList 页面 UI 现代化改造 - 待办任务清单

## 📋 项目概况

**目标**：对 `YaoHuo.Plugin` 项目中的 `FriendList.aspx` 页面进行 UI 现代化改造，使用 Handlebars.NET 替换旧的 ASP.NET Web Forms UI，并实现新旧 UI 可切换。

**相关文件路径**：

- 后端代码：`YaoHuo.Plugin/BBS/FriendList.aspx.cs`
- 旧版前端：`YaoHuo.Plugin/BBS/FriendList.aspx`
- 参考设计：`YaoHuo.Plugin/friends.html`（好友列表UI）、`YaoHuo.Plugin/blocklist.html`（黑名单UI）
- 模板服务：`YaoHuo.Plugin/WebSite/Tool/TemplateService.cs`
- 头部模型：`YaoHuo.Plugin/Template/Models/HeaderOptionsModel.cs`
- 主布局：`YaoHuo.Plugin/Template/Layouts/MainLayout.hbs`

## 🎯 核心任务分解

### 阶段一：数据模型设计与创建

#### ✅ 任务 1.1：创建 FriendList 页面数据模型

- [ ] **文件路径**：`YaoHuo.Plugin/Template/Models/FriendListPageModel.cs`
- [ ] **任务详情**：
  - [ ] 定义主要页面模型 `FriendListPageModel` 类
  - [ ] 包含页面标题、消息状态、搜索数据、好友列表数据、分页信息等属性
  - [ ] 定义子模型：
    - [ ] `MessageModel`：处理 INFO/ERROR 消息显示
    - [ ] `SearchModel`：搜索框相关数据（仅 friendtype=0 时）
    - [ ] `FriendItemModel`：单个好友/黑名单项的数据结构
    - [ ] `PaginationModel`：分页导航数据
- [ ] **注意事项**：
  - [ ] 为头像、在线状态、发送私信等暂未实现功能预留占位符字段
  - [ ] 根据 `friendtype` 值设计条件渲染的数据结构

#### ✅ 任务 1.2：验证数据模型完整性

- [ ] **检查项**：
  - [ ] 确保所有 `friendtype` 类型（0=好友，1=黑名单，2=追求，4=追求我的人，5=推荐）的数据需求都被覆盖
  - [ ] 确认占位符字段的命名清晰且易于理解
  - [ ] 验证模型与后端 `listVo` 数据的映射关系

### 阶段二：Handlebars 模板创建

#### ✅ 任务 2.1：创建 FriendList Handlebars 模板

- [ ] **文件路径**：`YaoHuo.Plugin/Template/Pages/FriendList.hbs`
- [ ] **任务详情**：
  - [ ] **参考设计文件**：
    - [ ] 基于 `friends.html` 设计好友列表部分的布局和样式
    - [ ] 基于 `blocklist.html` 设计黑名单部分的布局和样式
    - [ ] **注意**：不完全照搬，而是参考其布局效果
  - [ ] **模板结构**：
    - [ ] 使用 `{{#eq friendtype "0"}}` 等条件逻辑渲染不同 friendtype 的内容
    - [ ] 渲染页面头部（标题根据 friendtype 动态显示）
    - [ ] 条件渲染搜索框（仅 friendtype=0 时显示）
    - [ ] 渲染好友/黑名单列表（使用 `{{#each}}` 循环）
    - [ ] 渲染分页导航
    - [ ] 渲染操作按钮（删除、备注等）
- [ ] **功能要求**：
  - [ ] 实现占位符显示（头像、在线状态、发送私信按钮等）
  - [ ] 添加前端确认对话框的 JavaScript 代码
  - [ ] 实现分页 HTML 解析和重新生成的 JavaScript 逻辑

#### ✅ 任务 2.2：创建页面专属 CSS

- [ ] **文件路径**：`YaoHuo.Plugin/Template/CSS/FriendList.css`
- [ ] **任务详情**：
  - [ ] 基于现有的 `HandlebarsCommon.css` 和 `HandlebarsForm.css` 进行扩展
  - [ ] 添加 FriendList 页面特有的样式规则
  - [ ] 确保与现有样式系统的兼容性
  - [ ] 适配移动端响应式设计

### 阶段三：后端代码改造

#### ✅ 任务 3.1：修复 SQL 注入安全问题

- [ ] **文件路径**：`YaoHuo.Plugin/BBS/FriendList.aspx.cs` 中的 `goaddfriend` 方法
- [ ] **任务详情**：
  - [ ] 找到插入操作：`insert into wap_friends(siteid,userid,frienduserid,friendusername,friendnickname,friendtype)values(...)`
  - [ ] 将拼接 SQL 的方式改为参数化查询
  - [ ] 移除对不存在的 `WapTool.PrepareForSQL` 方法的依赖
  - [ ] 确保 `friendnickname` 字段（即 `user_Model.nickname`）作为参数传入

#### ✅ 任务 3.2：实现新旧 UI 切换逻辑

- [ ] **文件路径**：`YaoHuo.Plugin/BBS/FriendList.aspx.cs`
- [ ] **任务详情**：
  - [ ] 在 `Page_Load` 方法开头添加 UI 偏好检查逻辑
  - [ ] 实现 `CheckAndHandleUIPreference()` 方法
  - [ ] 实现 `TryRenderWithHandlebars()` 方法
  - [ ] 确保正确处理 `ThreadAbortException`
  - [ ] 当新版渲染成功时阻止旧版代码执行

#### ✅ 任务 3.3：实现数据模型构建逻辑

- [ ] **文件路径**：`YaoHuo.Plugin/BBS/FriendList.aspx.cs`
- [ ] **任务详情**：
  - [ ] 在 `showclass` 方法中添加数据模型构建逻辑
  - [ ] 在 `goaddfriend` 方法中添加数据模型构建逻辑
  - [ ] 实现 `BuildFriendListPageModel()` 私有方法
  - [ ] 处理不同 `friendtype` 的页面标题和内容差异
  - [ ] 构建消息状态（INFO/ERROR）的显示逻辑
  - [ ] 处理搜索功能的数据绑定
  - [ ] 为占位符功能设置默认值

#### ✅ 任务 3.4：实现 Handlebars 渲染调用

- [ ] **文件路径**：`YaoHuo.Plugin/BBS/FriendList.aspx.cs`
- [ ] **任务详情**：
  - [ ] 实现 `RenderWithHandlebars()` 方法
  - [ ] 调用 `TemplateService.RenderPageWithLayout` 方法
  - [ ] 配置正确的模板路径：`~/Template/Pages/FriendList.hbs`
  - [ ] 根据 `friendtype` 动态设置页面标题
  - [ ] 配置适当的 `HeaderOptionsModel`
  - [ ] 处理渲染错误和异常情况

### 阶段四：前端功能实现

#### ✅ 任务 4.1：实现分页 HTML 解析功能

- [ ] **位置**：`FriendList.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 解析后端返回的 `linkURL` HTML 字符串
  - [ ] 提取分页链接、当前页、总页数、总条数等信息
  - [ ] 重新生成符合新版 UI 样式的分页导航
  - [ ] 确保分页链接点击后的跳转功能正常

#### ✅ 任务 4.2：实现前端确认对话框

- [ ] **位置**：`FriendList.hbs` 模板中的 JavaScript 代码
- [ ] **任务详情**：
  - [ ] 为删除操作添加确认提示
  - [ ] 为清空列表操作添加确认提示
  - [ ] 使用 `confirm()` 或自定义模态框实现
  - [ ] 确保用户体验友好

#### ✅ 任务 4.3：实现搜索功能

- [ ] **位置**：`FriendList.hbs` 模板
- [ ] **任务详情**：
  - [ ] 在 `friendtype=0` 时渲染搜索表单
  - [ ] 绑定搜索关键字 `key` 变量
  - [ ] 确保搜索表单提交到正确的后端接口

### 阶段五：测试与验证

#### ✅ 任务 5.1：功能测试

- [ ] **测试项目**：
  - [ ] 测试不同 `friendtype` 值下的页面显示
    - [ ] `friendtype=0`：好友列表 + 搜索功能
    - [ ] `friendtype=1`：黑名单列表
    - [ ] `friendtype=2`：追求列表
    - [ ] `friendtype=4`：追求我的人列表
    - [ ] `friendtype=5`：推荐列表
  - [ ] 测试新旧 UI 切换功能
  - [ ] 测试搜索功能（仅 `friendtype=0`）
  - [ ] 测试分页导航功能
  - [ ] 测试删除等操作的确认提示
  - [ ] 测试添加好友/黑名单功能

#### ✅ 任务 5.2：安全测试

- [ ] **测试项目**：
  - [ ] 验证 `goaddfriend` 方法中的 SQL 注入修复
  - [ ] 测试参数化查询是否正确执行
  - [ ] 验证用户输入的安全过滤

#### ✅ 任务 5.3：样式测试

- [ ] **测试项目**：
  - [ ] 检查新版 UI 是否有样式问题
  - [ ] 测试新旧样式是否存在冲突
  - [ ] 验证响应式设计在不同设备上的显示效果
  - [ ] 确认占位符功能的视觉表现

#### ✅ 任务 5.4：错误处理测试

- [ ] **测试项目**：
  - [ ] 测试数据模型构建中的异常处理
  - [ ] 测试模板渲染失败时的回退机制
  - [ ] 验证错误消息的显示是否正确

### 阶段六：代码质量与优化

#### ✅ 任务 6.1：代码审查

- [ ] **审查项目**：
  - [ ] 确保代码符合项目的 C# 编码规范
  - [ ] 验证命名约定的一致性
  - [ ] 检查注释和文档的完整性
  - [ ] 确认错误处理的完备性

#### ✅ 任务 6.2：性能优化

- [ ] **优化项目**：
  - [ ] 确认模板缓存机制工作正常
  - [ ] 检查数据模型构建的效率
  - [ ] 验证前端 JavaScript 的性能

#### ✅ 任务 6.3：项目文件管理

- [ ] **文件操作**：
  - [ ] 确保新创建的文件已添加到 `YaoHuo.Plugin.csproj` 项目中
  - [ ] 验证文件的"生成操作"设置正确
  - [ ] 检查命名空间引用的正确性

## 🎯 成功标准检查清单

- [ ] FriendList 页面能够成功通过 Handlebars 渲染出新的 UI
- [ ] 新 UI 在不同 `friendtype` 下能正确显示相应的数据（包括占位符）
- [ ] 新旧 UI 切换功能工作正常
- [ ] 分页导航通过前端 JS 成功重新生成并可交互
- [ ] 删除等操作的前端确认提示工作正常
- [ ] 没有明显的 UI 错误或样式问题
- [ ] `goaddfriend` 方法中的插入操作使用参数化查询，消除了 SQL 注入风险
- [ ] 搜索功能在新版 UI 下工作正常
- [ ] 所有占位符功能明确标识为"未来实现"
- [ ] 错误处理机制完善，提供良好的用户体验

## 📝 注意事项

1. **数据缺失处理**：后端目前不提供头像和在线状态数据，需在数据模型中使用占位符
2. **功能占位符**：发送私信功能暂不可用，需在前端渲染为占位元素
3. **安全优先**：必须修复 SQL 注入问题，使用参数化查询
4. **兼容性保证**：确保新旧 UI 切换机制的稳定性
5. **样式一致性**：新版 UI 应与项目整体设计风格保持一致

## 🔄 依赖关系

- 任务 1（数据模型）是任务 2、3 的前置条件
- 任务 2（模板创建）依赖任务 1 的完成
- 任务 3（后端改造）可与任务 2 并行进行
- 任务 4（前端功能）依赖任务 2 的完成
- 任务 5（测试）依赖前面所有开发任务的完成

---

**创建时间**：{{current_date}}
**项目版本**：基于 .NET Framework 4.8 + Handlebars.NET
**参考文档**：`friendlist-ui-modernization-requirements.md`
