﻿using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 修改密码页面 - 支持新旧 UI 切换
    /// </summary>
    public class ModifyPW : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string INFO = "";
        public string ERROR = "";
        public string oldpassword = "";
        public string newpassword = "";
        public string newrepassword = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 基础验证和初始化
            InitializePage();

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            
            // 处理表单提交（旧版和新版都需要）
            ProcessFormSubmission();
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";

            // 检查Cookie中的UI偏好
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }

            // 默认使用旧版本
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            // 如果偏好是新版本，尝试使用Handlebars模板
            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars(); // 返回是否成功渲染新版
            }

            return false; // 使用旧版
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 检查是否存在TemplateService
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    // 使用反射调用TemplateService
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true; // 成功渲染新版
                        }
                    }
                }

                // 如果Handlebars不可用，记录错误但不回退
                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("Handlebars模板服务不可用，继续使用旧版");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 RenderWithHandlebars 中 Response.End() 的正常行为
                // 表示新版渲染成功并正常终止了线程，这是期望的行为
                System.Diagnostics.Debug.WriteLine("新版渲染成功，线程正常终止");
                return true; // 实际上是成功的
            }
            catch (Exception ex)
            {
                // 记录错误但不回退到静态模板
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                System.Diagnostics.Debug.WriteLine($"模板渲染错误: {ex.Message}，继续使用旧版");
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 处理表单提交（如果有）
                ProcessFormSubmission();

                // 构建数据模型
                var pageModel = BuildModifyPasswordPageModel();
                
                // 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/ModifyPassword.hbs",
                    pageModel,
                    "修改密码",
                    new YaoHuo.Plugin.BBS.Models.HeaderOptionsModel { ShowViewModeToggle = false } // 头部选项：不显示UI切换按钮
                );
                
                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End(); // 使用Response.End()确保页面执行完全终止
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，不需要处理
                // 直接重新抛出，让它正常终止线程
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 (ModifyPW.aspx): {ex.ToString()}");
                // 记录错误但不回退到旧版，让新的TemplateService返回错误HTML信息
                ERROR = "新版界面加载失败: " + WapTool.ErrorToString(ex.ToString());
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest(); // 在异常处理中使用CompleteRequest避免嵌套异常
            }
        }

        /// <summary>
        /// 构建 ModifyPassword 页面数据模型
        /// </summary>
        /// <returns>页面数据模型</returns>
        private ModifyPasswordPageModel BuildModifyPasswordPageModel()
        {
            var model = new ModifyPasswordPageModel
            {
                PageTitle = "修改密码"
            };

            // 消息状态
            BuildMessageModel(model);

            // 表单数据
            BuildFormDataModel(model);

            // 站点信息
            BuildSiteInfoModel(model);

            return model;
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(ModifyPasswordPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.HasMessage = true;
                switch (INFO)
                {
                    case "OK":
                        model.Message.Type = "success";
                        model.Message.Content = "密码修改成功！";
                        model.Message.IsSuccess = true;
                        break;
                    case "NULL":
                        model.Message.Type = "error";
                        model.Message.Content = "密码不能为空！";
                        break;
                    case "TWOERR":
                        model.Message.Type = "error";
                        model.Message.Content = "两次新密码不一致！";
                        break;
                    case "OLDERR":
                        model.Message.Type = "error";
                        model.Message.Content = "原密码错误！";
                        break;
                    default:
                        model.Message.Type = "info";
                        model.Message.Content = INFO;
                        break;
                }
            }
        }

        /// <summary>
        /// 构建表单数据模型
        /// </summary>
        private void BuildFormDataModel(ModifyPasswordPageModel model)
        {
            model.FormData.ActionUrl = http_start + "bbs/ModifyPW.aspx";
            model.FormData.HiddenFields.Action = "gomod";
            model.FormData.HiddenFields.SiteId = siteid.ToString();
            model.FormData.HiddenFields.ClassId = classid.ToString();
        }

        /// <summary>
        /// 构建站点信息模型
        /// </summary>
        private void BuildSiteInfoModel(ModifyPasswordPageModel model)
        {
            model.SiteInfo.SiteId = siteid.ToString();
            model.SiteInfo.ClassId = classid.ToString();
            model.SiteInfo.HttpStart = http_start;
        }

        /// <summary>
        /// 初始化页面基础设置
        /// </summary>
        private void InitializePage()
        {
            // 登录验证
            IsLogin(userid, "bbs/modifyuserinfo.aspx?siteid=" + siteid);
        }

        /// <summary>
        /// 处理表单提交
        /// </summary>
        private void ProcessFormSubmission()
        {
            string action = base.Request.Form.Get("action");
            if (action == "gomod")
            {
                try
                {
                    oldpassword = GetRequestValue("txtoldPW");
                    newpassword = GetRequestValue("txtnewPW");
                    newrepassword = GetRequestValue("txtrePW");
                    
                    if (newpassword.Trim() == "")
                    {
                        INFO = "NULL";
                        return;
                    }
                    
                    if (newpassword.Trim() != newrepassword.Trim())
                    {
                        INFO = "TWOERR";
                        return;
                    }
                    
                    if (userVo.password != oldpassword && userVo.password.ToLower() != PubConstant.md5(oldpassword).ToLower())
                    {
                        INFO = "OLDERR";
                        return;
                    }
                    
                    // 使用参数化查询更新密码
                    string updateSql = "update [user] set password=@password, sidtimeout=null where siteid=@siteid and userid=@userid";
                    DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, updateSql, 
                        new SqlParameter("@password", PubConstant.md5(newpassword)),
                        new SqlParameter("@siteid", siteid),
                        new SqlParameter("@userid", userid));
                    
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = "密码修改失败：" + ex.Message;
                }
            }
        }

        /// <summary>
        /// 判断是否应该渲染新版界面
        /// </summary>
        /// <returns>true: 渲染新版; false: 渲染旧版</returns>
        private bool ShouldRenderNewVersion()
        {
            return TemplateService.GetViewMode() == "new";
        }

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        private string ConnectionString => PubConstant.GetConnectionString(a);
    }
}