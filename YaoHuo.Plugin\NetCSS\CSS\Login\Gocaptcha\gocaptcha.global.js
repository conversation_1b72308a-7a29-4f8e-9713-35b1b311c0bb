var GoCaptcha=(()=>{var Ut=Object.defineProperty;var nn=Object.getOwnPropertyDescriptor;var on=Object.getOwnPropertyNames;var rn=Object.prototype.hasOwnProperty;var sn=(e,t)=>{for(var n in t)Ut(e,n,{get:t[n],enumerable:!0})},an=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of on(t))!rn.call(e,s)&&s!==n&&Ut(e,s,{get:()=>t[s],enumerable:!(o=nn(t,s))||o.enumerable});return e};var ln=e=>an(Ut({},"__esModule",{value:!0}),e);var xn={};sn(xn,{Button:()=>le,Click:()=>he,Rotate:()=>me,Slide:()=>de,SlideRegion:()=>fe,default:()=>Sn});var dt;(function(e){e.IS_REACTIVE="__v_isReactive"})(dt||(dt={}));var et;(function(e){e[e.Dirty=4]="Dirty",e[e.NoDirty=0]="NoDirty"})(et||(et={}));var rt,be;function Se(e){e._depsLength=0,e._trackId++}function xe(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ce(e.deps[t],e);e.deps.length=e._depsLength}}var at=class{constructor(t,n){this.fn=t,this.scheduler=n,this._trackId=0,this._depsLength=0,this._running=0,this._dirtyLevel=et.Dirty,this.deps=[],this.active=!0}get dirty(){return this._dirtyLevel===et.Dirty}set dirty(t){this._dirtyLevel=t?et.Dirty:et.NoDirty}run(){if(this._dirtyLevel=et.NoDirty,!this.active)return this.fn();let t=rt;try{return rt=this,be=!0,Se(this),this._running++,this.fn()}finally{this._running--,xe(this),be=!1,rt=t}}stop(){this.active&&(this.active=!1,Se(this),xe(this))}};function Ce(e,t){e.delete(t),e.size==0&&e.cleanup()}function Lt(e,t){if(t.get(e)!==e._trackId){t.set(e,e._trackId);let n=e.deps[e._depsLength];n!==t?(n&&Ce(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}function _t(e){for(let t of e.keys())t._dirtyLevel<et.Dirty&&(t._dirtyLevel=et.Dirty),t._running||t.scheduler&&t.scheduler()}var T;(function(e){e[e.ELEMENT=1]="ELEMENT",e[e.FUNCTIONAL_COMPONENT=2]="FUNCTIONAL_COMPONENT",e[e.STATEFUL_COMPONENT=4]="STATEFUL_COMPONENT",e[e.TEXT_CHILDREN=8]="TEXT_CHILDREN",e[e.ARRAY_CHILDREN=16]="ARRAY_CHILDREN",e[e.SLOTS_CHILDREN=32]="SLOTS_CHILDREN",e[e.TELEPORT=64]="TELEPORT",e[e.COMPONENT_SHOULD_KEEP_ALIVE=128]="COMPONENT_SHOULD_KEEP_ALIVE",e[e.COMPONENT_KEPT_ALIVE=256]="COMPONENT_KEPT_ALIVE",e[e.COMPONENT=6]="COMPONENT"})(T||(T={}));var yt;(function(e){e[e.TEXT=1]="TEXT",e[e.CLASS=2]="CLASS",e[e.STYLE=4]="STYLE"})(yt||(yt={}));function U(e){return typeof e=="object"&&e!==null}function Z(e){return e!==null&&typeof e=="function"}function De(e){return typeof e=="string"}var pt=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var $t=new WeakMap,Kt=(e,t)=>{let n=new Map;return n.cleanup=e,n.name=t,n};function Le(e,t){if(!rt)return;let n=$t.get(e);n||$t.set(e,n=new Map);let o=n.get(t);o||n.set(t,o=Kt(()=>n.delete(t),t)),Lt(rt,o)}function _e(e,t,n,o){let s=$t.get(e);if(!s)return;let r=s.get(t);r&&_t(r)}var we={get(e,t,n){if(t===dt.IS_REACTIVE)return!0;Le(e,t);let o=Reflect.get(e,t,n);return U(o)?L(o):o},set(e,t,n,o){let s=e[t],r=Reflect.set(e,t,n,o);return s!==n&&_e(e,t,n,s),r}};var Re=new WeakMap;function cn(e){if(!U(e)||e[dt.IS_REACTIVE])return e;let t=Re.get(e);if(t)return t;let n=new Proxy(e,we);return Re.set(e,n),n}function L(e){return cn(e)}function Te(e){return U(e)?L(e):e}function Pe(e){return!(!e||!e[dt.IS_REACTIVE])}function W(e){return hn(e)}function hn(e){return new qt(e)}var qt=class{constructor(t){this.rawValue=t,this.__v_isRef=!0,this._value=Te(t)}get value(){return Jt(this),this._value}set value(t){t!==this.rawValue&&(this.rawValue=t,this._value=t,Gt(this))}};function Jt(e){rt&&Lt(rt,e.dep=e.dep||Kt(()=>e.dep=void 0,"undefined"))}function Gt(e){let t=e.dep;t&&_t(t)}function un(e){return ft(e)?e.value:e}function ke(e){return new Proxy(e,{get:(t,n,o)=>un(Reflect.get(t,n,o)),set:(t,n,o,s)=>ft(t[n])&&!ft(o)?t[n].value=o:Reflect.set(t,n,o,s)})}function ft(e){return e&&e.__v_isRef}var Qt=class{constructor(t,n){this.setter=n,this.effect=new at(()=>t(this._value),()=>{Gt(this)})}get value(){return Jt(this),this.effect.dirty&&(this._value=this.effect.run()),this._value}set value(t){this.setter(t)}};function w(e){let t,n;return Z(e)?(t=e,n=()=>{}):(t=e.get,n=e.set),new Qt(t,n)}function V(e,t,n={}){return dn(e,t,n)}function Oe(e,t,n=0,o=new Set){if(!U(e))return e;if(t){if(n>=t)return e;n++}if(o.has(e))return e;for(let s in e)Oe(e[s],t,n,o);return e}function dn(e,t,{deep:n,immediate:o}){let s,r,d;Pe(e)?s=()=>(P=>Oe(P,n===!1?1:void 0))(e):ft(e)?s=()=>e.value:Z(e)&&(s=e);let u=P=>{d=()=>{P(),d=void 0}},F=()=>{if(t){let P=b.run();d&&d(),t(P,r,u),r=P}else b.run()},b=new at(s,F);return t?o?F():r=b.run():b.run(),()=>{b.stop()}}var Ie=e=>e.__isTeleport;var Zt=Symbol("Text"),te=Symbol("Fragment");function ee(e){return e?.__v_isVnode}function wt(e,t){return e.type===t.type&&e.key===t.key}function gt(e,t,n,o){let s=De(e)?T.ELEMENT:Ie(e)?T.TELEPORT:U(e)?T.STATEFUL_COMPONENT:Z(e)?T.FUNCTIONAL_COMPONENT:0,r={__v_isVnode:!0,type:e,props:t,children:n,key:t?.key,el:null,shapeFlag:s,ref:t?.ref,patchFlag:o};return Fe&&o>0&&Fe.push(r),n&&(Array.isArray(n)?r.shapeFlag|=T.ARRAY_CHILDREN:U(n)?r.shapeFlag|=T.SLOTS_CHILDREN:(n=String(n),r.shapeFlag|=T.TEXT_CHILDREN)),r}var Fe=null;function a(e,t,n){let o=arguments.length;return o===2?U(t)&&!Array.isArray(t)?ee(t)?gt(e,null,[t]):gt(e,t):gt(e,null,t):(o>3&&(n=Array.from(arguments).slice(2)),o==3&&ee(n)&&(n=[n]),gt(e,t,n))}function ne(e){let t=[0],n=t.slice(0),o,s,r,d=e.length;for(let b=0;b<d;b++){let P=e[b];if(P!==0&&e[t[t.length-1]]<P){n[b]=t[t.length-1],t.push(b);continue}for(o=0,s=t.length-1;o<s;)r=(o+s)/2|0,e[t[r]]<P?o=r+1:s=r;P<e[t[o]]&&(n[b]=t[o-1],t[o]=b)}let u=t.length,F=t[u-1];for(;u-- >0;)t[u]=F,F=n[F];return t}var Rt=[],oe=!1,pn=Promise.resolve();function Ae(e){Rt.includes(e)||Rt.push(e),oe||(oe=!0,pn.then(()=>{oe=!1;let t=Rt.slice(0);Rt.length=0,t.forEach(n=>n()),t.length=0}))}function Ne(e,t){return{data:null,vnode:e,subTree:null,isMounted:!1,update:null,props:{},attrs:{},slots:{},propsOptions:e.type.props,component:null,proxy:null,setupState:{},exposed:null,parent:t,ctx:{},provides:t?t.provides:Object.create(null)}}var fn=(e,t)=>{let n={},o={},s=e.propsOptions||{};if(t)for(let r in t){let d=t[r];r in s?n[r]=d:o[r]=d}e.attrs=o,e.props=L(n)},gn={$attrs:e=>e.attrs,$slots:e=>e.slots},mn={get(e,t){let{data:n,props:o,setupState:s}=e;if(n&&pt(n,t))return n[t];if(o&&pt(o,t))return o[t];if(s&&pt(s,t))return s[t];let r=gn[t];return r?r(e):void 0},set(e,t,n){let{data:o,props:s,setupState:r}=e;if(o&&pt(o,t))o[t]=n;else{if(s&&pt(s,t))return console.warn("props are readonly"),!1;r&&pt(r,t)&&(r[t]=n)}return!0}};function vn(e,t){e.vnode.shapeFlag&T.SLOTS_CHILDREN?e.slots=t:e.slots={}}function Be(e){let{vnode:t}=e;fn(e,t.props),vn(e,t.children),e.proxy=new Proxy(e,mn);let{data:n=()=>{},render:o,setup:s}=t.type;if(s){let r={slots:e.slots,attrs:e.attrs,expose(u){e.exposed=u},emit(u,...F){let b=`on${u[0].toUpperCase()+u.slice(1)}`,P=e.vnode.props[b];P&&P(...F)}};re(e);let d=s(e.props,r);ie(),Z(d)?e.render=d:e.setupState=ke(d)}Z(n)?e.data=L(n.call(e.proxy)):console.warn("data option must be a function"),e.render||(e.render=o)}var xt=null;var re=e=>{xt=e},ie=()=>{xt=null};function Ct(e){return(t,n=xt)=>{if(n){let o=()=>{re(n),t.call(n),ie()};(n[e]||(n[e]=[])).push(o)}}}var Do=Ct("_bm"),mt=Ct("_m"),Lo=Ct("_bu"),yn=Ct("_u"),Et=Ct("_um");function bt(e){for(let t=0;t<e.length;t++){let n=e[t];Z(n)&&n()}}var Me=e=>e.type.__isKeepAlive;var St;(function(e){e.REF="ref"})(St||(St={}));var tt;(function(e){e.SVG="svg",e.RECT="rect",e.CIRCLE="circle",e.ELLIPSE="ellipse",e.LINE="line",e.POLYLINE="polyline",e.POLYGON="polygon",e.PATH="path",e.ANIMATE="animate"})(tt||(tt={}));var ze=[tt.SVG,tt.RECT,tt.CIRCLE,tt.ELLIPSE,tt.LINE,tt.POLYLINE,tt.POLYGON,tt.PATH,tt.ANIMATE],He="http://www.w3.org/2000/svg";function Ve(e){let{insert:t,remove:n,createElement:o,createElementNS:s,createText:r,setText:d,setElementText:u,parentNode:F,nextSibling:b,patchProp:P}=e,ot=i=>{if(Array.isArray(i))for(let l=0;l<i.length;l++)typeof i[l]!="string"&&typeof i[l]!="number"||(i[l]=gt(Zt,null,String(i[l])));return i},k=(i,l,c,p)=>{ot(i);for(let f=0;f<i.length;f++)N(null,i[f],l,c,p)},Y=(i,l,c)=>{!i?.props||!i?.props[St.REF]||(c.setupState[i.props[St.REF]]=l)},z=(i,l,c,p,f)=>{i===null?((v,x,_,m)=>{let{type:g,children:y,props:R,shapeFlag:E,transition:h}=v,C;if(ze.includes(g.toLowerCase())){let D=R?.xmlns||He;C=v.el=s(D,g)}else C=v.el=o(g);if(R)for(let D in R)D!==St.REF&&P(C,D,null,R[D]);E&T.TEXT_CHILDREN?u(C,y):E&T.ARRAY_CHILDREN&&k(y,C,_,m),Y(v,C,m),h&&h.beforeEnter(C),t(C,x,_),h&&h.enter(C)})(l,c,p,f):K(i,l,c,p,f)},$=(i,l)=>{for(let c=0;c<i.length;c++){let p=i[c];B(p,l)}},S=(i,l,c,p,f)=>{let v=i.children,x=ot(l.children),_=i.shapeFlag,m=l.shapeFlag;m&T.TEXT_CHILDREN?(_&T.ARRAY_CHILDREN&&$(v,f),v!==x&&u(c,x)):_&T.ARRAY_CHILDREN?m&T.ARRAY_CHILDREN?((g,y,R,E)=>{let h=0,C=g.length-1,D=y.length-1;for(;h<=C&&h<=D;){let O=g[h],I=y[h];if(!wt(O,I))break;N(O,I,R),h++}for(;h<=C&&h<=D;){let O=g[C],I=y[D];if(!wt(O,I))break;N(O,I,R),C--,D--}if(h>C){if(h<=D){let O=D+1,I=y[O]?.el;for(;h<=D;)N(null,y[h],R,I),h++}}else if(h>D){if(h<=C)for(;h<=C;)B(g[h],E),h++}else{let O=h,I=h,H=new Map,Dt=D-I+1,ve=new Array(Dt).fill(0);for(let j=I;j<=D;j++){let st=y[j];H.set(st.key,j)}for(let j=O;j<=C;j++){let st=g[j],vt=H.get(st.key);vt==null?B(st,E):(ve[vt-I]=j+1,N(st,y[vt],R))}let ye=ne(ve),Ee=ye.length-1;for(let j=Dt-1;j>=0;j--){let st=I+j,vt=y[st+1]?.el,Yt=y[st];Yt.el?j==ye[Ee]?Ee--:t(Yt.el,R,vt):N(null,Yt,R,vt)}}})(v,x,c,f):$(v,f):(_&T.TEXT_CHILDREN&&u(c,""),m&T.ARRAY_CHILDREN&&k(x,c,p,f))},K=(i,l,c,p,f)=>{let v=l.el=i.el,x=i.props||{},_=l.props||{},{patchFlag:m,dynamicChildren:g}=l;if(m){if(yt.STYLE,yt.CLASS,m&yt.TEXT&&i.children!==l.children)return u(v,l.children)}else((y,R,E)=>{for(let h in R)P(E,h,y[h],R[h]);for(let h in y)h in R||P(E,h,y[h],null)})(x,_,v);g?((y,R,E,h,C)=>{for(let D=0;D<R.dynamicChildren.length;D++)N(y.dynamicChildren[D],R.dynamicChildren[D],E,h,C)})(i,l,v,p,f):S(i,l,v,p,f)};function A(i){let{render:l,vnode:c,proxy:p,props:f,attrs:v,slots:x}=i;return c.shapeFlag&T.STATEFUL_COMPONENT?l.call(p,p):c.type(v,{slots:x})}function q(i,l,c,p){let f=new at(()=>{if(i.isMounted){let{next:x}=i;x&&((m,g)=>{m.next=null,m.vnode=g,X(m,m.props,g.props||{}),Object.assign(m.slots,g.children)})(i,x),i._bu&&bt(i._bu);let _=A(i);N(i.subTree,_,l,c,i),i.subTree=_,i._u&&bt(i._u)}else{i._bm&&bt(i._bm);let x=A(i);N(null,x,l,c,i),i.isMounted=!0,i.subTree=x,i._m&&bt(i._m)}},()=>Ae(v)),v=i.update=()=>f.run();v()}let J=(i,l)=>{let c=Object.keys(l);if(c.length!==Object.keys(i).length)return!0;for(let p=0;p<c.length;p++){let f=c[p];if(l[f]!==i[f])return!0}return!1},X=(i,l,c)=>{if(J(l,c)){for(let p in c)i.props[p]=c[p];for(let p in i.props)p in c||delete i.props[p]}},G=(i,l)=>{let c=l.component=i.component;((p,f)=>{let{props:v,children:x}=p,{props:_,children:m}=f;return!(!x&&!m)||v!==_&&J(v,_||{})})(i,l)&&(c.next=l,c.update())},Q=(i,l,c,p,f)=>{i===null?l.shapeFlag&T.COMPONENT_KEPT_ALIVE?f.ctx.activate(l,c,p):((v,x,_,m)=>{let g=v.component=Ne(v,m);Me(v)&&(g.ctx.renderer={createElement:o,createElementNS:s,move(y,R,E){t(y.component.subTree.el,R,E)},unmount:B}),Be(g),q(g,x,_)})(l,c,p,f):G(i,l)},N=(i,l,c,p=null,f=null)=>{if(i==l)return;i&&!wt(i,l)&&(B(i,f),i=null);let{type:v,shapeFlag:x,ref:_}=l;switch(v){case Zt:((m,g,y)=>{if(m==null)t(g.el=r(g.children),y);else{let R=g.el=m.el;m.children!==g.children&&d(R,g.children)}})(i,l,c);break;case te:((m,g,y,R,E)=>{m==null?k(g.children,y,R,E):S(m,g,y,R,E)})(i,l,c,p,f);break;default:x&T.ELEMENT?z(i,l,c,p,f):x&T.TELEPORT?v.process(i,l,c,p,f,{mountChildren:k,patchChildren:S,move(m,g,y){t(m.component?m.component.subTree.el:m.el,g,y)}}):x&T.COMPONENT&&Q(i,l,c,p,f)}_!==null&&function(m,g){let y=g.shapeFlag&T.STATEFUL_COMPONENT?g.component.exposed||g.component.proxy:g.el;ft(m)&&(m.value=y)}(_,l)},B=(i,l)=>{let{shapeFlag:c,transition:p,el:f}=i,v=()=>{n(i.el)};c&T.COMPONENT_SHOULD_KEEP_ALIVE?l.ctx.deactivate(i):i.type===te?$(i.children,l):c&T.COMPONENT?B(i.component.subTree,l):c&T.TELEPORT?i.type.remove(i,$):p?p.leave(f,v):v()};return{render:(i,l)=>(i==null?l._vnode&&B(l._vnode,null):(N(l._vnode||null,i,l),l._vnode=i),()=>{i&&i.component&&i.component._um&&bt(i.component._um),B(l._vnode,null),l._vnode=null})}}var We={insert:(e,t,n)=>t.insertBefore(e,n||null),remove(e){let t=e.parentNode;t&&t.removeChild(e)},createElement:e=>document.createElement(e),createElementNS:(e,t)=>document.createElementNS(e,t),createText:e=>document.createTextNode(e),setText:(e,t)=>e.nodeValue=t,setElementText:(e,t)=>e.textContent=t,parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling};function Xe(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function je(e,t){t==null?e.removeAttribute("class"):e.className=t}function En(e){let t=n=>t.value(n);return t.value=e,t}function Ye(e,t,n){let o=e._vei||(e._vei={}),s=t.slice(2).toLowerCase(),r=o[t];if(n&&r)return r.value=n;if(n){let d=o[t]=En(n);return e.addEventListener(s,d)}r&&(e.removeEventListener(s,r),o[t]=void 0)}function Ue(e,t,n){let o=e.style;for(let s in n)o[s]=n[s];if(t)for(let s in t)n&&n[s]==null&&(o[s]=null)}function se(e,t,n,o){return t==="class"?je(e,o):t==="style"?Ue(e,n,o):/^on[^a-z]/.test(t)?Ye(e,t,o):Xe(e,t,o)}var bn=Object.assign({patchProp:se},We),nt=(e,t)=>Ve(bn).render(e,t);var lt={width:22,height:22,clickEvent:()=>{}},ct=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",width:"84",height:"84"},[a("circle",{cx:"50",cy:"36.8101",r:"10"},[a("animate",{attributeName:"cy",dur:"1s",repeatCount:"indefinite",calcMode:"spline",keySplines:"0.45 0 0.9 0.55;0 0.45 0.55 0.9",keyTimes:"0;0.5;1",values:"23;77;23"})])])),ht=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height,onClick:e.clickEvent},[a("path",{d:`M100.1,189.9C100.1,189.9,100,189.9,100.1,189.9c-49.7,0-90-40.4-90-89.9c0-49.6,40.4-89.9,89.9-89.9
		c49.6,0,89.9,40.4,89.9,89.9c0,18.2-5.4,35.7-15.6,50.7c-1.5,2.1-3.6,3.4-6.1,3.9c-2.5,0.4-5-0.1-7-1.6c-4.2-3-5.3-8.6-2.4-12.9
		c8.1-11.9,12.4-25.7,12.4-40.1c0-39.2-31.9-71.1-71.1-71.1c-39.2,0-71.1,31.9-71.1,71.1c0,39.2,31.9,71.1,71.1,71.1
		c7.7,0,15.3-1.2,22.6-3.6c2.4-0.8,4.9-0.6,7.2,0.5c2.2,1.1,3.9,3.1,4.7,5.5c1.6,4.9-1,10.2-5.9,11.9
		C119.3,188.4,109.8,189.9,100.1,189.9z M73,136.4C73,136.4,73,136.4,73,136.4c-2.5,0-4.9-1-6.7-2.8c-3.7-3.7-3.7-9.6,0-13.3
		L86.7,100L66.4,79.7c-3.7-3.7-3.7-9.6,0-13.3c3.7-3.7,9.6-3.7,13.3,0L100,86.7l20.3-20.3c1.8-1.8,4.1-2.8,6.7-2.8c0,0,0,0,0,0
		c2.5,0,4.9,1,6.7,2.8c1.8,1.8,2.8,4.1,2.8,6.7c0,2.5-1,4.9-2.8,6.7L113.3,100l20.3,20.3c3.7,3.7,3.7,9.6,0,13.3
		c-3.7,3.7-9.6,3.7-13.3,0L100,113.3l-20.3,20.3C77.9,135.4,75.5,136.4,73,136.4z`})])),ut=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height,onClick:e.clickEvent},[a("path",{d:`M135,149.9c-10.7,7.6-23.2,11.4-36,11.2c-1.7,0-3.4-0.1-5-0.3c-0.7-0.1-1.4-0.2-2-0.3c-1.3-0.2-2.6-0.4-3.9-0.6
	c-0.8-0.2-1.6-0.4-2.3-0.5c-1.2-0.3-2.5-0.6-3.7-1c-0.6-0.2-1.2-0.4-1.7-0.6c-1.4-0.5-2.8-1-4.2-1.5c-0.3-0.1-0.6-0.3-0.9-0.4
	c-1.6-0.7-3.2-1.4-4.7-2.3c-0.1,0-0.1-0.1-0.2-0.1c-5.1-2.9-9.8-6.4-14-10.6c-0.1-0.1-0.1-0.1-0.2-0.2c-1.3-1.3-2.5-2.7-3.7-4.1
	c-0.2-0.3-0.5-0.6-0.7-0.9c-8.4-10.6-13.5-24.1-13.5-38.8h14.3c0.4,0,0.7-0.2,0.9-0.5c0.2-0.3,0.2-0.8,0-1.1L29.5,60.9
	c-0.2-0.3-0.5-0.5-0.9-0.5c-0.4,0-0.7,0.2-0.9,0.5L3.8,97.3c-0.2,0.3-0.2,0.7,0,1.1c0.2,0.3,0.5,0.5,0.9,0.5h14.3
	c0,17.2,5.3,33.2,14.3,46.4c0.1,0.2,0.2,0.4,0.3,0.6c0.9,1.4,2,2.6,3,3.9c0.4,0.5,0.7,1,1.1,1.5c1.5,1.8,3,3.5,4.6,5.2
	c0.2,0.2,0.3,0.3,0.5,0.5c5.4,5.5,11.5,10.1,18.2,13.8c0.2,0.1,0.3,0.2,0.5,0.3c1.9,1,3.9,2,5.9,2.9c0.5,0.2,1,0.5,1.5,0.7
	c1.7,0.7,3.5,1.3,5.2,1.9c0.8,0.3,1.7,0.6,2.5,0.8c1.5,0.5,3.1,0.8,4.7,1.2c1.1,0.2,2.1,0.5,3.2,0.7c0.4,0.1,0.9,0.2,1.3,0.3
	c1.5,0.3,3,0.4,4.5,0.6c0.5,0.1,1.1,0.2,1.6,0.2c2.7,0.3,5.4,0.4,8.1,0.4c16.4,0,32.5-5.1,46.2-14.8c4.4-3.1,5.5-9.2,2.4-13.7
	C145.5,147.8,139.4,146.7,135,149.9 M180.6,98.9c0-17.2-5.3-33.1-14.2-46.3c-0.1-0.2-0.2-0.5-0.4-0.7c-1.1-1.6-2.3-3.1-3.5-4.6
	c-0.1-0.2-0.3-0.4-0.4-0.6c-8.2-10.1-18.5-17.9-30.2-23c-0.3-0.1-0.6-0.3-1-0.4c-1.9-0.8-3.8-1.5-5.7-2.1c-0.7-0.2-1.4-0.5-2.1-0.7
	c-1.7-0.5-3.4-0.9-5.1-1.3c-0.9-0.2-1.9-0.5-2.8-0.7c-0.5-0.1-0.9-0.2-1.4-0.3c-1.3-0.2-2.6-0.3-3.8-0.5c-0.9-0.1-1.8-0.3-2.6-0.3
	c-2.1-0.2-4.3-0.3-6.4-0.3c-0.4,0-0.8-0.1-1.2-0.1c-0.1,0-0.1,0-0.2,0c-16.4,0-32.4,5-46.2,14.8C49,35,48,41.1,51,45.6
	c3.1,4.4,9.1,5.5,13.5,2.4c10.6-7.5,23-11.3,35.7-11.2c1.8,0,3.6,0.1,5.4,0.3c0.6,0.1,1.1,0.1,1.6,0.2c1.5,0.2,2.9,0.4,4.3,0.7
	c0.6,0.1,1.3,0.3,1.9,0.4c1.4,0.3,2.8,0.7,4.2,1.1c0.4,0.1,0.9,0.3,1.3,0.4c1.6,0.5,3.1,1.1,4.6,1.7c0.2,0.1,0.3,0.1,0.5,0.2
	c9,3.9,17,10,23.2,17.6c0,0,0.1,0.1,0.1,0.2c8.7,10.7,14,24.5,14,39.4H147c-0.4,0-0.7,0.2-0.9,0.5c-0.2,0.3-0.2,0.8,0,1.1l24,36.4
	c0.2,0.3,0.5,0.5,0.9,0.5c0.4,0,0.7-0.2,0.9-0.5l23.9-36.4c0.2-0.3,0.2-0.7,0-1.1c-0.2-0.3-0.5-0.5-0.9-0.5L180.6,98.9L180.6,98.9
	L180.6,98.9z`})])),Tt=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height},[a("path",{d:`M131.6,116.3c0,0-75.6,0-109.7,0c-9.1,0-16.2-7.4-16.2-16.2c0-9.1,7.4-16.2,16.2-16.2c28.7,0,109.7,0,109.7,0
	s-5.4-5.4-30.4-30.7c-6.4-6.4-6.4-16.7,0-23.1s16.7-6.4,23.1,0l58.4,58.4c6.4,6.4,6.4,16.7,0,23.1c0,0-32.9,32.9-57.9,57.9
	c-6.4,6.4-16.7,6.4-23.1,0c-6.4-6.4-6.4-16.7,0-23.1C121.8,126.2,131.6,116.3,131.6,116.3z`})])),$e=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height},[a("circle",{fill:"#3E7CFF",cx:"100",cy:"100",r:"96.3"}),a("path",{fill:"#FFFFFF",d:`M140.8,64.4l-39.6-11.9h-2.4L59.2,64.4c-1.6,0.8-2.8,2.4-2.8,4v24.1c0,25.3,15.8,45.9,42.3,54.6
  c0.4,0,0.8,0.4,1.2,0.4c0.4,0,0.8,0,1.2-0.4c26.5-8.7,42.3-28.9,42.3-54.6V68.3C143.5,66.8,142.3,65.2,140.8,64.4z`})])),Ke=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height},[a("path",{fill:"#FFA000",d:`M184,26.6L102.4,2.1h-4.9L16,26.6c-3.3,1.6-5.7,4.9-5.7,8.2v49.8c0,52.2,32.6,94.7,87.3,112.6
	c0.8,0,1.6,0.8,2.4,0.8s1.6,0,2.4-0.8c54.7-18,87.3-59.6,87.3-112.6V34.7C189.8,31.5,187.3,28.2,184,26.6z M107.3,109.1
	c-0.5,5.4-3.9,7.9-7.3,7.9c-2.5,0,0,0,0,0c-3.2-0.6-5.7-2-6.8-7.4l-4.4-50.9c0-5.1,6.2-9.7,11.5-9.7c5.3,0,11,4.7,11,9.9
	L107.3,109.1z M109.3,133.3c0,5.1-4.2,9.3-9.3,9.3c-5.1,0-9.3-4.2-9.3-9.3c0-5.1,4.2-9.3,9.3-9.3C105.1,124,109.3,128.1,109.3,133.3
	z`})])),qe=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height},[a("path",{fill:"#ED4630",d:`M184,26.6L102.4,2.1h-4.9L16,26.6c-3.3,1.6-5.7,4.9-5.7,8.2v49.8c0,52.2,32.6,94.7,87.3,112.6
	c0.8,0,1.6,0.8,2.4,0.8s1.6,0,2.4-0.8c54.7-18,87.3-59.6,87.3-112.6V34.7C189.8,31.5,187.3,28.2,184,26.6z M134.5,123.1
	c3.1,3.1,3.1,8.2,0,11.3c-1.6,1.6-3.6,2.3-5.7,2.3s-4.1-0.8-5.7-2.3L100,111.3l-23.1,23.1c-1.6,1.6-3.6,2.3-5.7,2.3
	c-2,0-4.1-0.8-5.7-2.3c-3.1-3.1-3.1-8.2,0-11.3L88.7,100L65.5,76.9c-3.1-3.1-3.1-8.2,0-11.3c3.1-3.1,8.2-3.1,11.3,0L100,88.7
	l23.1-23.1c3.1-3.1,8.2-3.1,11.3,0c3.1,3.1,3.1,8.2,0,11.3L111.3,100L134.5,123.1z`})])),Je=e=>(e={...lt,...e},a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 200 200",width:e.width,height:e.height},[a("path",{fill:"#5EAA2F",d:`M183.3,27.2L102.4,2.9h-4.9L16.7,27.2C13.4,28.8,11,32,11,35.3v49.4c0,51.8,32.4,93.9,86.6,111.7
	c0.8,0,1.6,0.8,2.4,0.8c0.8,0,1.6,0,2.4-0.8c54.2-17.8,86.6-59.1,86.6-111.7V35.3C189,32,186.6,28.8,183.3,27.2z M146.1,81.4
	l-48.5,48.5c-1.6,1.6-3.2,2.4-5.7,2.4c-2.4,0-4-0.8-5.7-2.4L62,105.7c-3.2-3.2-3.2-8.1,0-11.3c3.2-3.2,8.1-3.2,11.3,0l18.6,18.6
	l42.9-42.9c3.2-3.2,8.1-3.2,11.3,0C149.4,73.3,149.4,78.2,146.1,81.4L146.1,81.4z`})]));var Pt=()=>({width:330,height:44,verticalPadding:12,horizontalPadding:16}),kt=()=>({type:"default",disabled:!1,title:"Click Button",clickEvent:()=>{}});function Ge(e){let t=0,n=0;if(e.getBoundingClientRect){let o=e.getBoundingClientRect(),s=document.documentElement;t=o.left+Math.max(s.scrollLeft,document.body.scrollLeft)-s.clientLeft,n=o.top+Math.max(s.scrollTop,document.body.scrollTop)-s.clientTop}else for(;e!==document.body;)t+=e.offsetLeft,n+=e.offsetTop,e=e.offsetParent;return{domX:t,domY:n}}function it(e,t){let n=t.relatedTarget;try{for(;n&&n!==e;)n=n.parentNode}catch(o){console.warn(o)}return n!==e}var Cr=Array.isArray;function M(e){let t=JSON.stringify(e),n;try{n=JSON.parse(t)}catch{n=e}return n}var Ot=class{constructor(){this.name="ButtonComponent";this.logicProps=L({config:{...Pt()},state:{...kt()}});this.localConfig=L({...M(this.logicProps.config)});this.localState=L({...M(this.logicProps.state)});this.setPropsConfig=t=>{this.logicProps.config=t};this.setPropsState=t=>{this.logicProps.state=t};this.setup=()=>{let t=this.localConfig;return this.btnClass=w(()=>{let n=this.localState.type,o=this.localState.disabled;return["go-captcha","gc-button-mode","gc-btn-block",`gc-${n}`,o?"gc-disabled":""].join(" ")}),this.btnStyles=w(()=>{let n=t.width,o=t.height,s=t.horizontalPadding,r=t.verticalPadding;return{width:n+"px",height:o+"px",paddingLeft:s+"px",paddingRight:s+"px",paddingTop:r+"px",paddingBottom:r+"px"}}),{}};this.render=t=>{let n=this.localState,o=$e();return n.value==="warn"?o=Ke():n.value==="error"?o=qe():n.value==="success"&&(o=Je()),a("div",{class:this.btnClass.value,style:this.btnStyles.value,onClick:this.localState.clickEvent},[a("div",{class:`${this.localState.type==="default"?"gc-ripple":""}`},[o]),a("span",{},[this.localState.title])])};this.mount=t=>{this.unmountFn=nt(a(this),t)};this.unmount=()=>{this.unmountFn&&this.unmountFn()};V(()=>this.logicProps.config,(t,n)=>{Object.assign(this.localConfig,t)},{deep:!0}),V(()=>this.logicProps.state,(t,n)=>{Object.assign(this.localState,t)},{deep:!0})}};var ae=class{constructor(t={}){this.setConfig=(t={})=>(t={...Pt(),...t},this._logic.setPropsConfig(t),this);this.setState=(t={})=>(t={...kt(),...t},this._logic.setPropsState(t),this);this.mount=t=>{if(!this._el)return this._logic.mount(t),this._el=t,this};this.destroy=()=>(this._logic.unmount(),this._el=null,this);this._logic=new Ot,this.setConfig(t)}};function le(e={}){let t=new ae(e);return{setConfig:t.setConfig,setState:t.setState,mount:t.mount,destroy:t.destroy}}var It=()=>({width:300,height:220,thumbWidth:150,thumbHeight:40,verticalPadding:16,horizontalPadding:12,showTheme:!0,title:"\u8BF7\u5728\u4E0B\u56FE\u4F9D\u6B21\u70B9\u51FB",buttonText:"\u786E\u8BA4",iconSize:22,dotSize:24}),Ft=()=>({image:"",thumb:""});var Qe=(e,t,n)=>{let o=W([]),s=k=>{let Y=k.currentTarget,z=Ge(Y),$=k.pageX||k.clientX,S=k.pageY||k.clientY,K=z.domX,A=z.domY,q=$-K,J=S-A,X=parseInt(q.toString()),G=parseInt(J.toString()),Q=new Date,N=o.value.length,B={key:Q.getTime(),index:N+1,x:X,y:G};return o.value=[...o.value,B],t.click&&t.click(X,G),k.cancelBubble=!0,k.preventDefault(),!1},r=k=>{let Y=M(o.value);return t.confirm&&t.confirm(Y,()=>{P()}),k.cancelBubble=!0,k.preventDefault(),!1},d=k=>(F(),k.cancelBubble=!0,k.preventDefault(),!1),u=k=>(b(),k.cancelBubble=!0,k.preventDefault(),!1),F=()=>{t.close&&t.close(),P()},b=()=>{t.refresh&&t.refresh(),P()},P=()=>{o.value=[]};return{dots:o,clickEvent:s,confirmEvent:r,closeEvent:d,refreshEvent:u,resetData:P,clearData:()=>{P(),n&&n()},refresh:b,close:F}};var At=class{constructor(){this.name="ClickComponent";this.logicProps=L({data:{...Ft()},event:{},config:{...It()}});this.localData=L({...M(this.logicProps.data)});this.localEvent=L({...M(this.logicProps.event)});this.localConfig=L({...M(this.logicProps.config)});this.clear=()=>{this.handler.clearData()};this.reset=()=>{this.handler.resetData()};this.refresh=()=>{this.handler.refresh()};this.close=()=>{this.handler.close()};this.clearData=()=>{this.localData.image="",this.localData.thumb=""};this.setPropsData=t=>{this.logicProps.data=t};this.setPropsEvent=t=>{this.logicProps.event=t};this.setPropsConfig=t=>{this.logicProps.config=t};this.setup=()=>{let t=this.localData,n=this.localEvent,o=this.localConfig;return this.handler=Qe(t,n,this.clearData),this.wrapperStyles=w(()=>{let s=o.horizontalPadding||0,r=o.verticalPadding||0;return{width:(o.width||0)+s*2+(o.showTheme?2:0)+"px",paddingLeft:s+"px",paddingRight:s+"px",paddingTop:r+"px",paddingBottom:r+"px",display:this.hasDisplayWrapperState.value?"inherit":"none"}}),this.hasDisplayImageState=w(()=>t.image&&t.image.length>0||t.thumb&&t.thumb.length>0),this.hasDisplayWrapperState=w(()=>(o.width||0)>0||(o.height||0)>0),this.bodyStyles=w(()=>({width:o.width+"px",height:o.height+"px"})),this.thumbStyles=w(()=>({width:o.thumbWidth+"px",height:o.thumbHeight+"px",display:this.hasDisplayImageState.value?"inherit":"none"})),this.imageStyles=w(()=>({width:o.width+"px",height:o.height+"px",display:this.hasDisplayImageState.value?"inherit":"none"})),this.iconStyles=w(()=>{let s=o.iconSize;return{width:s,height:s}}),{}};this.render=t=>a("div",{class:`go-captcha gc-click-mode gc-wrapper ${this.localConfig.showTheme&&"gc-theme"}`,style:this.wrapperStyles.value},[a("div",{class:"gc-header"},[a("span",{},[this.localConfig.title]),a("img",{style:this.thumbStyles.value,src:this.localData.thumb})]),a("div",{class:"gc-body",style:this.bodyStyles.value},[a("div",{class:"gc-loading"},[ct()]),a("img",{class:"gc-picture",style:this.imageStyles.value,src:this.localData.image,onClick:this.handler.clickEvent}),a("div",{class:"gc-dots"},[...this.handler.dots.value.map(n=>a("div",{class:"gc-dot",key:`${n.key+"-"+n.index}`,style:{width:this.localConfig.dotSize+"px",height:this.localConfig.dotSize+"px",borderRadius:this.localConfig.dotSize+"px",top:n.y-(this.localConfig.dotSize||1)/2-1+"px",left:n.x-(this.localConfig.dotSize||1)/2-1+"px"}},[n.index]))])]),a("div",{class:"gc-footer"},[a("div",{class:"gc-icon-block"},[ht({...this.iconStyles.value,clickEvent:this.handler.closeEvent}),ut({...this.iconStyles.value,clickEvent:this.handler.refreshEvent})]),a("div",{class:"gc-button-block"},[a("button",{class:!this.hasDisplayImageState.value&&"disabled",onClick:this.handler.confirmEvent},[this.localConfig.buttonText])])])]);this.mount=t=>{this.unmountFn=nt(a(this),t)};this.unmount=()=>{this.unmountFn&&this.unmountFn()};V(()=>this.logicProps.config,(t,n)=>{Object.assign(this.localConfig,t)},{deep:!0}),V(()=>this.logicProps.data,(t,n)=>{Object.assign(this.localData,t)},{deep:!0}),V(()=>this.logicProps.event,(t,n)=>{Object.assign(this.localEvent,t)},{deep:!0})}};var ce=class{constructor(t={}){this.setConfig=(t={})=>(t={...It(),...t},this._logic.setPropsConfig(t),this);this.setData=(t={})=>(t={...Ft(),...t},this._logic.setPropsData(t),this);this.setEvents=(t={})=>(t={...t},this._logic.setPropsEvent(t),this);this.mount=t=>{if(!this._el)return this._logic.mount(t),this._el=t,this};this.destroy=()=>(this._logic.unmount(),this._el=null,this);this.clear=()=>{this._logic.clear()};this.reset=()=>{this._logic.reset()};this.refresh=()=>{this._logic.refresh()};this.close=()=>{this._logic.close()};this._logic=new At,this.setConfig(t)}};function he(e={}){let t=new ce(e);return{setConfig:t.setConfig,setData:t.setData,setEvents:t.setEvents,mount:t.mount,destroy:t.destroy,clear:t.clear,reset:t.reset,refresh:t.refresh,close:t.close}}var Nt=()=>({width:300,height:220,thumbWidth:150,thumbHeight:40,verticalPadding:16,horizontalPadding:12,showTheme:!0,title:"\u8BF7\u62D6\u52A8\u6ED1\u5757\u5B8C\u6210\u62FC\u56FE",iconSize:22,scope:!0}),Bt=()=>({thumbX:0,thumbY:0,thumbWidth:0,thumbHeight:0,image:"",thumb:""});function Ze(e,t,n,o,s,r,d,u,F){let b=L({dragLeft:0,thumbLeft:e.thumbX||0,isFreeze:!1}),P=A=>{b.isFreeze||(b.thumbLeft=A.thumbX||0)},ot=A=>{if(!it(u.value,A))return;let q=A.touches&&A.touches[0],J=d.value.offsetLeft,X=s.value.offsetWidth,G=d.value.offsetWidth,Q=X-G,N=r.value.offsetWidth,B=r.value.offsetLeft,i=X-N,c=(X-(N+B))/Q,p=!1,f=null,v=0,x=0;q?v=q.pageX-J:v=A.clientX-J;let _=O=>{p=!0;let I=O.touches&&O.touches[0],H=0;I?H=I.pageX-v:H=O.clientX-v;let Dt=B+H*c;if(H>=Q){b.dragLeft=Q,b.thumbLeft=x=i;return}if(H<=0){b.dragLeft=0,b.thumbLeft=x=B;return}b.dragLeft=H,b.thumbLeft=x=Dt,t.move&&t.move(x,e.thumbY||0),O.cancelBubble=!0,O.preventDefault()},m=O=>{it(u.value,O)&&(D(),p&&(p=!1,!(x<0)&&(t.confirm&&t.confirm({x:parseInt(x.toString()),y:e.thumbY||0},()=>{S()}),O.cancelBubble=!0,O.preventDefault())))},g=O=>{f=O},y=()=>{f=null},R=O=>{f&&(m(f),D())},E=n.scope,h=E?o.value:u.value,C=E?o.value:document.body,D=()=>{C.removeEventListener("mousemove",_,!1),C.removeEventListener("touchmove",_,{passive:!1}),h.removeEventListener("mouseup",m,!1),h.removeEventListener("mouseenter",y,!1),h.removeEventListener("mouseleave",g,!1),h.removeEventListener("touchend",m,!1),C.removeEventListener("mouseleave",m,!1),C.removeEventListener("mouseup",R,!1),b.isFreeze=!1};b.isFreeze=!0,C.addEventListener("mousemove",_,!1),C.addEventListener("touchmove",_,{passive:!1}),h.addEventListener("mouseup",m,!1),h.addEventListener("mouseenter",y,!1),h.addEventListener("mouseleave",g,!1),h.addEventListener("touchend",m,!1),C.addEventListener("mouseleave",m,!1),C.addEventListener("mouseup",R,!1)},k=A=>(z(),A.cancelBubble=!0,A.preventDefault(),!1),Y=A=>($(),A.cancelBubble=!0,A.preventDefault(),!1),z=()=>{t&&t.close&&t.close(),S()},$=()=>{t&&t.refresh&&t.refresh(),S()},S=()=>{b.dragLeft=0,b.thumbLeft=e.thumbX||0};return{state:b,updateData:P,dragEvent:ot,closeEvent:k,refreshEvent:Y,resetData:S,clearData:()=>{F&&F(),S()},refresh:$,close:z}}var Mt=class{constructor(){this.name="SlideComponent";this.logicProps=L({data:{...Bt()},event:{},config:{...Nt()}});this.localData=L({...M(this.logicProps.data)});this.localEvent=L({...M(this.logicProps.event)});this.localConfig=L({...M(this.logicProps.config)});this.rootRef=W(null);this.dragBarRef=W(null);this.containerRef=W(null);this.dragBlockRef=W(null);this.tileRef=W(null);this.clear=()=>{this.handler.clearData()};this.reset=()=>{this.handler.resetData()};this.refresh=()=>{this.handler.refresh()};this.close=()=>{this.handler.close()};this.clearData=()=>{this.localData.thumb="",this.localData.image="",this.localData.thumbX=0,this.localData.thumbY=0,this.localData.thumbWidth=0,this.localData.thumbHeight=0};this.setPropsData=t=>{this.logicProps.data=t};this.setPropsEvent=t=>{this.logicProps.event=t};this.setPropsConfig=t=>{this.logicProps.config=t};this.setup=()=>{let t=this.localData,n=this.localEvent,o=this.localConfig;this.wrapperStyles=w(()=>{let r=o.horizontalPadding||0,d=o.verticalPadding||0;return{width:(o.width||0)+r*2+(o.showTheme?2:0)+"px",paddingLeft:r+"px",paddingRight:r+"px",paddingTop:d+"px",paddingBottom:d+"px",display:this.hasDisplayWrapperState.value?"inherit":"none"}}),this.hasDisplayImageState=w(()=>t.image&&t.image.length>0||t.thumb&&t.thumb.length>0),this.hasDisplayWrapperState=w(()=>(o.width||0)>0||(o.height||0)>0),this.bodyStyles=w(()=>{let r=o.width,d=o.height;return{width:r+"px",height:d+"px"}}),this.thumbStyles=w(()=>{let r=this.handler.state.thumbLeft,d=this.hasDisplayImageState.value,u=t.thumbWidth||o.thumbWidth,F=t.thumbHeight||o.thumbHeight;return{width:u+"px",height:F+"px",display:d?"inherit":"none",top:t.thumbY+"px",left:r+"px"}}),this.imageStyles=w(()=>{let r=o.width,d=o.height;return{width:r+"px",height:d+"px",display:this.hasDisplayImageState.value?"inherit":"none"}}),this.iconStyles=w(()=>{let r=o.iconSize;return{width:r,height:r}});let s=r=>r.preventDefault();return mt(async()=>{this.dragBlockRef?.value&&this.dragBlockRef.value.addEventListener("dragstart",s)}),Et(()=>{this.dragBlockRef?.value&&this.dragBlockRef.value.removeEventListener("dragstart",s)}),{rootRef:this.rootRef,containerRef:this.containerRef,tileRef:this.tileRef,dragBlockRef:this.dragBlockRef,dragBarRef:this.dragBarRef}};this.render=t=>a("div",{class:`go-captcha gc-slide-mode gc-wrapper ${this.localConfig.showTheme&&"gc-theme"}`,style:this.wrapperStyles.value,ref:"rootRef"},[a("div",{class:"gc-header"},[a("span",{},[this.localConfig.title]),a("div",{class:"gc-icon-block"},[ht({...this.iconStyles.value,clickEvent:this.handler.closeEvent}),ut({...this.iconStyles.value,clickEvent:this.handler.refreshEvent})])]),a("div",{class:"gc-body",style:this.bodyStyles.value,ref:"containerRef"},[a("div",{class:"gc-loading"},[ct()]),a("img",{class:"gc-picture",style:this.imageStyles.value,src:this.localData.image}),a("div",{class:"gc-tile",ref:"tileRef",style:this.thumbStyles.value},[a("img",{style:{display:this.hasDisplayImageState.value?"inherit":"none"},src:this.localData.thumb})])]),a("div",{class:"gc-footer"},[a("div",{class:"gc-drag-slide-bar",ref:"dragBarRef"},[a("div",{class:"gc-drag-line"}),a("div",{class:`gc-drag-block ${this.hasDisplayImageState.value?"":"disabled"}`,style:{left:this.handler.state.dragLeft+"px"},ref:"dragBlockRef",onMousedown:this.handler.dragEvent},[a("div",{class:"gc-drag-block-inline",onTouchstart:this.handler.dragEvent},[Tt()])])])])]);this.mount=t=>{this.unmountFn=nt(a(this),t)};this.unmount=()=>{this.unmountFn&&this.unmountFn()};V(()=>this.logicProps.config,(t,n)=>{Object.assign(this.localConfig,t)},{deep:!0}),V(()=>this.logicProps.data,(t,n)=>{Object.assign(this.localData,t),this.handler?.updateData(this.localData)},{deep:!0}),V(()=>this.logicProps.event,(t,n)=>{Object.assign(this.localEvent,t)},{deep:!0}),this.handler=Ze(this.localData,this.localEvent,this.localConfig,this.rootRef,this.containerRef,this.tileRef,this.dragBlockRef,this.dragBarRef,this.clearData)}};var ue=class{constructor(t={}){this.setConfig=(t={})=>(t={...Nt(),...t},this._logic.setPropsConfig(t),this);this.setData=(t={})=>(t={...Bt(),...t},this._logic.setPropsData(t),this);this.setEvents=(t={})=>(t={...t},this._logic.setPropsEvent(t),this);this.mount=t=>{if(!this._el)return this._logic.mount(t),this._el=t,this};this.destroy=()=>(this._logic.unmount(),this._el=null,this);this.clear=()=>{this._logic.clear()};this.reset=()=>{this._logic.reset()};this.refresh=()=>{this._logic.refresh()};this.close=()=>{this._logic.close()};this._logic=new Mt,this.setConfig(t)}};function de(e={}){let t=new ue(e);return{setConfig:t.setConfig,setData:t.setData,setEvents:t.setEvents,mount:t.mount,destroy:t.destroy,clear:t.clear,reset:t.reset,refresh:t.refresh,close:t.close}}var zt=()=>({width:300,height:220,verticalPadding:16,horizontalPadding:12,showTheme:!0,title:"\u8BF7\u62D6\u62FD\u8D34\u56FE\u5B8C\u6210\u62FC\u56FE",iconSize:22,scope:!0}),Ht=()=>({thumbX:0,thumbY:0,thumbWidth:0,thumbHeight:0,image:"",thumb:""});function tn(e,t,n,o,s,r,d){let u=L({x:e.thumbX||0,y:e.thumbY||0,isFreeze:!1}),F=S=>{u.isFreeze||(u.x=S.thumbX||0,u.y=S.thumbY||0)},b=S=>{if(!it(s.value,S))return;let K=S.touches&&S.touches[0],A=r.value.offsetLeft,q=r.value.offsetTop,J=s.value.offsetWidth,X=s.value.offsetHeight,G=r.value.offsetWidth,Q=r.value.offsetHeight,N=J-G,B=X-Q,i=!1,l=null,c=0,p=0,f=0,v=0;K?(c=K.pageX-A,p=K.pageY-q):(c=S.clientX-A,p=S.clientY-q);let x=D=>{i=!0;let O=D.touches&&D.touches[0],I=0,H=0;O?(I=O.pageX-c,H=O.pageY-p):(I=D.clientX-c,H=D.clientY-p),I<=0&&(I=0),H<=0&&(H=0),I>=N&&(I=N),H>=B&&(H=B),u.x=I,u.y=H,f=I,v=H,t.move&&t.move(I,H),D.cancelBubble=!0,D.preventDefault()},_=D=>{it(s.value,D)&&(C(),i&&(i=!1,!(f<0||v<0)&&(t.confirm&&t.confirm({x:f,y:v},()=>{z()}),D.cancelBubble=!0,D.preventDefault())))},m=D=>{l=D},g=()=>{l=null},y=D=>{l&&(_(l),C())},R=n.scope,E=R?o.value:s.value,h=R?o.value:document.body,C=()=>{h.removeEventListener("mousemove",x,!1),h.removeEventListener("touchmove",x,{passive:!1}),E.removeEventListener("mouseup",_,!1),E.removeEventListener("mouseenter",g,!1),E.removeEventListener("mouseleave",m,!1),E.removeEventListener("touchend",_,!1),h.removeEventListener("mouseleave",_,!1),h.removeEventListener("mouseup",y,!1),u.isFreeze=!1};u.isFreeze=!0,h.addEventListener("mousemove",x,!1),h.addEventListener("touchmove",x,{passive:!1}),E.addEventListener("mouseup",_,!1),E.addEventListener("mouseenter",g,!1),E.addEventListener("mouseleave",m,!1),E.addEventListener("touchend",_,!1),h.addEventListener("mouseleave",_,!1),h.addEventListener("mouseup",y,!1)},P=S=>(k(),S.cancelBubble=!0,S.preventDefault(),!1),ot=S=>(Y(),S.cancelBubble=!0,S.preventDefault(),!1),k=()=>{t&&t.close&&t.close(),z()},Y=()=>{t&&t.refresh&&t.refresh(),z()},z=()=>{u.x=e.thumbX||0,u.y=e.thumbY||0};return{state:u,updateData:F,dragEvent:b,closeEvent:P,refreshEvent:ot,resetData:z,clearData:()=>{d&&d(),z()},refresh:Y,close:k}}var Vt=class{constructor(){this.name="SlideRegionComponent";this.logicProps=L({data:{...Ht()},event:{},config:{...zt()}});this.localData=L({...M(this.logicProps.data)});this.localEvent=L({...M(this.logicProps.event)});this.localConfig=L({...M(this.logicProps.config)});this.rootRef=W(null);this.containerRef=W(null);this.tileRef=W(null);this.clear=()=>{this.handler.clearData()};this.reset=()=>{this.handler.resetData()};this.refresh=()=>{this.handler.refresh()};this.close=()=>{this.handler.close()};this.clearData=()=>{this.localData.thumb="",this.localData.image="",this.localData.thumbX=0,this.localData.thumbY=0,this.localData.thumbWidth=0,this.localData.thumbHeight=0};this.setPropsData=t=>{this.logicProps.data=t};this.setPropsEvent=t=>{this.logicProps.event=t};this.setPropsConfig=t=>{this.logicProps.config=t};this.setup=()=>{let t=this.localData,n=this.localEvent,o=this.localConfig;this.wrapperStyles=w(()=>{let r=o.horizontalPadding||0,d=o.verticalPadding||0;return{width:(o.width||0)+r*2+(o.showTheme?2:0)+"px",paddingLeft:r+"px",paddingRight:r+"px",paddingTop:d+"px",paddingBottom:d+"px",display:this.hasDisplayWrapperState.value?"inherit":"none"}}),this.hasDisplayImageState=w(()=>t.image&&t.image.length>0||t.thumb&&t.thumb.length>0),this.hasDisplayWrapperState=w(()=>(o.width||0)>0||(o.height||0)>0),this.bodyStyles=w(()=>{let r=o.width,d=o.height;return{width:r+"px",height:d+"px"}}),this.thumbStyles=w(()=>{let r=this.handler.state.x,d=this.handler.state.y,u=this.hasDisplayImageState.value,F=t.thumbWidth||o.thumbWidth,b=t.thumbHeight||o.thumbHeight;return{width:F+"px",height:b+"px",display:u?"inherit":"none",top:d+"px",left:r+"px"}}),this.imageStyles=w(()=>{let r=o.width,d=o.height;return{width:r+"px",height:d+"px",display:this.hasDisplayImageState.value?"inherit":"none"}}),this.iconStyles=w(()=>{let r=o.iconSize;return{width:r,height:r}});let s=r=>r.preventDefault();return mt(async()=>{this.tileRef?.value&&this.tileRef.value.addEventListener("dragstart",s)}),Et(()=>{this.tileRef?.value&&this.tileRef.value.removeEventListener("dragstart",s)}),{rootRef:this.rootRef,containerRef:this.containerRef,tileRef:this.tileRef}};this.render=t=>a("div",{class:`go-captcha gc-slide-mode gc-wrapper ${this.localConfig.showTheme&&"gc-theme"}`,style:this.wrapperStyles.value,ref:"rootRef"},[a("div",{class:"gc-header"},[a("span",{style:{"text-align":"center",padding:"0"}},[this.localConfig.title])]),a("div",{class:"gc-body",style:this.bodyStyles.value,ref:"containerRef"},[a("div",{class:"gc-loading"},[ct()]),a("img",{class:"gc-picture",style:this.imageStyles.value,src:this.localData.image}),a("div",{class:"gc-tile",ref:"tileRef",style:this.thumbStyles.value,onMousedown:this.handler.dragEvent,onTouchstart:this.handler.dragEvent},[a("img",{style:{display:this.hasDisplayImageState.value?"inherit":"none"},src:this.localData.thumb})])]),a("div",{class:"gc-footer"},[a("div",{class:"gc-icon-block"},[a("div",{class:"gc-icon-block"},[ht({...this.iconStyles.value,clickEvent:this.handler.closeEvent}),ut({...this.iconStyles.value,clickEvent:this.handler.refreshEvent})])])])]);this.mount=t=>{this.unmountFn=nt(a(this),t)};this.unmount=()=>{this.unmountFn&&this.unmountFn()};V(()=>this.logicProps.config,(t,n)=>{Object.assign(this.localConfig,t)},{deep:!0}),V(()=>this.logicProps.data,(t,n)=>{Object.assign(this.localData,t),this.handler?.updateData(this.localData)},{deep:!0}),V(()=>this.logicProps.event,(t,n)=>{Object.assign(this.localEvent,t)},{deep:!0}),this.handler=tn(this.localData,this.localEvent,this.localConfig,this.rootRef,this.containerRef,this.tileRef,this.clearData)}};var pe=class{constructor(t={}){this.setConfig=(t={})=>(t={...zt(),...t},this._logic.setPropsConfig(t),this);this.setData=(t={})=>(t={...Ht(),...t},this._logic.setPropsData(t),this);this.setEvents=(t={})=>(t={...t},this._logic.setPropsEvent(t),this);this.mount=t=>{if(!this._el)return this._logic.mount(t),this._el=t,this};this.destroy=()=>(this._logic.unmount(),this._el=null,this);this.clear=()=>{this._logic.clear()};this.reset=()=>{this._logic.reset()};this.refresh=()=>{this._logic.refresh()};this.close=()=>{this._logic.close()};this._logic=new Vt,this.setConfig(t)}};function fe(e={}){let t=new pe(e);return{setConfig:t.setConfig,setData:t.setData,setEvents:t.setEvents,mount:t.mount,destroy:t.destroy,clear:t.clear,reset:t.reset,refresh:t.refresh,close:t.close}}var Wt=()=>({width:300,height:220,size:220,verticalPadding:16,horizontalPadding:12,showTheme:!0,title:"\u8BF7\u62D6\u52A8\u6ED1\u5757\u5B8C\u6210\u62FC\u56FE",iconSize:22,scope:!0}),Xt=()=>({angle:0,image:"",thumb:"",thumbSize:0});function en(e,t,n,o,s,r,d){let u=L({dragLeft:0,thumbAngle:e.angle||0,isFreeze:!1}),F=S=>{u.isFreeze||(u.thumbAngle=S.angle||0)},b=S=>{if(!it(r.value,S))return;let K=S.touches&&S.touches[0],A=s.value.offsetLeft,q=r.value.offsetWidth,J=s.value.offsetWidth,X=q-J,G=360,Q=(G-e.angle)/X,N=0,B=!1,i=null,l=0,c=0;K?l=K.pageX-A:l=S.clientX-A;let p=E=>{B=!0;let h=E.touches&&E.touches[0],C=0;if(h?C=h.pageX-l:C=E.clientX-l,N=e.angle+C*Q,C>=X){u.dragLeft=X,u.thumbAngle=c=G;return}if(C<=0){u.dragLeft=0,u.thumbAngle=c=e.angle;return}u.dragLeft=C,u.thumbAngle=c=N,t.rotate&&t.rotate(N),E.cancelBubble=!0,E.preventDefault()},f=E=>{it(r.value,E)&&(R(),B&&(B=!1,!(c<0)&&(t.confirm&&t.confirm(parseInt(c.toString()),()=>{z()}),E.cancelBubble=!0,E.preventDefault())))},v=E=>{i=E},x=()=>{i=null},_=E=>{i&&(f(i),R())},m=n.scope,g=m?o.value:r.value,y=m?o.value:document.body,R=()=>{y.removeEventListener("mousemove",p,!1),y.removeEventListener("touchmove",p,{passive:!1}),g.removeEventListener("mouseup",f,!1),g.removeEventListener("mouseenter",x,!1),g.removeEventListener("mouseleave",v,!1),g.removeEventListener("touchend",f,!1),y.removeEventListener("mouseleave",f,!1),y.removeEventListener("mouseup",_,!1),u.isFreeze=!1};u.isFreeze=!0,y.addEventListener("mousemove",p,!1),y.addEventListener("touchmove",p,{passive:!1}),g.addEventListener("mouseup",f,!1),g.addEventListener("mouseenter",x,!1),g.addEventListener("mouseleave",v,!1),g.addEventListener("touchend",f,!1),y.addEventListener("mouseleave",f,!1),y.addEventListener("mouseup",_,!1)},P=S=>(k(),S.cancelBubble=!0,S.preventDefault(),!1),ot=S=>(Y(),S.cancelBubble=!0,S.preventDefault(),!1),k=()=>{t&&t.close&&t.close(),z()},Y=()=>{t&&t.refresh&&t.refresh(),z()},z=()=>{u.dragLeft=0,u.thumbAngle=e.angle};return{state:u,updateData:F,dragEvent:b,closeEvent:P,refreshEvent:ot,resetData:z,clearData:()=>{d&&d(),z()},refresh:Y,close:k}}var jt=class{constructor(){this.name="RotateComponent";this.logicProps=L({data:{...Xt()},event:{},config:{...Wt()}});this.localData=L({...M(this.logicProps.data)});this.localEvent=L({...M(this.logicProps.event)});this.localConfig=L({...M(this.logicProps.config)});this.rootRef=W(null);this.dragBarRef=W(null);this.dragBlockRef=W(null);this.clear=()=>{this.handler.clearData()};this.reset=()=>{this.handler.resetData()};this.refresh=()=>{this.handler.refresh()};this.close=()=>{this.handler.close()};this.clearData=()=>{this.localData.thumb="",this.localData.image="",this.localData.angle=0};this.setPropsData=t=>{this.logicProps.data=t};this.setPropsEvent=t=>{this.logicProps.event=t};this.setPropsConfig=t=>{this.logicProps.config=t};this.setup=()=>{let t=this.localData,n=this.localEvent,o=this.localConfig;this.wrapperStyles=w(()=>{let r=o.horizontalPadding||0,d=o.verticalPadding||0;return{width:(o.width||0)+r*2+(o.showTheme?2:0)+"px",paddingLeft:r+"px",paddingRight:r+"px",paddingTop:d+"px",paddingBottom:d+"px",display:this.hasDisplayWrapperState.value?"inherit":"none"}}),this.hasDisplayImageState=w(()=>t.image&&t.image.length>0||t.thumb&&t.thumb.length>0),this.hasDisplayWrapperState=w(()=>(o.width||0)>0||(o.height||0)>0),this.bodyStyles=w(()=>{let r=o.width,d=o.height;return{width:r+"px",height:d+"px"}}),this.thumbStyles=w(()=>({transform:`rotate(${this.handler.state.thumbAngle}deg)`,...t.thumbSize>0?{width:t.thumbSize+"px",height:t.thumbSize+"px"}:{}})),this.imageStyles=w(()=>{let r=o.size;return{width:r+"px",height:r+"px"}}),this.iconStyles=w(()=>{let r=o.iconSize;return{width:r,height:r}});let s=r=>r.preventDefault();return mt(async()=>{this.dragBlockRef?.value&&this.dragBlockRef.value.addEventListener("dragstart",s)}),Et(()=>{this.dragBlockRef?.value&&this.dragBlockRef.value.removeEventListener("dragstart",s)}),{rootRef:this.rootRef,dragBlockRef:this.dragBlockRef,dragBarRef:this.dragBarRef}};this.render=t=>a("div",{class:`go-captcha gc-rotate-mode gc-wrapper ${this.localConfig.showTheme&&"gc-theme"}`,style:this.wrapperStyles.value,ref:"rootRef"},[a("div",{class:"gc-header"},[a("span",{},[this.localConfig.title]),a("div",{class:"gc-icon-block"},[ht({...this.iconStyles.value,clickEvent:this.handler.closeEvent}),ut({...this.iconStyles.value,clickEvent:this.handler.refreshEvent})])]),a("div",{class:"gc-body",style:this.bodyStyles.value,ref:"containerRef"},[a("div",{class:"gc-body-inner",style:this.imageStyles.value},[a("div",{class:"gc-loading"},[ct()]),a("div",{class:"gc-picture gc-rotate-picture",style:this.imageStyles.value},[a("img",{style:{display:this.hasDisplayImageState.value?"inherit":"none"},src:this.localData.image}),a("div",{class:"gc-round"})]),a("div",{class:"gc-thumb gc-rotate-thumb"},[a("div",{class:"gc-rotate-thumb-block",style:this.thumbStyles.value},[a("img",{style:{display:this.hasDisplayImageState.value?"inherit":"none"},src:this.localData.thumb})])])])]),a("div",{class:"gc-footer"},[a("div",{class:"gc-drag-slide-bar",ref:"dragBarRef"},[a("div",{class:"gc-drag-line"}),a("div",{class:`gc-drag-block ${this.hasDisplayImageState.value?"":"disabled"}`,style:{left:this.handler.state.dragLeft+"px"},ref:"dragBlockRef",onMousedown:this.handler.dragEvent},[a("div",{class:"gc-drag-block-inline",onTouchstart:this.handler.dragEvent},[Tt()])])])])]);this.mount=t=>{this.unmountFn=nt(a(this),t)};this.unmount=()=>{this.unmountFn&&this.unmountFn()};V(()=>this.logicProps.config,(t,n)=>{Object.assign(this.localConfig,t)},{deep:!0}),V(()=>this.logicProps.data,(t,n)=>{Object.assign(this.localData,t),this.handler?.updateData(this.localData)},{deep:!0}),V(()=>this.logicProps.event,(t,n)=>{Object.assign(this.localEvent,t)},{deep:!0}),this.handler=en(this.localData,this.localEvent,this.localConfig,this.rootRef,this.dragBlockRef,this.dragBarRef,this.clearData)}};var ge=class{constructor(t={}){this.setConfig=(t={})=>(t={...Wt(),...t},this._logic.setPropsConfig(t),this);this.setData=(t={})=>(t={...Xt(),...t},this._logic.setPropsData(t),this);this.setEvents=(t={})=>(t={...t},this._logic.setPropsEvent(t),this);this.mount=t=>{if(!this._el)return this._logic.mount(t),this._el=t,this};this.destroy=()=>(this._logic.unmount(),this._el=null,this);this.clear=()=>{this._logic.clear()};this.reset=()=>{this._logic.reset()};this.refresh=()=>{this._logic.refresh()};this.close=()=>{this._logic.close()};this._logic=new jt,this.setConfig(t)}};function me(e={}){let t=new ge(e);return{setConfig:t.setConfig,setData:t.setData,setEvents:t.setEvents,mount:t.mount,destroy:t.destroy,clear:t.clear,reset:t.reset,refresh:t.refresh,close:t.close}}var Sn={Button:le,Click:he,Slide:de,SlideRegion:fe,Rotate:me};return ln(xn);})();
