// 获取新的下拉框元素和容器元素
const faceselect = document.getElementById("faceselect");
const emoticonContainer = document.getElementById("emoticon-container");
const selectOptions = document.querySelector("#faceselect .select_options");

// 检查和隐藏表情容器和select_options
if (emoticonContainer) {
    emoticonContainer.style.display = "none";
}
if (selectOptions) {
    selectOptions.style.display = "none";
}

// 获取新的表情选项
const emoticonOptions = document.querySelectorAll(".select_option input.select_input");

// 异步加载表情数据
let emoticons = {};
let emoticonFallbacks = {};
let emoticonDataLoaded = false;
let emoticonTableInitialized = false;

// 创建一个表格元素
const emoticonTable = document.createElement("table");
emoticonTable.style.width = "100%"; // 表格占据整个容器宽度
emoticonTable.style.borderCollapse = "collapse"; // 去除表格边框间距

// 根据屏幕宽度决定每行显示的单元格数量和单元格高度
let maxCellsPerRow = window.innerWidth > 750 ? 10 : 8;
let cellHeight = window.innerWidth > 750 ? 70 : 35;

// 定义加载中提示
const loadingElement = document.createElement("div");
loadingElement.innerText = "表情加载中...";
loadingElement.style.textAlign = "center";
loadingElement.style.padding = "20px";
loadingElement.style.fontSize = "14px";
loadingElement.style.color = "#666";

// 加载表情数据函数
function loadEmoticonData() {
    return new Promise((resolve, reject) => {
        // 如果已经加载过数据，直接返回
        if (emoticonDataLoaded) {
            resolve();
            return;
        }

        // 将加载提示添加到表情容器
        if (emoticonContainer && !emoticonContainer.contains(loadingElement)) {
            emoticonContainer.appendChild(loadingElement);
        }

        fetch("/NetCSS/JS/BookView/Emoji.json")
            .then(response => {
                if (!response.ok) {
                    throw new Error("表情数据加载失败");
                }
                return response.json();
            })
            .then(data => {
                emoticons = data.emoticons || {};
                emoticonFallbacks = data.emoticonFallbacks || {};
                emoticonDataLoaded = true;
                resolve();
            })
            .catch(error => {
                console.error("加载表情数据出错:", error);
                reject(error);
            });
    });
}

// 初始化表情表格函数
function initEmoticonTable() {
    // 如果已经初始化过表格，直接返回
    if (emoticonTableInitialized) {
        return;
    }

    // 移除加载提示
    if (loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
    }

    // 遍历新的表情选项，创建表情图片并添加到表格
    let row = null;
    emoticonOptions.forEach((option, index) => {
        if (index % maxCellsPerRow === 0) {
            // 创建新行
            row = emoticonTable.insertRow();
        }

        const cell = row.insertCell();
        const emoticonValue = option.value;

        // 设置表格单元格的样式
        cell.style.width = `${100 / maxCellsPerRow}%`; // 计算单元格宽度
        cell.style.height = `${cellHeight}px`; // 设置单元格高度
        cell.style.padding = "0"; // 去除单元格内边距
        cell.style.position = "relative"; // 相对定位

        // 如果没有表情数据，只创建单元格但不添加图片
        if (!emoticonDataLoaded || !emoticons[emoticonValue]) {
            return;
        }

        const emoticonImage = document.createElement("img");
        const emoticonSrc = emoticons[emoticonValue];
        emoticonImage.src = emoticonSrc;
        emoticonImage.alt = emoticonValue;
        emoticonImage.setAttribute("referrerpolicy", "no-referrer");

        // 为图片添加title属性，显示表情包的名称（不包括.gif扩展名）
        const emoticonName = emoticonValue.replace(".gif", "");
        emoticonImage.title = emoticonName;

        // 添加点击事件处理
        emoticonImage.addEventListener("click", function () {
            // 更新选择框的值
            option.checked = true;
            // 隐藏容器
            if (emoticonContainer) {
                emoticonContainer.style.display = "none";
            }
            if (selectOptions) {
                selectOptions.style.display = "block";
            }
        });

        emoticonImage.onerror = function () {
            // 处理加载失败时的情况
            const fallbackData = emoticonFallbacks[emoticonSrc];
            if (fallbackData) {
                if (fallbackData.fallback1 && !this.fallback1Failed) {
                    this.fallback1Failed = true;
                    emoticonImage.src = fallbackData.fallback1;
                } else if (fallbackData.fallback2) {
                    emoticonImage.src = fallbackData.fallback2;
                } else {
                    // 如果没有有效的备用 URL，可以隐藏当前表情图片
                    cell.style.display = "none";
                }
            }
        };

        // 创建一个单元格遮罩
        const cellMask = document.createElement("div");
        cellMask.style.position = "absolute";
        cellMask.style.top = "0";
        cellMask.style.left = "0";
        cellMask.style.width = "100%";
        cellMask.style.height = "100%";
        cellMask.style.cursor = "pointer"; // 鼠标样式为手型

        // 单元格遮罩的点击事件与图像点击事件一致
        cellMask.addEventListener("click", function () {
            option.checked = true;
            // 隐藏容器
            if (emoticonContainer) {
                emoticonContainer.style.display = "none";
            }
            if (selectOptions) {
                selectOptions.style.display = "block";
            }
        });

        // 将图片和遮罩添加到单元格
        cell.appendChild(emoticonImage);
        cell.appendChild(cellMask); // 添加遮罩
    });

    // 将表格添加到表情容器
    if (emoticonContainer) {
        emoticonContainer.appendChild(emoticonTable);
        emoticonTableInitialized = true;
    } else {
        console.log("emoticonContainer元素不存在，无法添加表格");
    }

    // 隐藏无效的表情行
    cleanupInvalidRows();
}

// 清理无效表情行
function cleanupInvalidRows() {
    // 获取所有TR元素
    const trElements = emoticonTable.querySelectorAll("tr");

    // 遍历TR元素
    trElements.forEach(trElement => {
        // 获取TR元素下的TD元素
        const tdElements = trElement.querySelectorAll("td");
        let hasValidImage = false;

        // 检查行中是否有有效图片
        tdElements.forEach(tdElement => {
            const imgElement = tdElement.querySelector("img");
            if (imgElement && imgElement.getAttribute("src") !== "undefined" && imgElement.getAttribute("src") !== null) {
                hasValidImage = true;
            }
        });

        // 如果整行没有有效图片，隐藏该行
        if (!hasValidImage) {
            trElement.style.display = "none";
        }
    });
}

// 检查 selectExpand 元素并添加事件监听
const selectExpand = document.getElementById("emotion-opener");
if (selectExpand) {
    selectExpand.addEventListener("click", function (event) {
        if (emoticonContainer) {
            if (emoticonContainer.style.display === "none") {
                // 点击打开表情面板时，加载表情数据并初始化表格
                loadEmoticonData()
                    .then(() => {
                        initEmoticonTable();
                        emoticonContainer.style.display = "block";
                        if (selectOptions) {
                            selectOptions.style.display = "none";
                        }
                    })
                    .catch(error => {
                        // 加载失败时显示错误信息
                        if (loadingElement.parentNode) {
                            loadingElement.innerText = "表情加载失败，请刷新页面重试";
                            loadingElement.style.color = "red";
                        }
                        console.error("表情数据加载失败:", error);
                    });
                event.stopPropagation();
            } else {
                emoticonContainer.style.display = "none";
                if (selectOptions) {
                    selectOptions.style.display = "block";
                }
            }
        }
    });
} else {
    console.log("selectExpand元素不存在，无法添加点击事件");
}

// 检查 faceselectInput 元素并添加事件监听
const faceselectInput = document.querySelector("#faceselect .select_close");
if (faceselectInput) {
    faceselectInput.addEventListener("click", function () {
        if (emoticonContainer && emoticonContainer.style.display === "none" && !faceselectInput.checked) {
            if (selectOptions) {
                selectOptions.style.display = "none";
            }
        }
    });
} else {
    console.log("faceselectInput元素不存在，无法添加点击事件");
}

// 点击页面的其他地方时隐藏容器
document.addEventListener("click", function (event) {
    if (emoticonContainer && emoticonContainer.style.display === "block"
        && event.target !== selectExpand && event.target !== faceselect) {
        emoticonContainer.style.display = "none";
        if (selectOptions) {
            selectOptions.style.display = "block";
        }
    }
});