using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 编辑个人资料页面 - 合并昵称修改、个性签名修改和详细资料修改功能
    /// </summary>
    public class EditProfile : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string INFO = "";
        public string ERROR = "";
        
        // 昵称相关
        public string tonickname = "";
        public string nickNum = "0";
        private readonly string KL_Check_Repeat_Nickname = PubConstant.GetAppString("KL_Check_Repeat_Nickname");
        private static readonly string[] ForbiddenNicknames = GetForbiddenNicknames();
        
        // 个性签名相关
        public string remarkNum = "0";
        
        // 详细资料相关
        public string aihao = "";
        public string qq = "";
        
        // 导航相关
        public string backurl = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 基础验证和初始化
            InitializePage();
            
            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            
            // 如果不是提交操作，直接返回显示表单
            string action = base.Request.Form.Get("action");
            if (action != "gomod")
            {
                return;
            }
            
            // 处理表单提交
            ProcessFormSubmission();
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";

            // 检查Cookie中的UI偏好
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }

            // 默认使用旧版本
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            // 如果偏好是新版本，尝试使用Handlebars模板
            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars(); // 返回是否成功渲染新版
            }

            return false; // 使用旧版
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 检查是否存在TemplateService
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    // 使用反射调用TemplateService
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true; // 成功渲染新版
                        }
                    }
                }

                // 如果Handlebars不可用，记录错误但不回退
                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("Handlebars模板服务不可用，继续使用旧版");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 RenderWithHandlebars 中 Response.End() 的正常行为
                // 表示新版渲染成功并正常终止了线程，这是期望的行为
                System.Diagnostics.Debug.WriteLine("新版渲染成功，线程正常终止");
                return true; // 实际上是成功的
            }
            catch (Exception ex)
            {
                // 记录错误但不回退到静态模板
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                System.Diagnostics.Debug.WriteLine($"模板渲染错误: {ex.Message}，继续使用旧版");
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 处理表单提交（如果有）
                string action = base.Request.Form.Get("action");
                if (action == "gomod")
                {
                    ProcessFormSubmission();
                }

                // 构建数据模型
                var pageModel = BuildEditProfilePageModel();
                
                // 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/EditProfile.hbs",
                    pageModel,
                    "编辑个人资料",
                    new YaoHuo.Plugin.BBS.Models.HeaderOptionsModel { ShowViewModeToggle = false } // 头部选项：不显示UI切换按钮
                );
                
                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End(); // 使用Response.End()确保页面执行完全终止
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，不需要处理
                // 直接重新抛出，让它正常终止线程
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 (EditProfile.aspx): {ex.ToString()}");
                // 记录错误但不回退到旧版，让新的TemplateService返回错误HTML信息
                ERROR = "新版界面加载失败: " + WapTool.ErrorToString(ex.ToString());
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest(); // 在异常处理中使用CompleteRequest避免嵌套异常
            }
        }

        /// <summary>
        /// 判断是否应该渲染新版界面
        /// </summary>
        /// <returns>true: 渲染新版; false: 渲染旧版</returns>
        private bool ShouldRenderNewVersion()
        {
            // 与 MyFile 使用相同的 Cookie 判断逻辑
            return TemplateService.GetViewMode() == "new";
        }

        /// <summary>
        /// 构建 EditProfile 页面数据模型
        /// </summary>
        /// <returns>页面数据模型</returns>
        private EditProfilePageModel BuildEditProfilePageModel()
        {
            var model = new EditProfilePageModel
            {
                PageTitle = "编辑个人资料"
            };

            // 消息状态
            BuildMessageModel(model);

            // 表单数据
            BuildFormDataModel(model);

            // 站点信息
            BuildSiteInfoModel(model);

            // 选项列表
            BuildOptionListsModel(model);

            return model;
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(EditProfilePageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.HasMessage = true;
                switch (INFO)
                {
                    case "OK":
                        model.Message.Type = "success";
                        model.Message.Content = "更新成功！";
                        break;
                    case "NICK_NULL":
                        model.Message.Type = "error";
                        model.Message.Content = "昵称不能为空！";
                        break;
                    case "NICK_HASEXIST":
                        model.Message.Type = "error";
                        model.Message.Content = "本网站中此昵称已存在！";
                        break;
                    case "NICK_TOOFREQUENT":
                        model.Message.Type = "warning";
                        model.Message.Content = "每自然月只能修改1次昵称，请下个月再试！";
                        break;
                    case "NICK_FORBIDDEN":
                        model.Message.Type = "error";
                        model.Message.Content = "昵称包含禁用词，请重新输入！";
                        break;
                    default:
                        model.Message.Type = "info";
                        model.Message.Content = INFO;
                        break;
                }
            }
        }

        /// <summary>
        /// 构建表单数据模型
        /// </summary>
        private void BuildFormDataModel(EditProfilePageModel model)
        {
            model.FormData.ActionUrl = http_start + "bbs/EditProfile.aspx";

            // 论坛资料
            model.FormData.ForumInfo.Nickname = tonickname;
            model.FormData.ForumInfo.Signature = userVo.remark ?? "";

            // 联系方式
            model.FormData.ContactInfo.Mobile = userVo.mobile ?? "";
            model.FormData.ContactInfo.Email = userVo.email ?? "";
            model.FormData.ContactInfo.QQ = qq;

            // 个人信息
            model.FormData.PersonalInfo.Hobby = aihao;
            model.FormData.PersonalInfo.MaritalStatus = userVo.fenfuo ?? "";
            model.FormData.PersonalInfo.Occupation = userVo.zhiye ?? "";
            model.FormData.PersonalInfo.City = userVo.city ?? "";
            model.FormData.PersonalInfo.Age = (int)userVo.age;
            model.FormData.PersonalInfo.Height = userVo.shenggao ?? "";
            model.FormData.PersonalInfo.Weight = userVo.tizhong ?? "";
            model.FormData.PersonalInfo.Zodiac = userVo.xingzuo ?? "";
            model.FormData.PersonalInfo.Gender = (int)userVo.sex;

            // 隐藏字段
            model.FormData.HiddenFields.Action = "gomod";
            model.FormData.HiddenFields.SiteId = siteid.ToString();
            model.FormData.HiddenFields.ClassId = classid.ToString();
            model.FormData.HiddenFields.BackUrl = backurl;
        }

        /// <summary>
        /// 构建站点信息模型
        /// </summary>
        private void BuildSiteInfoModel(EditProfilePageModel model)
        {
            model.SiteInfo.SiteId = siteid.ToString();
            model.SiteInfo.ClassId = classid.ToString();
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.BackUrl = backurl;
        }

        /// <summary>
        /// 构建选项列表模型
        /// </summary>
        private void BuildOptionListsModel(EditProfilePageModel model)
        {
            // 婚否选项
            string[] maritalOptions = { "", "未婚", "已婚" };
            foreach (string option in maritalOptions)
            {
                model.OptionLists.MaritalStatusOptions.Add(new OptionItem
                {
                    Value = option,
                    Text = string.IsNullOrEmpty(option) ? "请选择" : option,
                    Selected = userVo.fenfuo == option
                });
            }

            // 星座选项
            string[] zodiacOptions = { "", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", 
                                      "天秤座", "天蝎座", "射手座", "摩羯座", "水瓶座", "双鱼座" };
            foreach (string option in zodiacOptions)
            {
                model.OptionLists.ZodiacOptions.Add(new OptionItem
                {
                    Value = option,
                    Text = string.IsNullOrEmpty(option) ? "请选择" : option,
                    Selected = userVo.xingzuo == option
                });
            }
        }

        /// <summary>
        /// 初始化页面基础设置
        /// </summary>
        private void InitializePage()
        {
            // 登录验证
            IsLogin(userid, "bbs/modifyuserinfo.aspx?siteid=" + siteid);
            NeedPassWordToAdminNew();
            
            // 初始化配置参数
            InitializeConfigParameters();
            
            // 初始化返回地址
            InitializeBackUrl();
            
            // 初始化用户数据
            InitializeUserData();
        }

        /// <summary>
        /// 初始化配置参数
        /// </summary>
        private void InitializeConfigParameters()
        {
            // 昵称长度限制
            nickNum = WapTool.GetSiteDefault(siteVo.Version, 48);
            if (!WapTool.IsNumeric(nickNum) || nickNum == "0")
            {
                nickNum = "16";
            }
            
            // 个性签名长度限制
            remarkNum = WapTool.GetSiteDefault(siteVo.Version, 49);
            if (!WapTool.IsNumeric(remarkNum) || remarkNum == "0")
            {
                remarkNum = "50";
            }
        }

        /// <summary>
        /// 初始化返回地址
        /// </summary>
        private void InitializeBackUrl()
        {
            backurl = base.Request.QueryString.Get("backurl");
            if (string.IsNullOrEmpty(backurl))
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (string.IsNullOrEmpty(backurl))
            {
                backurl = "bbs/modifyuserinfo.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
        }

        /// <summary>
        /// 初始化用户数据
        /// </summary>
        private void InitializeUserData()
        {
            tonickname = userVo.nickname;
            
            // 分离爱好和QQ
            var parts = (userVo.aihao + "___").Split('_');
            aihao = parts[0];
            qq = parts.Length > 1 ? parts[1] : "";
        }

        /// <summary>
        /// 处理表单提交
        /// </summary>
        private void ProcessFormSubmission()
        {
            try
            {
                // 获取表单数据
                string newNickname = GetRequestValue("tonickname");
                string newRemark = GetRequestValue("remark");
                
                // 处理昵称修改 - 如果昵称有变化，直接更新数据库
                bool nicknameChanged = ProcessNicknameChange(newNickname);
                if (!string.IsNullOrEmpty(INFO) && INFO.StartsWith("NICK_"))
                {
                    return; // 昵称验证失败，直接返回
                }
                
                // 处理个性签名修改
                ProcessRemarkChange(newRemark);
                
                // 处理详细资料修改
                if (!ProcessDetailedInfoChange())
                {
                    return;
                }
                
                // 更新用户信息（除昵称外的其他信息）
                MainBll.UpdateUser_WAP(userVo);
                
                // 如果昵称有变化，单独更新昵称和时间
                if (nicknameChanged)
                {
                    string updateSql = "update [user] set nickname=@nickname, LastNickChangeDate=GETDATE() where siteid=@siteid and userid=@userid";
                    DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, updateSql, 
                        new SqlParameter("@nickname", tonickname),
                        new SqlParameter("@siteid", siteid),
                        new SqlParameter("@userid", userid));
                }
                
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        /// <summary>
        /// 处理昵称修改
        /// </summary>
        /// <param name="newNickname">新昵称</param>
        /// <returns>昵称是否有变化</returns>
        private bool ProcessNicknameChange(string newNickname)
        {
            // 移除所有空格(包括全角空格)
            newNickname = RemoveAllSpaces(newNickname);
            
            // 如果昵称未变化，跳过昵称验证
            if (newNickname == userVo.nickname)
            {
                tonickname = newNickname;
                return false; // 没有变化
            }
            
            // 长度限制
            if (newNickname.Length > 15)
            {
                newNickname = newNickname.Substring(0, 15);
            }
            
            // 空值检查
            if (string.IsNullOrEmpty(newNickname))
            {
                INFO = "NICK_NULL";
                return false;
            }
            
            // 检查是否包含禁用昵称
            if (ContainsForbiddenNickname(newNickname))
            {
                INFO = "NICK_FORBIDDEN";
                return false;
            }
            
            // 检查上次修改昵称的时间
            if (!CanChangeNickname())
            {
                INFO = "NICK_TOOFREQUENT";
                return false;
            }
            
            // 检查昵称是否已存在
            if (KL_Check_Repeat_Nickname != "1" && MainBll.isHasExistNickname(siteid, newNickname))
            {
                INFO = "NICK_HASEXIST";
                return false;
            }
            
            // 应用长度限制
            if (nickNum != "0")
            {
                newNickname = WapTool.Left(newNickname, int.Parse(nickNum));
            }
            
            // 更新昵称变量（但不立即更新数据库）
            tonickname = newNickname;
            
            return true; // 有变化
        }

        /// <summary>
        /// 处理个性签名修改
        /// </summary>
        /// <param name="newRemark">新个性签名</param>
        private void ProcessRemarkChange(string newRemark)
        {
            if (remarkNum != "0" && !string.IsNullOrEmpty(newRemark))
            {
                newRemark = WapTool.Left(newRemark, int.Parse(remarkNum));
            }
            userVo.remark = newRemark ?? "";
        }

        /// <summary>
        /// 处理详细资料修改
        /// </summary>
        /// <returns>是否成功</returns>
        private bool ProcessDetailedInfoChange()
        {
            // 性别验证（特殊处理，因为不允许为空）
            string sexStr = GetRequestValue("sex");
            if (!long.TryParse(sexStr, out long sex) || (sex != 0 && sex != 1))
            {
                ERROR = "性别格式不正确";
                return false;
            }
            userVo.sex = sex;

            // 通用验证方法
            if (!ValidateField("age", 10L, 99L, "年龄必须为10-99之间的数字", out long age))
                return false;
            userVo.age = age;

            if (!ValidateField("shenggao", 100, 250, "身高必须为100-250之间的数字", out int shenggao))
                return false;
            userVo.shenggao = shenggao.ToString();

            if (!ValidateField("tizhong", 30, 300, "体重必须为30-300之间的数字", out int tizhong))
                return false;
            userVo.tizhong = tizhong.ToString();

            // 星座验证
            if (!ValidateZodiacSign("xingzuo", out string xingzuo))
                return false;
            userVo.xingzuo = xingzuo;

            // 汉字字段验证
            if (!ValidateChineseField("aihao", 0, 10, "爱好最多10个汉字", out string newAihao))
                return false;
            this.aihao = newAihao;

            if (!ValidateChineseField("fenfuo", 0, 2, "婚否最多2个汉字", out string fenfuo))
                return false;
            userVo.fenfuo = fenfuo;

            if (!ValidateChineseField("zhiye", 0, 5, "职业最多5个汉字", out string zhiye))
                return false;
            userVo.zhiye = zhiye;

            if (!ValidateChineseField("city", 0, 8, "城市最多8个汉字", out string city))
                return false;
            userVo.city = city;

            // 其他字段验证
            if (!ValidateStringField("mobile", 11, 11, @"^\d{11}$", "手机号必须为11位数字", out string mobile))
                return false;
            userVo.mobile = mobile;

            if (!ValidateStringField("email", 0, 30, @"^.+@.+\..+$", "邮箱格式不正确或超过30个字符", out string email))
                return false;
            userVo.email = email;

            if (!ValidateStringField("qq", 5, 11, @"^\d{5,11}$", "QQ号必须为5-11位数字", out string newQq))
                return false;
            this.qq = newQq;

            // 更新爱好和QQ
            userVo.aihao = aihao + "_" + qq;

            return true;
        }

        #region 昵称验证相关方法

        /// <summary>
        /// 检查是否可以修改昵称（每月限制）
        /// </summary>
        /// <returns>是否可以修改</returns>
        private bool CanChangeNickname()
        {
            string sqlStr = "SELECT LastNickChangeDate FROM [user] WHERE siteid=@siteid AND userid=@userid";
            object result = DbHelperSQL.ExecuteScalar(ConnectionString, CommandType.Text, sqlStr,
                new SqlParameter("@siteid", siteid),
                new SqlParameter("@userid", userid));

            if (result != null && result != DBNull.Value)
            {
                DateTime lastChangeDate = Convert.ToDateTime(result);
                if (DateTime.Now.Year == lastChangeDate.Year && DateTime.Now.Month == lastChangeDate.Month)
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 检查昵称是否包含禁用词
        /// </summary>
        /// <param name="nickname">昵称</param>
        /// <returns>是否包含禁用词</returns>
        private bool ContainsForbiddenNickname(string nickname)
        {
            if (ForbiddenNicknames == null || ForbiddenNicknames.Length == 0)
                return false;
                
            return ForbiddenNicknames.Any(forbidden => 
                !string.IsNullOrWhiteSpace(forbidden) && 
                nickname.ToLower().Contains(forbidden.Trim().ToLower()));
        }

        /// <summary>
        /// 移除所有空格(包括全角空格)
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>移除空格后的字符串</returns>
        private string RemoveAllSpaces(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            return Regex.Replace(input, @"\s+", "", RegexOptions.Compiled);
        }

        /// <summary>
        /// 获取禁用昵称列表
        /// </summary>
        /// <returns>禁用昵称数组</returns>
        private static string[] GetForbiddenNicknames()
        {
            try
            {
                string configValue = ConfigurationManager.AppSettings["ForbiddenNicknames"];
                if (string.IsNullOrEmpty(configValue))
                    return new string[0];
                    
                return configValue.Split(',');
            }
            catch
            {
                return new string[0];
            }
        }

        #endregion

        #region 详细资料验证相关方法

        /// <summary>
        /// 验证数字字段
        /// </summary>
        private bool ValidateField<T>(string fieldName, T min, T max, string errorMessage, out T value) where T : struct, IComparable<T>
        {
            string strValue = GetRequestValue(fieldName);
            if (string.IsNullOrEmpty(strValue))
            {
                value = default(T);
                return true;
            }
            if (!TryParse(strValue, out value) || value.CompareTo(min) < 0 || value.CompareTo(max) > 0)
            {
                ERROR = errorMessage;
                return false;
            }
            return true;
        }

        /// <summary>
        /// 类型转换
        /// </summary>
        private bool TryParse<T>(string value, out T result)
        {
            try
            {
                result = (T)Convert.ChangeType(value, typeof(T));
                return true;
            }
            catch
            {
                result = default(T);
                return false;
            }
        }

        /// <summary>
        /// 验证中文字段
        /// </summary>
        private bool ValidateChineseField(string fieldName, int minLength, int maxLength, string errorMessage, out string value)
        {
            value = GetRequestValue(fieldName);
            if (string.IsNullOrEmpty(value))
            {
                return true;
            }
            if (value.Length < minLength || value.Length > maxLength || !Regex.IsMatch(value, @"^[\u4e00-\u9fa5]+$"))
            {
                ERROR = errorMessage;
                return false;
            }
            return true;
        }

        /// <summary>
        /// 验证字符串字段
        /// </summary>
        private bool ValidateStringField(string fieldName, int minLength, int maxLength, string pattern, string errorMessage, out string value)
        {
            value = GetRequestValue(fieldName);
            if (string.IsNullOrEmpty(value))
            {
                return true;
            }
            if (value.Length < minLength || value.Length > maxLength)
            {
                ERROR = errorMessage;
                return false;
            }
            if (pattern != null && !Regex.IsMatch(value, pattern))
            {
                ERROR = errorMessage;
                return false;
            }
            return true;
        }

        /// <summary>
        /// 验证星座
        /// </summary>
        private bool ValidateZodiacSign(string fieldName, out string value)
        {
            string[] validSigns = new string[]
            {
                "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座",
                "天秤座", "天蝎座", "射手座", "摩羯座", "水瓶座", "双鱼座"
            };

            value = GetRequestValue(fieldName);
            if (string.IsNullOrEmpty(value))
            {
                return true;
            }
            if (!validSigns.Contains(value))
            {
                ERROR = "必须填入十二星座（3个字）";
                return false;
            }
            return true;
        }

        #endregion

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        private string ConnectionString => PubConstant.GetConnectionString(a);
    }
} 