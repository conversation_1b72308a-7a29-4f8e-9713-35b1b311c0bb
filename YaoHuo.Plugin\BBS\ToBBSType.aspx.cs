﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class ToBBSType : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string top = "";

        public string down = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            CheckManagerLvl("04", classVo.adminusername, "bbs/showadmin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
            if (classVo.userid.ToString() != siteid)
            {
                base.Response.End();
            }
            if (classVo.classid.ToString() != classid)
            {
                base.Response.End();
            }
            if (classVo.bbsType.IndexOf('_') > 0)
            {
                top = classVo.bbsType.Split('_')[0];
                down = classVo.bbsType.Split('_')[1];
            }
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("chkall");
                string strWhere = "";
                top = GetRequestValue("top");
                down = GetRequestValue("down");
                top = top.Replace("_", "*");
                down = down.Replace("_", "*");
                if (requestValue == "yes" && (userVo.managerlvl == "00" || userVo.managerlvl == "01"))
                {
                    strWhere = "bbs/index.aspx";
                }
                class_BLL class_BLL = new class_BLL(a);
                class_BLL.updateBbsType(long.Parse(siteid), long.Parse(classid), top + "_" + down, strWhere);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}