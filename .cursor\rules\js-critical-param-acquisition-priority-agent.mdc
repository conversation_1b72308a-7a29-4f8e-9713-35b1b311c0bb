---
description: JS关键参数获取优先级：构建异步请求时，优先从服务端输出(全局/隐藏字段)，其次URL参数，确保可靠性，避免因参数缺失或变化导致请求失败
globs: 
alwaysApply: false
---

# JS关键参数获取优先级规则

## 使用场景
- 当在客户端JavaScript代码中需要获取用于构造API请求（特别是异步请求）的关键上下文参数时，例如 `siteid`、`classid`、`userid` 等。
- 尤其适用于需要修复因参数获取不正确导致请求失败或行为异常的场景。
- 当进行代码修改或添加新功能，涉及到从页面获取此类参数时。

## 关键规则
- **优先可靠来源**: 必须优先尝试从服务器端明确输出到页面的全局JavaScript变量（如 `window.siteid`）中获取关键参数。
- **其次表单字段**: 如果全局变量不存在，应尝试从页面表单中相应的隐藏字段（如 `form.querySelector('[name="siteid"]').value`）获取。
- **最后URL参数**: 仅当上述两种方式都无法获取到参数时，才考虑从当前页面的URL查询参数 (`new URLSearchParams(window.location.search).get("paramName")`) 中获取。
- **参数校验**: 在获取参数后，必须进行有效性检查（如确保其不为 `null` 或 `undefined`）。如果关键参数缺失，应记录错误，并根据业务逻辑决定是中止操作、提示用户还是使用安全的默认值（若适用）。
- **避免单一依赖**: 绝不能仅依赖URL查询参数作为获取关键上下文参数的唯一途径，除非能确保该参数在所有访问路径下都恒定存在且可靠。

## 示例
<example>
假设页面需要获取 `siteid` 和 `classid` 来发起一个异步请求。

```javascript
// 正确的获取方式
function getParamsForApi(formElement) {
  let siteId = null;
  let classId = null;

  // 1. 优先从全局变量获取
  if (window.g_siteId && window.g_classId) {
    siteId = window.g_siteId;
    classId = window.g_classId;
  }

  // 2. 其次从表单隐藏字段获取 (如果全局变量不存在)
  if (!siteId && formElement) {
    const siteIdField = formElement.querySelector('input[name="siteid"]');
    if (siteIdField) siteId = siteIdField.value;
  }
  if (!classId && formElement) {
    const classIdField = formElement.querySelector('input[name="classid"]');
    if (classIdField) classId = classIdField.value;
  }

  // 3. 最后从URL参数获取 (如果上述方式都失败)
  if (!siteId || !classId) {
    const params = new URLSearchParams(window.location.search);
    if (!siteId) siteId = params.get("siteid");
    if (!classId) classId = params.get("classid");
  }

  if (!siteId || !classId) {
    console.error("关键参数 siteid 或 classid 获取失败！");
    return null; // 或者抛出错误，或者返回一个明确的失败指示
  }

  return { siteId, classId };
}

// 假设服务器端在ASPX页面中输出了全局变量：
// <script>window.g_siteId = <%= this.CurrentSiteID %>; window.g_classId = <%= this.CurrentClassID %>;</script>
// 或者表单中有隐藏字段：
// <input type="hidden" name="siteid" value="<%= this.CurrentSiteID %>" />
// <input type="hidden" name="classid" value="<%= this.CurrentClassID %>" />
```
说明: 此示例展示了获取参数的正确优先级顺序：全局变量 -> 表单隐藏字段 -> URL查询参数，并包含了参数有效性检查。
</example>

<example type="invalid">
```javascript
// 错误的方式：仅依赖URL参数
function getParamsForApiOnlyFromUrl() {
  const params = new URLSearchParams(window.location.search);
  const siteId = params.get("siteid");
  const classId = params.get("classid");

  // 如果URL中没有这些参数，siteId和classId将为null
  // 例如，用户通过伪静态URL访问时： /articles/my-post.html

  if (!siteId || !classId) {
    // 这里的错误处理可能不足以应对所有情况，或者根本没有处理
    console.warn("siteid 或 classid 未在URL中找到。");
  }
  
  // 直接使用可能为null的参数构建请求URL
  const apiUrl = `/api/data?siteid=${siteId}&classid=${classId}`; 
  // fetch(apiUrl)...
  // 这会导致像 /api/data?siteid=null&classid=null 这样的错误请求

  return { siteId, classId };
}
```
说明: 此示例仅从URL查询参数获取 `siteid` 和 `classid`。如果页面的URL不包含这些参数（例如伪静态URL或特定跳转后），这将导致获取到的值为 `null`，进而构建出错误的API请求URL（如本次修复前遇到的 `/bbs/book_view.aspx?siteid=null&classid=null&id=...`），导致功能异常。
</example>