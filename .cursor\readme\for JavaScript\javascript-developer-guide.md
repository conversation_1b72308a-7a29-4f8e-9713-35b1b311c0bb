# JavaScript 开发者指南 - YaoHuo.Plugin 项目

## 1. 引言

本文档旨在为 YaoHuo.Plugin 项目的 JavaScript 开发提供一套全面的编码规范、最佳实践和常见问题解决方案。遵循本指南有助于提高代码质量、可维护性、性能，并促进团队成员间的有效协作。

本文档基于项目内置的编码规范 (`.cursor/rules/03-javascript-style-docs.mdc`)，并对其进行更详细的阐述和补充。

## 2. 核心原则

* **兼容性优先：** 始终确保代码在目标浏览器 (IE11+, Edge, Chrome) 上的兼容性。
* **可读性与可维护性：** 代码首先是写给人看的。清晰、简洁、易于理解的代码比追求极致技巧更重要。
* **模块化与封装：** 严格控制作用域，避免全局污染，提倡功能内聚。
* **性能意识：** 在不牺牲可读性的前提下，关注代码执行效率和资源加载。
* **安全性：** 始终警惕常见的Web安全漏洞，如XSS、CSRF等。
* **渐进增强：** 核心功能应尽量保证在JavaScript失效或受限时的可用性。

## 3. 编码规范详解

### 3.1. 兼容性与环境

* **目标浏览器**: 确保代码兼容 IE11+、Microsoft Edge 及现代版 Chrome。
  * 在进行功能开发或重构时，需在这些浏览器上进行充分测试。
  * 对于 IE11 特有的兼容性问题，应添加必要的 polyfill 或采用兼容性写法，并加以注释说明。
* **ES6+特性**:
  * 推荐使用 ES6+ 带来的现代特性（如 `let`/`const`、箭头函数、模板字符串、解构赋值、Promise、async/await 等）以提升代码可读性和开发效率。
  * **重要：** 由于项目需要兼容 IE11，所有 ES6+ 代码**必须**通过 Babel 等工具转译为 ES5兼容代码，或确保所用特性已被 IE11 支持。在没有构建流程自动转译前，需谨慎选用，或手动编写兼容代码。
  * 优先使用 `const` 声明不应被重新赋值的变量，其次使用 `let`，避免使用 `var`。
* **宿主环境**: 项目中的 JavaScript 主要运行在浏览器端。

### 3.2. 命名规范

* **变量与函数**: 使用 `camelCase` (小驼峰命名法)。
  * 例如: `currentUser`, `calculateTotalPrice()`, `mainContainerElement`。
* **构造函数/类**: 使用 `PascalCase` (大驼峰命名法)。
  * 例如: `class UserProfile { ... }`, `function PopupManager() { ... }`。
* **常量**:
  * 对于模块内全局性的、值固定不变的配置项或标识，推荐使用 `ALL_UPPERCASE` 并用下划线分隔。
    * 例如: `const MAX_RETRY_COUNT = 3;`, `const API_BASE_URL = '/api/v1';`
  * 如果常量组织在一个配置对象中，其属性名可遵循 `camelCase`。
    * 例如: `const CONFIG = { apiKey: 'xyz', timeoutDuration: 5000 };`
* **描述性**: 命名应清晰、准确地反映其用途或代表的数据，避免使用模糊或过于简短的名称 (如 `a`, `b`, `x`, `obj`, `arr`)。
  * 推荐使用完整的单词或业界公认的缩写。
* **全局变量**:
  * **严格禁止定义全局变量。** 全局变量极易导致命名冲突、状态不可控和调试困难。
  * **所有JS代码块必须通过模块模式进行封装，形成独立作用域。** (详见 3.3. 模块化与封装)

### 3.3. 代码结构与模块化

* **单一职责原则 (SRP)**:
  * **函数：** 每个函数应力求短小精悍，只做一件事情并把它做好。避免编写包含多个逻辑分支、功能庞杂的"万能函数"。
  * **文件/模块：** 每个 `.js` 文件应被视为一个独立的模块，聚焦于一个单一的核心功能或业务关注点。
    * **反例：** 将"异步发帖"、"图片处理"、"用户@功能"等多个不相关的功能耦合在同一个JS文件中。
    * **正例：** `FastPostHandler.js`, `ImageDecorator.js`, `AtMentionService.js`。
* **模块化封装 (IIFE)**:
  * **强烈推荐使用 IIFE (立即调用函数表达式) 来封装每个JS文件（模块）的全部代码，从而创建私有作用域，防止内部变量和函数泄露到全局。**
    ```javascript
    // 例如：MyModule.js
    (function() {
        'use strict'; // 推荐在 IIFE 内部使用严格模式

        // 私有变量和函数
        var privateVar = 'secret';
        function privateFunction() {
            // ...
        }

        // 公共接口 (如果需要暴露给外部)
        // window.MyModule = {
        //     publicMethod: function() { ... }
        // };

        // 初始化逻辑
        function init() {
            // ...
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init(); // DOMContentLoaded 已经触发
        }

    })();
    ```
* **配置文件顶部常量化**:
  * 模块内部的可配置参数、选择器、阈值、API端点等，应在模块顶部通过 `const CONFIG = { ... }` 形式集中定义。
  * 这使得配置易于查找和修改，避免在代码逻辑中散布"魔法数字"或字符串。
    ```javascript
    // 示例顶部配置
    const CONFIG = {
        API_ENDPOINT: '/api/submitData',
        MAX_ITEMS: 10,
        ANIMATION_DURATION: 300, // ms
        SUBMIT_BUTTON_SELECTOR: '#submit-action',
        ERROR_MESSAGE_CLASS: 'error-prompt'
    };
    ```
* **文件组织**:
  * 遵循项目既有的目录结构，例如将帖子详情页相关的脚本放于 `/NetCSS/JS/BookView/` 目录下。
  * 文件名应清晰反映模块的主要功能，使用 `camelCase` 或 `PascalCase` (例如 `userProfile.js`, `UserProfileManager.js`)。

### 3.4. DOM 操作与事件处理

* **选择器与缓存**:
  * 优先使用 `document.getElementById('elementId')` (如果ID已知且唯一)。
  * 其次使用 `parentElement.querySelector('.child-class')` 或 `document.querySelectorAll('tag[attribute="value"]')` 等更精确的CSS选择器。
  * **性能：** 频繁访问的DOM元素**必须**在脚本初始化或适当作用域的起始处查询一次，并将其引用缓存到变量中，避免在循环或高频回调中重复查询DOM。
    ```javascript
    // 推荐
    const submitButton = document.getElementById('submit-btn');
    const userNameInput = document.querySelector('.user-form input[name="username"]');

    function handleSubmit() {
        if (submitButton.disabled) return;
        let name = userNameInput.value;
        // ...
    }
    ```
  * **健壮性：** 在使用 `querySelector` 或 `querySelectorAll` 的结果（以及通过其他方式获取的DOM引用）之前，**必须进行存在性检查（判空）**，确保元素确实存在于页面上，然后再对其进行属性访问或方法调用。
    ```javascript
    const userAvatar = document.querySelector('.profile-avatar img');
    if (userAvatar) {
        userAvatar.src = newAvatarUrl;
    } else {
        console.warn('User avatar element not found.');
    }
    ```
* **DOM 修改**:
  * **减少直接操作：** 避免不必要的DOM读写操作，因为它们可能触发重绘和回流，影响性能。
  * **批量更新：** 如果需要对DOM进行多次插入或修改，优先使用 `DocumentFragment` 在内存中构建好完整的DOM子树，然后一次性将其追加到实际DOM中。
  * **动态UI构建（重要）：** 对于需要动态生成HTML结构的情况（如根据数据列表渲染UI），**强烈推荐将数据与视图分离**。
    * **避免：** 大量手动调用 `document.createElement()`, `element.setAttribute()`, `element.appendChild()` 以及通过字符串拼接生成复杂HTML (`element.innerHTML = "<div>" + data.value + "</div>..."`)。这种方式难以维护、易出错，且有潜在XSS风险。
    * **推荐：**
      * 使用 **ES6模板字符串** 结合简单的辅助函数来构建结构化的HTML片段。
      * 对于更复杂的场景，考虑引入一个轻量级的客户端模板引擎（如 Handlebars.js 的一个子集功能，或者一个极简的自定义模板函数）。
      * 目标是让HTML结构更直观，数据填充更清晰。

    ```javascript
    // 示例：使用模板字符串
    function createListItem(itemData) {
        const li = document.createElement('li');
        li.className = `item item-${itemData.id}`;
        li.innerHTML = \`
            <img src="${itemData.imageUrl}" alt="${itemData.name}">
            <h4>${itemData.name}</h4>
            <p>${itemData.description}</p>
        \`;
        return li;
    }
    ```
* **事件处理**:
  * **绑定与解绑：** 始终使用 `element.addEventListener('eventname', handlerFunction)` 来绑定事件。
  * **内存泄漏（重要）：** 当DOM元素被移除、或者模块/组件被销毁不再需要时，**必须显式调用 `element.removeEventListener('eventname', handlerFunction)` 来清理之前绑定的事件监听器**，以防止内存泄漏。确保 `handlerFunction` 是同一个函数的引用。
    ```javascript
    function handleClick(event) { /* ... */ }
    myButton.addEventListener('click', handleClick);

    // 当 myButton 不再需要时或被移除前
    // myButton.removeEventListener('click', handleClick);
    ```
  * **事件委托：** 对于动态添加的子元素或大量相似元素的事件（例如列表中的每一项），应考虑使用事件委托。即将事件监听器绑定到其共同的父元素上，然后在处理函数中通过 `event.target` 判断事件发生的具体子元素。这能显著减少事件监听器的数量，提高性能，并自动处理动态添加的元素。
* **XSS防护**:
  * 向DOM插入文本内容时，**优先使用 `element.textContent = trustedText;` 或 `element.innerText = trustedText;`**。
  * **谨慎使用 `element.innerHTML = potentiallyUnsafeHtml;`**。如果必须使用 `innerHTML` 来插入HTML结构，必须确保其内容来源可靠，或者已经过严格的服务端清理和客户端转义，以防止跨站脚本攻击 (XSS)。
* **减少硬编码依赖**:
  * 避免在JS中硬编码依赖于非常特定、可能经常变化的DOM结构层级或过于具体的CSS类名组合。
  * 优先使用ID选择器（如果元素是唯一的）、更稳定的父级元素结合简单子元素类名，或者使用 `data-*` 属性作为JS钩子来定位元素，这样可以使JS逻辑与具体的HTML/CSS实现有一定程度的解耦。

### 3.5. 数据管理

* **数据来源与JS分离 (重要)**:
  * 对于大量的、相对静态的配置数据、URL映射、文本列表、常量集合等 (例如 `SelEmoji.js` 中的表情数据)，**强烈建议将其从JavaScript代码中分离出来**。
  * **存储方式：**
    * 可以存放在单独的 `.json` 文件中。JS模块在初始化时通过 `fetch` API 异步加载这些JSON数据。
    * 对于少量简单的配置，也可以考虑存储在HTML标签的 `data-*` 属性中，由JS读取。
  * **收益：**
    * 显著减小JS文件的体积，加快下载和解析速度。
    * 提高数据的可维护性（修改JSON比修改JS逻辑更安全简单）。
    * 使JS代码更聚焦于行为和逻辑。
* **`localStorage` / `sessionStorage`**:
  * 用于在客户端持久化少量用户偏好或状态数据。
  * **注意：** `localStorage` 和 `sessionStorage` 只能存储字符串。存入非字符串数据时会自动调用 `toString()`。取出数据时，如果原始数据是数字或布尔值，需要手动进行类型转换 (`parseInt()`, `parseFloat()`, `value === 'true'`)。
  * 读取时始终要考虑项可能不存在 (返回 `null`) 的情况。

### 3.6. 与后端交互 (AJAX)

* **统一请求方式**:
  * **推荐在项目中统一使用 `fetch` API 进行异步HTTP请求。** `fetch` 是现代浏览器内置的标准，基于Promise，使用更简洁。
  * 可以考虑封装一个通用的请求函数 (例如 `apiClient.js` 模块)，负责处理：
    * 统一的API基地址。
    * 请求头设置 (如 `Content-Type`, CSRF Token)。
    * `POST` 请求时 `body` 的序列化 (如 `JSON.stringify()`)。
    * 响应状态码检查 (`response.ok`)。
    * JSON响应解析 (`response.json()`)。
    * 统一的错误处理和上报。
* **参数校验**: 前端发送到服务端的参数应进行初步的格式和类型校验。服务端**必须**进行严格的最终校验。
* **异步处理**: 优先使用 `async/await` 语法糖配合 `Promise` 来编写更扁平化、易读的异步代码。
  ```javascript
  async function fetchData(userId) {
      try {
          const response = await fetch(\`/api/users/\${userId}\`);
          if (!response.ok) {
              throw new Error(\`HTTP error! status: \${response.status}\`);
          }
          const data = await response.json();
          return data;
      } catch (error) {
          console.error('Fetch data failed:', error);
          // Display user-friendly error message
          throw error; // Optionally re-throw
      }
  }
  ```
* **错误处理**: 妥善处理网络错误 (连接中断、超时)、HTTP错误 (4xx, 5xx 状态码) 以及后端返回的业务逻辑错误。向用户提供清晰、友好的反馈。
* **iframe通信安全**:
  * 如果页面需要通过 `window.postMessage()` 与嵌入的 `<iframe>` (或父窗口) 进行通信：
    * **发送方：** 在调用 `targetWindow.postMessage(message, targetOrigin)` 时，务必指定明确的 `targetOrigin` (例如 `https://secure.example.com`)，而不是使用 `*`，以防止消息被发送到错误的窗口。
    * **接收方 (重要)：** 在 `window.addEventListener('message', receiveMessage)` 的处理函数中，**必须首先校验 `event.origin` 属性**，确保消息来自预期的、可信的源，然后再处理 `event.data`。
      ```javascript
      window.addEventListener('message', function(event) {
          // 替换为实际预期的iframe源
          const TRUSTED_IFRAME_ORIGIN = 'https://my-trusted-iframe.example.com';
          if (event.origin !== TRUSTED_IFRAME_ORIGIN) {
              console.warn(\`Received message from untrusted origin: \${event.origin}\`);
              return;
          }
          // 处理 event.data ...
      });
      ```

### 3.7. 错误处理与日志记录

* **`try...catch`**: 对可能抛出预期异常的关键操作 (如JSON解析、复杂的计算、外部API调用反馈处理) 使用 `try...catch` 块进行包裹。不要滥用 `try...catch` 隐藏所有错误。
* **明确错误信息**: 抛出或捕获错误时，提供有意义的、包含上下文信息的错误消息，以便于定位问题。
* **`parseInt()` / `parseFloat()`**: 这两个函数在解析无法转换为数字的字符串时会返回 `NaN`。**必须使用 `isNaN()` 函数检查其结果**，而不是依赖 `NaN` 的 falsy 特性。
  ```javascript
  let count = parseInt(userInput, 10);
  if (isNaN(count)) {
      count = 0; // 或处理错误
  }
  ```
* **日志记录**:
  * **开发环境：** 适度使用 `console.log()` 进行调试输出关键变量或流程节点。使用 `console.warn()` 提示潜在问题，`console.error()` 报告错误。
  * **生产环境：** 应移除或通过条件编译去除绝大部分 `console.log()`。关键错误应考虑上报到服务端日志服务或第三方监控平台。

### 3.8. 性能优化

* **核心原则**:
  * **减少重绘(repaint)与回流(reflow)**: 避免在循环中频繁读取触发布局的DOM属性 (如 `offsetWidth`, `offsetHeight`, `scrollTop`) 或频繁修改会导致页面结构变化的样式。
  * **节流(throttle)与防抖(debounce)**: 对高频触发的事件 (如 `window.resize`, `window.scroll`, `input` 事件) 的处理函数，应使用节流或防抖技术来限制其执行频率。
* **图片优化**:
  * **懒加载(Lazy Loading)：** 对于页面中非首屏的图片，或者一次性可能加载大量图片的场景 (如表情选择器、图片列表)，应实现图片懒加载。即图片在进入浏览器视口 (viewport) 前不加载其实际 `src`。
* **`MutationObserver` 的高效使用**:
  * 当使用 `MutationObserver` 监听DOM变化以响应动态内容时 (如异步加载的回复列表)，注意优化其回调函数的执行效率。
  * 避免在回调函数中执行过多、过于频繁的DOM重查询或代价高昂的重绘操作。
  * 如果可能，对回调函数的执行进行防抖处理，或更精确地处理 `mutationsList` 中的记录，只针对真正需要处理的节点进行操作。
* **脚本加载策略**:
  * **`defer` 属性：** 对于大多数非阻塞渲染的脚本，推荐在 `<script>` 标签上使用 `defer` 属性。这能确保脚本在HTML解析完毕后、`DOMContentLoaded` 事件触发前按顺序执行。
  * **`async` 属性：** 仅用于那些完全独立、不依赖其他脚本执行顺序、也不操作早期DOM的脚本。
  * **按需加载 (Code Splitting / Dynamic Import)：** 对于大型的、非首屏核心功能必需的JS模块 (例如复杂的弹窗逻辑、不常用的管理面板功能)，应考虑使用代码分割和动态导入 (`import('module.js').then(...)`) 的策略。即这些模块的JS代码只在用户实际触发相关功能时才从服务器下载和执行。这能显著优化初始页面的加载性能。
    * *(注：此项可能需要构建工具如Webpack/Rollup的支持才能方便实现)*

### 3.9. 注释与文档

* **清晰注释**:
  * 对复杂的逻辑、算法、业务规则、不直观的代码段或"黑科技"写法，必须添加清晰、简洁的注释，解释其目的和工作方式。
  * 避免对显而易见的代码进行注释。
* **JSDoc**:
  * 推荐对模块、类、公共函数（尤其是工具函数或会被多处调用的函数）使用 JSDoc 风格的注释，清晰说明其功能、参数 (名称、类型、说明)、返回值 (类型、说明) 以及任何重要的行为或前置条件。

  ```javascript
  /**
   * 计算两个数字的和.
   * @param {number} a 第一个数字.
   * @param {number} b 第二个数字.
   * @returns {number} 两个数字的和.
   */
  function sum(a, b) {
      return a + b;
  }
  ```
* **代码即文档**: 编写自解释的代码是最高境界。通过良好的命名、清晰的结构和遵循单一职责原则，可以大大减少对注释的依赖。

### 3.10. 安全性

* **禁止动态代码执行**: 严禁使用 `eval()` 和 `new Function(string)`。避免使用 `setTimeout(string)` 和 `setInterval(string)` 的字符串参数形式。
* **输入验证与输出编码**:
  * 所有来自用户输入或不可信来源的数据，在用于业务逻辑或显示到页面前，都应进行严格的格式、类型和范围验证。
  * 在将数据动态插入到HTML页面时，遵循XSS防护原则（优先 `textContent`，谨慎 `innerHTML` 并确保编码/转义）。
* **依赖安全**: 定期检查并更新项目所使用的第三方JavaScript库（如果有），关注其已知的安全漏洞。

### 3.11. 代码风格与审查

* **一致性**: 遵循本文档及团队已有的代码风格和约定，保持项目代码风格的整体一致性。
* **可读性优先**: 优先保证代码的可读性和可维护性。使用适当的空格、缩进、空行等方式格式化代码，使其易于阅读。
* **简洁性**: 避免不必要的复杂性，力求代码简洁明了，用最简单直接的方式解决问题。
* **代码审查**: 鼓励团队成员之间进行代码审查 (Code Review)。这是提高代码质量、发现潜在问题、知识共享和促进团队整体水平提升的有效手段。

### 3.12. 其他重要原则

* **不引入不必要的库**: 除非确实能够显著提升开发效率、解决复杂问题或提供必要的兼容性，否则避免随意引入大型框架或第三方库，特别是对于可以通过原生JavaScript或少量代码就能实现的功能。保持项目轻量。
* **渐进增强**: 在设计和实现功能时，考虑其在JavaScript被禁用或执行出错时的表现。核心内容和功能应尽可能通过HTML和CSS保证基本可用性，JavaScript用于增强用户体验和提供高级交互。

## 4. 工具与实践建议

* **ESLint/JSHint**: 考虑在项目中引入JavaScript Linter工具，配置统一的规则集，帮助团队成员在编码阶段就发现并修正不规范或有潜在问题的代码。
* **代码格式化工具 (Prettier)**: 考虑使用Prettier等代码格式化工具，在代码提交前自动格式化，保证团队代码风格的高度一致。
* **浏览器开发者工具**: 熟练使用现代浏览器的开发者工具进行调试、性能分析、网络监控等。

## 5. 附录：常见问题与反模式 (FAQ / Anti-Patterns) - (可选，后续补充)

* 可以根据项目中反复出现的问题或常见的错误实践，在此处进行总结和警示。

---

**本文档会根据项目发展和团队经验持续更新。**
