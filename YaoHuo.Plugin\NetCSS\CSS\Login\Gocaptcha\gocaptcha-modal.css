/* GoCap<PERSON><PERSON>dal Styles - 弹窗遮罩样式 */

/* 默认隐藏原始的gocaptcha-wrap容器 */
#gocaptcha-wrap {
    display: none !important;
}

/* 在弹窗中显示gocaptcha-wrap */
.gocaptcha-modal-wrapper #gocaptcha-wrap {
    display: block !important;
    width: auto !important;
    margin: 0 !important;
}

/* 当包含备用 Turnstile 验证码时显示容器 */
#gocaptcha-wrap:has(#backup-turnstile-widget) {
    display: block !important;
}

/* 备用 Turnstile 验证码样式 - 与输入框左对齐 */
#backup-turnstile-widget {
    text-align: left;
}

/* 确保备用 Turnstile 内的 iframe 左对齐 */
#backup-turnstile-widget iframe {
    margin: 0;
    display: block;
}

/* 遮罩层 */
.gocaptcha-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    align-items: center;
    justify-content: center;
}

.gocaptcha-modal-overlay.show {
    display: flex !important;
    opacity: 1;
}

/* 弹窗容器 - 透明包装器 */
.gocaptcha-modal-wrapper {
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    max-width: 90vw;
    max-height: 90vh;
}

/* 为 Turnstile 弹窗特殊放宽父容器宽度 */
.gocaptcha-modal-wrapper.turnstile-modal {
    max-width: 95vw;
}

.gocaptcha-modal-overlay.show .gocaptcha-modal-wrapper {
    transform: scale(1);
}

/* 为GoCaptcha组件添加弹窗样式 */
.gocaptcha-modal-wrapper .go-captcha {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4) !important;
    overflow: hidden !important;
    border: none !important;
}

/* 验证失败错误提示覆盖层 */
.gocaptcha-error-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    text-align: center;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
    pointer-events: none;
}

.gocaptcha-error-overlay.show {
    opacity: 1;
    transform: translateY(0);
}

/* 验证失败抖动动画 */
@keyframes gocaptcha-shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
    20%, 40%, 60%, 80% { transform: translateX(8px); }
}

.gocaptcha-shake-animation {
    animation: gocaptcha-shake 0.6s ease-in-out;
}

/* 确保gc-body容器支持绝对定位的子元素 */
.gocaptcha-modal-wrapper .gc-body {
    position: relative !important;
}

/* 加载状态 */
.gocaptcha-modal-loading {
    background: white;
    padding: 50px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
    color: #333;
    min-width: 280px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.gocaptcha-modal-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f1f1f1;
    border-top: 4px solid #378d8d;
    border-radius: 50%;
    animation: gocaptcha-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes gocaptcha-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.gocaptcha-modal-loading div:last-child {
    font-size: 16px;
    color: #666;
    margin-top: 8px;
}

/* 错误状态 */
.gocaptcha-modal-error {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
    color: #dc3545;
    font-size: 16px;
    min-width: 300px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.gocaptcha-modal-error-buttons {
    margin-top: 15px;
    text-align: center;
}

.gocaptcha-modal-error-buttons button {
    margin: 5px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    font-family: inherit;
}

.gocaptcha-modal-error-buttons button:first-child {
    background-color: #007cba;
    color: white;
}

.gocaptcha-modal-error-buttons button:last-child {
    background-color: #28a745;
    color: white;
}

.gocaptcha-modal-error-buttons button:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.gocaptcha-modal-error button {
    background: #378d8d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 20px;
    transition: all 0.2s ease;
    font-family: inherit;
}

.gocaptcha-modal-error button:hover {
    background: #4ba0a0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(55, 141, 141, 0.3);
}

/* 成功状态 */
.gocaptcha-modal-success {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    text-align: center;
    color: #28a745;
    font-size: 16px;
    min-width: 250px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.gocaptcha-modal-success div:first-child {
    font-size: 24px;
    margin-bottom: 8px;
}

/* 防止页面滚动 */
body.gocaptcha-modal-open {
    overflow: hidden;
}

/* 移动端响应式 */
@media (max-width: 480px) {
    .gocaptcha-modal-wrapper {
        transform: scale(0.8);
        margin: 20px;
        max-width: calc(100vw - 40px);
        width: auto;
    }
    
    .gocaptcha-modal-overlay.show .gocaptcha-modal-wrapper {
        transform: scale(0.9);
    }
    
    .gocaptcha-modal-loading,
    .gocaptcha-modal-error,
    .gocaptcha-modal-success {
        margin: 20px;
        padding: 30px;
        min-width: auto;
        max-width: calc(100vw - 40px);
    }
    
    .gocaptcha-error-overlay {
        font-size: 12px;
        padding: 6px 10px;
    }
    
    /* Turnstile 移动端适配 */
    .gocaptcha-modal-turnstile {
        margin: 20px;
        max-width: calc(100vw - 40px);
        width: auto;
    }
    
    .gocaptcha-modal-header {
        padding: 12px 16px;
    }
    
    .gocaptcha-modal-header h3 {
        font-size: 14px;
    }
    
    .gocaptcha-modal-body {
        /* 在移动端也减少内边距 */
        padding: 10px !important; 
        min-height: 100px;
        /* 确保 flex 属性保留 */
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    #turnstile-widget {
        max-width: 280px;
        transform: scale(0.9);
        transform-origin: center;
    }
    
    #turnstile-widget iframe {
        width: 280px !important;
    }
}

/* 确保GoCaptcha内部元素正常显示 */
.gocaptcha-modal-wrapper .go-captcha * {
    box-sizing: border-box;
}

/* 额外确保弹窗显示 */
.gocaptcha-modal-overlay.show {
    display: flex !important;
}

/* Turnstile 验证码特殊样式 */
.gocaptcha-modal-turnstile {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    /* 调整最大宽度和宽度 */
    max-width: 420px; 
    width: 95%; 
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.gocaptcha-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.gocaptcha-modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.gocaptcha-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #999;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.gocaptcha-modal-close:hover {
    background: #e9ecef;
    color: #666;
}

.gocaptcha-modal-body {
    /* 减少内边距 */
    padding: 15px;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Turnstile 组件容器 */
#turnstile-widget {
    margin: 0 auto;
    max-width: 300px;
    overflow: visible; /* 改为 visible */
}

/* 确保 Turnstile iframe 不会溢出 */
#turnstile-widget iframe {
    max-width: 100% !important;
    width: 300px !important;
    height: auto !important;
} 