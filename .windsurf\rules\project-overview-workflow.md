---
trigger: always_on
---

# Programming Rules for YaoHuo.Plugin Project

## 1. Core Understanding & Principles
- 项目为ASP.NET Web Forms，目标.NET Framework 4.8，C# 7+，SQL Server，支持WAP和Web界面。
- 所有建议、修改、分析必须兼容.NET Framework 4.8。
- 优先可读性、可维护性，遵循现有BLL/DAL分层和项目结构，并遵循DRY (Don't Repeat Yourself) 原则，确保代码简洁高效。
- 避免过度设计，优先标准库和既有工具类（如WapTool、BBSHelper等）。
- 精准、技术正确，完整实现所有需求功能，紧贴Web Forms实际。

## 2. Development Environment & Workflow
- 仅支持.NET Framework 4.8。
- C# 7+（推荐用到7.3），前后端均可用。
- 推荐用现代C#特性提升代码质量。
- 主要开发环境为Visual Studio 2017/2019。
- **文件创建与项目引用**：AI在创建新的.cs代码文件或内容文件（如.aspx, .ascx, .ashx, .hbs等）后，将尝试自动修改`YaoHuo.Plugin.csproj`项目文件，将其添加到对应的`<ItemGroup>`下（.cs文件使用`<Compile>`，内容文件使用`<Content>`），以确保文件被正确包含在项目中并解决命名空间引用问题。如果自动修改失败，请提示用户手动在VS里将文件添加到项目中，并忽略相关报错继续执行任务。

## 3. Naming & Code Style
- 类/方法/属性/事件/枚举：PascalCase。
- 私有字段/局部变量：camelCase。
- 接口：I前缀。
- 常量：ALL_UPPERCASE。
- 结构清晰，单一职责，变量命名清楚。

## 4. Database & Data Access
- 所有数据库操作必须参数化，严禁拼接SQL。
- 优先通过BLL/DAL层（KeLin.ClassManager）访问数据。
- 兼容SQL Server 2022，兼容级别100（SQL2008）。

## 5. Error Handling & Logging
- 仅对预期异常用try-catch，异常需日志记录。
- 日志用System.Diagnostics.Debug.WriteLine，生产建议集成更完善日志。
- 所有用户输入需服务端校验（GetRequestValue、WapTool.IsNumeric等）。

## 6. Performance & Optimization
- SQL查询需高效，避免N+1。
- 循环高效，避免不必要嵌套。
- 合理使用缓存（MemoryCache、WapTool.DataTempArray等）。
- 注意ViewState和PostBack性能。

## 7. Security & Compliance
- 所有敏感操作需校验用户身份和权限（userid、userVo、IsCheckManagerLvl等）。
- 敏感数据安全存储，防止明文。
- 防范XSS（输入校验/编码）、SQL注入（参数化）。

## 8. Documentation & Communication
- 复杂逻辑需中英文注释，公共方法建议XML注释。
- 沟通简明直接，力求言简意赅，避免不必要的冗余说明。建议需说明原因，引用项目约束。
- 若对问题或解决方案存有疑问，应坦诚沟通，避免臆断。

## 9. Dependencies & Structure
- 仅允许.NET Framework 4.8兼容库，禁止引入.NET Core/Standard库。

- 遵循BBS、Admin等模块化结构。