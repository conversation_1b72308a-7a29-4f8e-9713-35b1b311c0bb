---
description: 
globs: *.js
alwaysApply: false
---
## JavaScript 编码规范

### 1. 兼容性与环境
- **目标浏览器**: 确保代码兼容 IE11+、Microsoft Edge 及现代版 Chrome。
- **ES6+特性**: 谨慎使用 ES6+ 新特性。若使用，确保通过测试或转译保证兼容性。优先使用现代特性以提升代码可读性和可维护性，兼容性是首要前提。
- **宿主环境**: 明确代码运行在浏览器端，注意API差异。

### 2. 命名规范
- **变量与函数**: `camelCase` (例: `myVariable`, `calculateValue()`)。
- **构造函数/类**: `PascalCase` (例: `MyClass`, `UserService`)。
- **常量**: 模块内常量推荐 `ALL_UPPERCASE` (例: `MAX_USERS`) 或 `camelCase` (作为配置对象属性时)。
- **描述性**: 命名应清晰、准确，避免模糊或过简。
- **全局变量**: **严格禁止。必须使用模块模式 (IIFE) 或 ES6 模块 (若环境支持) 封装代码，避免污染全局作用域和命名冲突。**

### 3. 代码结构与模块化
- **单一职责**:
    - **函数应短小精悍，职责单一。**
    - **每个JS文件 (模块) 应聚焦于单一核心功能或业务关注点。避免将不相关的功能耦合在同一文件中。**
- **模块化封装**: **强烈推荐使用 IIFE (立即调用函数表达式) 封装每个独立功能的JS文件，形成独立作用域。**
- **配置文件顶部常量化**: 模块内部的可配置参数 (如动画时长、API端点、特定阈值) 应在模块顶部通过 `const CONFIG = {...}` 形式集中定义。
- **文件组织**: 遵循项目既有结构，通常存放于指定目录 (例如 `/NetCSS/JS/`)。

### 4. DOM 操作与事件处理
- **选择器与缓存**:
    - 优先使用 `document.getElementById` (ID已知时) 和 `document.querySelector` / `document.querySelectorAll`。
    - **频繁访问的DOM元素必须缓存，避免重复查询。**
    - **对 `querySelector`/`querySelectorAll` 的结果，在使用前必须进行存在性检查 (判空)，防止运行时错误。**
- **DOM 修改**:
    - 减少直接DOM操作次数。批量修改DOM时考虑 `DocumentFragment`。
    - **对于动态UI构建，推荐将数据与视图分离。考虑使用模板字符串或轻量级模板方法，避免大量手动 `createElement` 和 `innerHTML` 拼接，以提高可维护性。**
- **事件处理**:
    - 使用 `element.addEventListener()` 绑定事件。
    - **元素移除或模块销毁时，必须显式调用 `removeEventListener()` 清理相关事件监听器，防止内存泄漏。**
    - 考虑事件委托处理动态或大量相似元素的事件。
- **XSS防护**: 优先使用 `element.textContent`。若必须使用 `innerHTML`，确保内容已严格清理和转义。
- **减少硬编码依赖**: 避免对特定、脆弱的DOM结构和CSS类名的硬编码依赖。优先通过更稳定的父级元素、数据属性 (`data-*`) 定位。

### 5. 数据管理
- **数据分离**: **强烈建议将大量的静态配置数据 (如URL映射、常量列表) 从JS代码中分离，存放于JSON文件并按需异步加载，或作为HTML数据属性读取。**
- **localStorage/sessionStorage**: 注意存取值均为字符串，读取后可能需类型转换和判空。

### 6. 与后端交互 (AJAX)
- **统一请求方式**: **推荐项目中统一使用 `fetch` API 进行异步请求，并可考虑封装通用请求函数。**
- **参数校验**: 前后端均需校验。
- **异步处理**: 使用 `Promise` 或 `async/await`。
- **错误处理**: 妥善处理网络和服务端错误，提供用户反馈。
- **iframe通信安全**: 若使用 `postMessage` 与iframe通信，**必须校验 `event.origin`**，确保消息来源的安全性。

### 7. 错误处理与日志记录
- **`try...catch`**: 包裹可能抛出异常的关键操作。
- **明确错误信息**: 提供有意义的错误信息。
- **`parseInt`/`parseFloat`**: **对结果使用 `isNaN()` 进行检查。**
- **日志**: 开发环境使用 `console`，生产环境考虑上报。

### 8. 性能优化
- **核心原则**: 减少重绘与回流，节流与防抖高频事件。
- **图片优化**: **考虑对非首屏或大量图片使用懒加载策略。**
- **`MutationObserver`**: 高效使用，注意优化回调逻辑，避免在回调中执行过多或频繁的DOM重操作。
- **按需加载**: **对于大型、非首屏必需的JS模块，考虑按需加载 (动态 `import()` 或其他策略) 以优化首屏性能。**

### 9. 注释与文档
- **清晰注释**: 对复杂逻辑、业务规则或不直观代码段添加。
- **JSDoc**: 推荐对公共函数/模块使用 JSDoc 风格注释。

### 10. 安全性
- **禁止动态代码执行**: 严禁 `eval()`, `new Function(string)`。
- **输入验证与输出编码**: 严格校验用户输入，适当编码输出数据。
- **依赖安全**: 定期检查并更新项目依赖。

### 11. 代码风格与审查
- **一致性**: 遵循团队已有风格。
- **可读性优先**: 格式化代码，力求简洁明了。
- **代码审查**: 鼓励团队代码审查。

### 12. 其他重要原则
- **不引入不必要的库**: 除非确实需要，避免引入大型库。
- **渐进增强**: 考虑核心功能在无JS情况下的可用性 (若适用)。