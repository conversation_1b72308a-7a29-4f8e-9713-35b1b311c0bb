using System;
using System.Web;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin
{
    public class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("=== Application_Start: Beginning initialization ===");
            
            try
            {
                // 触发 TemplateService 静态构造函数的执行
                // 新的 TemplateService 会在首次访问时自动初始化
                var viewMode = TemplateService.GetViewMode(); // 这会触发静态构造函数
                System.Diagnostics.Debug.WriteLine($"Application_Start: TemplateService initialized. Default view mode: {viewMode}");
                
                // 获取详细的调试信息
                var debugInfo = TemplateService.GetDebugInfo();
                System.Diagnostics.Debug.WriteLine("Application_Start: TemplateService debug info:");
                System.Diagnostics.Debug.WriteLine(debugInfo);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"!!! Application_Start: ERROR during TemplateService initialization: {ex.ToString()}");
                // 不抛出异常，避免阻止应用程序启动
            }
            
            System.Diagnostics.Debug.WriteLine("=== Application_Start: Completed ===");
        }

        protected void Session_Start(object sender, EventArgs e)
        {

        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {

        }
    }
} 