{{! 状态消息显示 }}
{{#if Message.HasMessage}}
<div class="message {{Message.Type}} mb-4">
    {{#if Message.IsSuccess}}
    <div class="card text-center border border-border-light">
        <div class="bg-white text-text-primary py-8 px-4 pb-6 border-b border-border-light">
            <div class="mb-4">
                <i data-lucide="check-circle" class="w-16 h-16 stroke-2 text-primary mx-auto"></i>
            </div>
            <h3 class="text-xl font-semibold m-0 text-text-primary">兑换成功</h3>
        </div>
        <div class="py-6 px-4 pb-8 bg-white">
            <p class="text-text-secondary mb-4 leading-6">{{Message.Content}}</p>
            <div class="flex justify-center items-center">
                <a href="{{FormData.HiddenFields.BackUrl}}" class="btn btn-primary py-3 px-6 text-base">
                    <i data-lucide="arrow-left" class="mr-2"></i>
                    返回
                </a>
            </div>
        </div>
    </div>
    {{else}}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
            <i data-lucide="alert-circle" class="w-5 h-5 text-red-500 mt-0.5 mr-3"></i>
            <div class="text-red-800">{{Message.Content}}</div>
        </div>
    </div>
    {{/if}}
</div>
{{/if}}

{{! 只有在非成功状态时才显示主要内容 }}
{{#unless Message.IsSuccess}}

{{! 账户信息卡片 }}
<div class="card mb-4">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="wallet" class="card-icon"></i>
            我的资产
        </h2>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的 RMB</div>
                <div class="text-xl font-medium text-primary">{{UserAssets.RMBDisplay}}</div>
            </div>
            <div class="text-center">
                <div class="text-gray-600 text-sm mb-1">我的{{UserAssets.MoneyName}}</div>
                <div class="text-xl font-medium text-primary">{{UserAssets.MoneyDisplay}}</div>
            </div>
        </div>
    </div>
</div>

{{! 切换标签 }}
<div class="max-w-full mx-auto mb-4" style="padding: 0 1rem;">
    <div class="bg-gray-100 rounded-full p-1 flex">
        <button id="exchangeTab" class="flex-1 py-2 px-4 rounded-full bg-primary text-white text-center transition-all">兑换</button>
        <button id="rechargeTab" class="flex-1 py-2 px-4 rounded-full text-gray-700 text-center transition-all">充值</button>
    </div>
</div>

{{! 兑换模块 }}
<div id="exchangeSection" class="card mb-4">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="repeat" class="card-icon"></i>
            兑换{{UserAssets.MoneyName}}
        </h2>
    </div>
    <div class="card-body">
        {{#if ExchangeSettings.IsEnabled}}

        <form id="exchange-form" action="{{FormData.ActionUrl}}" method="post">
            <div class="form-group">
                <label class="form-label required">输入金额</label>
                <input type="number"
                       id="exchangeAmount"
                       name="tomoney"
                       class="form-input"
                       placeholder="请输入RMB数字"
                       value="{{FormData.ExchangeAmount}}"
                       step="0.01"
                       min="0.01"
                       autocomplete="off"
                       readonly
                       onfocus="this.removeAttribute('readonly');"
                       required>
                <div class="mt-2 bg-blue-50 rounded-lg p-3">
                    {{! 兑换比例显示（默认显示） }}
                    <div id="exchangeRateDisplay" class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-blue-500" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12.0049 22.0027C6.48204 22.0027 2.00488 17.5256 2.00488 12.0027C2.00488 6.4799 6.48204 2.00275 12.0049 2.00275C17.5277 2.00275 22.0049 6.4799 22.0049 12.0027C22.0049 17.5256 17.5277 22.0027 12.0049 22.0027ZM12.0049 20.0027C16.4232 20.0027 20.0049 16.421 20.0049 12.0027C20.0049 7.58447 16.4232 4.00275 12.0049 4.00275C7.5866 4.00275 4.00488 7.58447 4.00488 12.0027C4.00488 16.421 7.5866 20.0027 12.0049 20.0027ZM7.00488 13.0027H16.0049V15.0027H12.0049V18.0027L7.00488 13.0027ZM12.0049 9.00275V6.00275L17.0049 11.0027H8.00488V9.00275H12.0049Z"/>
                        </svg>
                        <span class="text-gray-700">{{ExchangeSettings.RateDisplay}}</span>
                    </div>
                    {{! 可兑换数量显示（输入后显示） }}
                    <div id="crystalAmountDisplay" class="flex items-center gap-2 hidden">
                        <svg class="w-5 h-5 text-blue-500" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.0049 2.00281C18.4232 2.00281 22.0049 5.58453 22.0049 10.0028C22.0049 13.2474 20.0733 16.0409 17.2973 17.296C16.0422 20.0718 13.249 22.0028 10.0049 22.0028C5.5866 22.0028 2.00488 18.4211 2.00488 14.0028C2.00488 10.7587 3.9359 7.96554 6.71122 6.71012C7.96681 3.93438 10.7603 2.00281 14.0049 2.00281ZM10.0049 8.00281C6.69117 8.00281 4.00488 10.6891 4.00488 14.0028C4.00488 17.3165 6.69117 20.0028 10.0049 20.0028C13.3186 20.0028 16.0049 17.3165 16.0049 14.0028C16.0049 10.6891 13.3186 8.00281 10.0049 8.00281ZM11.0049 9.00281V10.0028H13.0049V12.0028H9.00488C8.72874 12.0028 8.50488 12.2267 8.50488 12.5028C8.50488 12.7483 8.68176 12.9524 8.91501 12.9948L9.00488 13.0028H11.0049C12.3856 13.0028 13.5049 14.1221 13.5049 15.5028C13.5049 16.8835 12.3856 18.0028 11.0049 18.0028V19.0028H9.00488V18.0028H7.00488V16.0028H11.0049C11.281 16.0028 11.5049 15.7789 11.5049 15.5028C11.5049 15.2573 11.328 15.0532 11.0948 15.0109L11.0049 15.0028H9.00488C7.62417 15.0028 6.50488 13.8835 6.50488 12.5028C6.50488 11.1221 7.62417 10.0028 9.00488 10.0028V9.00281H11.0049ZM14.0049 4.00281C12.2214 4.00281 10.6196 4.78097 9.52064 6.01629C9.68133 6.00764 9.84254 6.00281 10.0049 6.00281C14.4232 6.00281 18.0049 9.58453 18.0049 14.0028C18.0049 14.1655 18 14.327 17.9905 14.4873C19.2265 13.3885 20.0049 11.7866 20.0049 10.0028C20.0049 6.6891 17.3186 4.00281 14.0049 4.00281Z"/>
                        </svg>
                        <span class="text-gray-700">可兑换</span>
                        <span id="crystalAmount" class="text-primary font-medium">0</span>
                        <span class="text-gray-700">{{UserAssets.MoneyName}}</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label required">我的密码</label>
                <input type="password"
                       id="password"
                       name="changePW"
                       class="form-input"
                       placeholder="请输入密码确认兑换"
                       autocomplete="off"
                       readonly
                       onfocus="this.removeAttribute('readonly');"
                       required>
            </div>

            {{! 隐藏字段 }}
            <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
            <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
            <input type="hidden" name="backurl" value="{{FormData.HiddenFields.BackUrl}}">

            <button type="submit" id="exchangeBtn" class="btn btn-primary w-full py-3">
                <i data-lucide="repeat" class="w-5 h-5 mr-2"></i>
                确认兑换
            </button>
        </form>
        {{else}}
        <div class="bg-red-50 rounded-lg p-4 text-center">
            <i data-lucide="x-circle" class="w-12 h-12 text-red-500 mx-auto mb-2"></i>
            <p class="text-red-800 font-medium">站长已关闭此功能</p>
        </div>
        {{/if}}
    </div>
</div>

{{! 充值模块 }}
<div id="rechargeSection" class="card mb-4 hidden">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="scan-line" class="card-icon"></i>
            扫码充值
        </h2>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-2 gap-4 mb-4">
            {{#if RechargeInfo.SupportsAlipay}}
            <button id="alipayBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-blue-50 text-blue-600 rounded-lg transition-all">
                <svg class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.4079 16.7903C16.2353 15.8398 14.6887 15.1443 13.768 14.704C12.368 16.4 10.896 17.424 8.688 17.424C6.48 17.424 5 16.064 5.176 14.392C5.296 13.296 6.048 11.504 9.304 11.816C11.024 11.976 11.808 12.296 13.216 12.76C13.576 12.096 13.88 11.36 14.104 10.584H7.88V9.968H10.952V8.864H7.2V8.184H10.952V6.592C10.952 6.592 10.984 6.344 11.264 6.344H12.8V8.192H16.8V8.872H12.8V9.976H16.064C15.768 11.2 15.312 12.32 14.744 13.296C15.2543 13.4776 16.841 13.9718 19.5043 14.7785C19.8249 13.913 20 12.977 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C14.6217 20 16.9488 18.7389 18.4079 16.7903ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM8.432 16.368C9.872 16.368 11.256 15.496 12.392 14.016C10.784 13.24 9.448 12.856 7.952 12.856C6.648 12.856 5.968 13.656 5.848 14.272C5.728 14.888 6.096 16.368 8.432 16.368Z"/>
                </svg>
                <span>支付宝</span>
            </button>
            {{/if}}
            {{#if RechargeInfo.SupportsWechat}}
            <button id="wechatBtn" class="flex items-center justify-center gap-2 py-3 px-4 bg-gray-100 text-gray-400 rounded-lg transition-all">
                <svg class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.1458 8.99325L9.34705 14.6006L9.27753 14.6465C9.18483 14.6925 9.09213 14.7154 8.97625 14.7154C8.72133 14.7154 8.51275 14.5776 8.39688 14.3709L8.35053 14.2791L6.51971 10.329C6.49653 10.283 6.49653 10.2371 6.49653 10.1912C6.49653 10.0074 6.63558 9.86964 6.82098 9.86964C6.8905 9.86964 6.96003 9.8926 7.02955 9.93853L9.18483 11.4543C9.34705 11.5461 9.53245 11.615 9.74103 11.615C9.8569 11.615 9.97278 11.5921 10.0887 11.5461L18.3634 7.89746C16.9347 6.27313 14.6348 5.19995 12.0006 5.19995C7.57986 5.19995 4.10059 8.22235 4.10059 11.8C4.10059 13.1655 4.60024 14.4728 5.53227 15.5809C5.58056 15.6383 5.65277 15.7177 5.74666 15.8155C6.54199 16.6438 6.94301 17.7739 6.84765 18.9182L6.82289 19.2153L7.53841 18.7789C8.34812 18.2851 9.30697 18.095 10.2438 18.2426C10.4553 18.2759 10.6292 18.3015 10.7634 18.3192C11.1696 18.3728 11.5828 18.4 12.0006 18.4C16.4213 18.4 19.9006 15.3776 19.9006 11.8C19.9006 10.8036 19.6307 9.85022 19.1458 8.99325ZM6.19286 21.9423C6.00989 22.0566 5.79484 22.1087 5.57981 22.0908C5.02944 22.045 4.62045 21.5616 4.66631 21.0112L4.85456 18.7521C4.90224 18.1799 4.70173 17.6149 4.30407 17.2008C4.1819 17.0735 4.08111 16.9627 4.0017 16.8683C2.80622 15.447 2.10059 13.6951 2.10059 11.8C2.10059 7.0503 6.53297 3.19995 12.0006 3.19995C17.4682 3.19995 21.9006 7.0503 21.9006 11.8C21.9006 16.5496 17.4682 20.4 12.0006 20.4C11.4911 20.4 10.9906 20.3665 10.5018 20.302C10.3491 20.2819 10.1593 20.254 9.93256 20.2182C9.46412 20.1444 8.9847 20.2395 8.57985 20.4864L6.19286 21.9423Z"/>
                </svg>
                <span>微信支付</span>
            </button>
            {{/if}}
        </div>
        
        <button id="showQRCodeBtn" class="w-full flex items-center justify-center gap-2 py-3 px-4 bg-primary text-white rounded-lg mb-4">
            <i data-lucide="qr-code" class="w-5 h-5"></i>
            <span>显示付款二维码</span>
        </button>

        <div class="bg-blue-50 rounded-lg p-3 mb-4">
            <div class="flex items-start gap-2">
                <i data-lucide="info" class="text-blue-500 mt-0.5"></i>
                <div class="flex-1">
                    <p class="text-blue-800 font-medium m-0">付款时请"添加备注"你的ID</p>
                    <div class="flex items-center gap-2 mt-1">
                        <p class="text-blue-700 m-0">你的ID号为：<span id="userId" class="text-primary font-semibold">{{RechargeInfo.UserId}}</span></p>
                        <button id="copyIdBtn" class="flex items-center justify-center w-6 h-6 bg-white rounded-full shadow-sm hover:bg-gray-50 transition-colors">
                            <i data-lucide="copy" class="w-3 h-3 text-gray-500"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="border-t border-gray-100 pt-3">
            <p class="text-gray-600 text-sm m-0">{{RechargeInfo.Instructions}}</p>
        </div>
    </div>
</div>

{{/unless}}

{{! 二维码弹窗 }}
<div id="qrCodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg w-5/6 max-w-md">
        <div class="p-4 border-b border-gray-100 flex justify-between items-center">
            <h3 class="text-lg font-medium">付款二维码</h3>
            <button id="closeQRCodeBtn" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
                <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
            </button>
        </div>
        <div class="p-6 flex justify-center">
            <div class="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <img id="qrCodeImage" src="/Tupian/AliPay.jpg" alt="付款二维码" class="w-full h-full object-contain rounded-lg">
            </div>
        </div>
        <div class="p-4 border-t border-gray-100 text-center">
            <p id="qrCodeText" class="text-gray-600 text-sm m-0">请使用支付宝扫码付款</p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图标
        lucide.createIcons();

        // 标签切换功能
        setupTabSwitching();

        // 兑换计算功能
        setupExchangeCalculator();

        // 充值功能
        setupRechargeFeatures();

        // 表单验证和提交
        setupFormValidation();
    });

    // 标签切换功能
    function setupTabSwitching() {
        const rechargeTab = document.getElementById('rechargeTab');
        const exchangeTab = document.getElementById('exchangeTab');
        const rechargeSection = document.getElementById('rechargeSection');
        const exchangeSection = document.getElementById('exchangeSection');

        if (!rechargeTab || !exchangeTab || !rechargeSection || !exchangeSection) return;

        // 充值标签点击
        rechargeTab.addEventListener('click', function() {
            rechargeTab.classList.add('bg-primary', 'text-white');
            rechargeTab.classList.remove('text-gray-700');
            exchangeTab.classList.remove('bg-primary', 'text-white');
            exchangeTab.classList.add('text-gray-700');
            rechargeSection.classList.remove('hidden');
            exchangeSection.classList.add('hidden');
        });

        // 兑换标签点击
        exchangeTab.addEventListener('click', function() {
            exchangeTab.classList.add('bg-primary', 'text-white');
            exchangeTab.classList.remove('text-gray-700');
            rechargeTab.classList.remove('bg-primary', 'text-white');
            rechargeTab.classList.add('text-gray-700');
            exchangeSection.classList.remove('hidden');
            rechargeSection.classList.add('hidden');
        });
    }

    // 兑换计算功能
    function setupExchangeCalculator() {
        const exchangeAmount = document.getElementById('exchangeAmount');
        const crystalAmount = document.getElementById('crystalAmount');
        const exchangeRateDisplay = document.getElementById('exchangeRateDisplay');
        const crystalAmountDisplay = document.getElementById('crystalAmountDisplay');

        if (!exchangeAmount || !crystalAmount || !exchangeRateDisplay || !crystalAmountDisplay) return;

        // 从页面获取兑换比例
        const exchangeRate = {{ExchangeSettings.ExchangeRate}};

        exchangeAmount.addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            const crystals = Math.floor(amount * exchangeRate);
            crystalAmount.textContent = crystals.toLocaleString();

            // 根据输入状态切换显示
            if (amount > 0) {
                // 有输入时显示可兑换数量
                exchangeRateDisplay.classList.add('hidden');
                crystalAmountDisplay.classList.remove('hidden');
            } else {
                // 无输入时显示兑换比例
                exchangeRateDisplay.classList.remove('hidden');
                crystalAmountDisplay.classList.add('hidden');
            }
        });
    }

    // 充值功能
    function setupRechargeFeatures() {
        const alipayBtn = document.getElementById('alipayBtn');
        const wechatBtn = document.getElementById('wechatBtn');
        const showQRCodeBtn = document.getElementById('showQRCodeBtn');
        const qrCodeModal = document.getElementById('qrCodeModal');
        const closeQRCodeBtn = document.getElementById('closeQRCodeBtn');
        const copyIdBtn = document.getElementById('copyIdBtn');
        const userId = document.getElementById('userId');
        const qrCodeImage = document.getElementById('qrCodeImage');
        const qrCodeText = document.getElementById('qrCodeText');

        // 当前选择的支付方式（默认支付宝）
        let currentPaymentMethod = 'alipay';

        // 支付方式选择
        if (alipayBtn && wechatBtn) {
            alipayBtn.addEventListener('click', function() {
                alipayBtn.classList.remove('bg-gray-100', 'text-gray-400');
                alipayBtn.classList.add('bg-blue-50', 'text-blue-600');
                wechatBtn.classList.remove('bg-green-50', 'text-green-600');
                wechatBtn.classList.add('bg-gray-100', 'text-gray-400');
                currentPaymentMethod = 'alipay';
                updateQRCode();
            });

            wechatBtn.addEventListener('click', function() {
                wechatBtn.classList.remove('bg-gray-100', 'text-gray-400');
                wechatBtn.classList.add('bg-green-50', 'text-green-600');
                alipayBtn.classList.remove('bg-blue-50', 'text-blue-600');
                alipayBtn.classList.add('bg-gray-100', 'text-gray-400');
                currentPaymentMethod = 'wechat';
                updateQRCode();
            });
        }

        // 更新二维码和文本
        function updateQRCode() {
            if (!qrCodeImage || !qrCodeText) return;

            if (currentPaymentMethod === 'alipay') {
                qrCodeImage.src = '/Tupian/AliPay.jpg';
                qrCodeText.textContent = '请使用支付宝扫码付款';
            } else if (currentPaymentMethod === 'wechat') {
                qrCodeImage.src = '/Tupian/WeChat.png';
                qrCodeText.textContent = '请使用微信扫码付款';
            }
        }

        // 显示二维码弹窗
        if (showQRCodeBtn && qrCodeModal) {
            showQRCodeBtn.addEventListener('click', function() {
                updateQRCode(); // 确保显示正确的二维码
                qrCodeModal.classList.remove('hidden');
                lucide.createIcons();
            });
        }

        // 关闭二维码弹窗
        if (closeQRCodeBtn && qrCodeModal) {
            closeQRCodeBtn.addEventListener('click', function() {
                qrCodeModal.classList.add('hidden');
            });

            // 点击弹窗外部关闭
            qrCodeModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
        }

        // 复制用户ID
        if (copyIdBtn && userId) {
            copyIdBtn.addEventListener('click', function() {
                const id = userId.textContent;
                navigator.clipboard.writeText(id).then(() => {
                    // 显示复制成功提示
                    showToast('复制成功');

                    // 临时改变图标
                    copyIdBtn.innerHTML = '<i data-lucide="check" class="w-3 h-3 text-green-500"></i>';
                    lucide.createIcons();

                    setTimeout(() => {
                        copyIdBtn.innerHTML = '<i data-lucide="copy" class="w-3 h-3 text-gray-500"></i>';
                        lucide.createIcons();
                    }, 1500);
                }).catch(() => {
                    showToast('复制失败，请手动复制');
                });
            });
        }
    }

    // 表单验证和提交
    function setupFormValidation() {
        const form = document.getElementById('exchange-form');
        const submitBtn = document.getElementById('exchangeBtn');
        const amountInput = document.getElementById('exchangeAmount');
        const passwordInput = document.getElementById('password');

        if (!form || !submitBtn) return;

        form.addEventListener('submit', function(e) {
            // 基础验证
            if (amountInput && !amountInput.value.trim()) {
                e.preventDefault();
                showFieldError(amountInput, '请输入兑换金额');
                return;
            }

            if (passwordInput && !passwordInput.value.trim()) {
                e.preventDefault();
                showFieldError(passwordInput, '请输入密码');
                return;
            }

            // 显示提交状态
            submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin mr-2"></i>兑换中...';
            submitBtn.disabled = true;

            // 重新创建图标
            setTimeout(() => {
                lucide.createIcons();
            }, 10);
        });

        // 输入框验证
        if (amountInput) {
            amountInput.addEventListener('input', function() {
                clearFieldError(this);
            });
        }

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                clearFieldError(this);
            });
        }
    }

    // 显示字段错误
    function showFieldError(field, message) {
        clearFieldError(field);
        field.classList.add('border-red-500');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-xs text-red-500 mt-1';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    // 清除字段错误
    function clearFieldError(field) {
        field.classList.remove('border-red-500');
        const existingError = field.parentNode.querySelector('.text-xs.text-red-500');
        if (existingError) {
            existingError.remove();
        }
    }

    // 显示Toast提示
    function showToast(message) {
        // 创建Toast元素
        const toast = document.createElement('div');
        toast.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded z-50 transition-opacity';
        toast.textContent = message;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('opacity-100');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 1500);
    }
</script>