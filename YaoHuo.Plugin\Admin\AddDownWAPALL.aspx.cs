﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Admin
{
    public class AddDownWAPALL : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string string_11 = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/BaseSiteModifyWML.aspx?siteid=" + siteid);
            if (action == "gomod")
            {
                try
                {
                    string requestValue = GetRequestValue("path");
                    requestValue = WapTool.URLtoWAP(requestValue).Replace("|", "｜");
                    MainBll.UpdateSQL("update [user] set version='" + WapTool.SetSiteDefault(siteVo.Version, requestValue, 11) + "' where userid=" + siteid);
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
        }
    }
}