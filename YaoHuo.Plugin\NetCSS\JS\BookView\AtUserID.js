/**
 * @description 妖火艾特脚本
 * <AUTHOR>
 * @date 2022-11-26
 * @version 0.0.1
*/
(function () {
    let atUserId = '';

    function sendFriendMsg() {
        if (!atUserId) {
            return;
        }

        const url = '/bbs/messagelist_add.aspx';
        const form = new FormData();
        const comment = document.getElementsByName('content')[0].value.split(' ').slice(1).join(' ');
        const title = document.title.split('-')[0];
        const path = window.location.pathname + window.location.search;

        form.append('content', `查看帖子：[url=${path}]${title}[/url]///对方回复：${comment}`);
        form.append('action', 'gomod');
        form.append('touseridlist', atUserId);
        form.append('siteid', 1000);
        form.append('backurl', 'myfile.aspx?siteid=1000');
        form.append('title', '在帖子中@了你');
        form.append('classid', '0');
        form.append('types', '0');
        form.append('issystem', '');
        form.append('g', '发送');

        const sidMatch = document.cookie.match(/sidyaohuo=(.*)/) || document.cookie.match(/sidwww=(.*)/);
        if (!sidMatch) {
            return;
        }

        const sid = sidMatch[1];
        if (!sid) {
            return;
        }

        form.append('sid', sid);

        fetch(url, {
            method: 'POST',
            body: form
        })
            .then(response => {
                if (!response.ok) {
                    console.error(`HTTP error! status: ${response.status}`);
                }
            })
            .catch(error => {
                console.error('发送消息时发生错误:', error);
            });
    }

    function searchNickname() {
        const apiUrl = `/bbs/userinfo.aspx?touserid=${atUserId}`;

        fetch(apiUrl, {
            method: 'GET'
        })
            .then(res => res.text())
            .then(html => {
                const nicknameMatch = html.match(/<title>(.*?)的空间<\/title>/);
                const nickname = nicknameMatch ? nicknameMatch[1] : '';
                const textObj = document.getElementsByName('content')[0];
                const valueSplit = textObj.value.split('昵称检索中...');
                valueSplit.splice(0, 1);
                textObj.value = `@${nickname ? nickname : 'ID号错误'} ${valueSplit.join()}`;
            });
    }

    function textMonitor() {
        const textObj = document.getElementsByName('content')[0];
        if (textObj) {
            textObj.oninput = function (e) {
                if (isNaN(e.data) || !e.data) {
                    return;
                }

                if (textObj.value.startsWith('@')) {
                    const valueSplit = textObj.value.split(' ');
                    if (valueSplit.length > 1 && valueSplit[0].length > 3 && !isNaN(valueSplit[0].split('@')[1])) {
                        atUserId = valueSplit[0].substring(1);
                        valueSplit.splice(0, 1);
                        textObj.value = `昵称检索中...${valueSplit.join()}`;
                        searchNickname();
                    }
                } else {
                    atUserId = '';
                }
            };
        }
    }

    document.addEventListener("DOMContentLoaded", () => {
        textMonitor();
    });
})(); 