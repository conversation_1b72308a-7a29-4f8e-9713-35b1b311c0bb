<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>{{PageTitle}}</title>
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
    {{!-- 移除 CDN 引入和内联配置/样式 --}}
    {{!-- <script src="https://cdn.tailwindcss.com/3.4.16"></script> --}}
    {{!-- <script>...</script> --}}
    {{!-- <style>...</style> --}}

    {{!-- 引用本地构建的 Tailwind CSS 文件 --}}
    <link rel="stylesheet" href="/Template/CSS/output.css?22">

    {{#if PageSpecificCss}}
    <link rel="stylesheet" href="{{PageSpecificCss}}">
    {{/if}}
</head>
<body>
    <div class="container">
        {{> Header PageTitle=PageTitle HeaderOptions=HeaderOptions}}
        
        <div class="main-content">
            {{{Content}}}
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // Cookie 管理函数
        function setUiPreferenceCookie(value, days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            var expires = 'expires=' + date.toUTCString();
            document.cookie = 'ui_preference=' + value + ';' + expires + ';path=/';
        }

        function getUiPreferenceCookie() {
            var name = 'ui_preference=';
            var decodedCookie = decodeURIComponent(document.cookie);
            var cookieArray = decodedCookie.split(';');
            for (var i = 0; i < cookieArray.length; i++) {
                var cookie = cookieArray[i];
                while (cookie.charAt(0) === ' ') {
                    cookie = cookie.substring(1);
                }
                if (cookie.indexOf(name) === 0) {
                    return cookie.substring(name.length, cookie.length);
                }
            }
            return '';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 卡片加载动画已移除以解决滚动问题
        });
    </script>
</body>
</html> 