﻿# YaoHuo.Plugin

## 项目简介

YaoHuo.Plugin 是基于 ASP.NET Web Forms (.NET Framework 4.8, C# 7+) 的插件式社区系统，支持WAP/WEB多端访问，集成论坛、勋章、黑名单、游戏等丰富功能，采用三层架构（UI/业务逻辑/数据访问），强调可扩展性与安全性。

---

## 项目结构与依赖关系

```mermaid
graph TD
    subgraph YaoHuo.Plugin
        A1[WebForms页面/控件]
        A2[核心工具类 Tool/WebTool/WapTool]
        A3[业务逻辑层 KeLin.ClassManager.BLL]
        A4[数据访问层 KeLin.ClassManager.DAL]
        A5[外部依赖 Dapper/自定义DLL]
        A6[静态资源 CSS/JS/图片]
        A7[配置 Web.config]
    end
  
    A1 --> A2
    A1 --> A3
    A2 --> A3
    A3 --> A4
    A4 --> A5
    A1 -.-> A6
    A1 -.-> A7
    A2 --> A5
```

### 主要目录与模块

- **首页/登录/我的地盘/好友/空间/消息/银行/勋章/游戏**：对应各自的 .aspx 页面及 code-behind
- **BBS**：论坛模块，含发帖、回帖、点赞、打赏、黑名单等
- **Admin**：后台管理，内容/用户/系统设置
- **Album**：相册模块
- **Games**：小游戏（如吹牛）
- **XinZhang**：勋章相关
- **WebSite**：核心工具类
- **Control**：可复用用户控件（如 FaceAndReply、LikeModule）
- **Web.config**：全局配置
- **Lib/**：外部依赖DLL
- **packages.config**：NuGet依赖

---

## 插件功能

1. 黑名单功能
2. 对贴子奖赏功能
3. 显示与隐藏勋章，重复购买过滤

---

## 主要功能点

- 论坛发帖/回帖/点赞/奖赏/黑名单
- 勋章系统（显示/隐藏/购买过滤）
- 货币转账（转账、打赏、手续费）
- 消息中心（一键阅读）
- 游戏功能（如吹牛）
- 管理后台（内容/用户/系统）
- 多端适配（WAP/WEB）
- 严格参数化SQL，防注入
- 多语言支持
- 缓存与性能优化

---

## 技术架构

- **UI层**：.aspx/.ascx + code-behind，负责页面渲染与交互
- **业务逻辑层**：KeLin.ClassManager.BLL 及自定义Bll，处理业务规则
- **数据访问层**：KeLin.ClassManager.DAL，所有数据库操作均参数化
- **工具类**：WapTool/WebTool 等，提供通用方法
- **外部依赖**：Dapper、AspNetPager、KeLin系列DLL等

---

## 依赖

- .NET Framework 4.8
- C# 7+
- Dapper 2.1.66
- KeLin.ClassManager/KeLin_WebSite/等自定义DLL
- Microsoft.CodeDom.Providers.DotNetCompilerPlatform

## C# 版本支持说明

- **后端代码（.cs 文件）**：项目构建配置支持 C# 7+（通常最高到 7.3），可放心使用字符串内插、模式匹配、元组、表达式体成员等现代特性。
- **前端内联代码（.aspx/.ascx）**：集成 `Microsoft.CodeDom.Providers.DotNetCompilerPlatform`（Roslyn）后，页面内联 C# 代码同样支持 C# 7.x 绝大多数特性，实现与后端一致的现代语法体验。
- **注意**：如遇极少数特性不兼容，通常是 .NET Framework 4.8 基类库（BCL）本身的限制，而非编译器问题。

---

## BBS模块分层与重构最佳实践

### 推荐分层结构

- **页面层（UI）**：如 Book_Re.aspx.cs、Book_View.aspx.cs
  - 只负责参数收集、栏目配置解析、权限校验、调用服务、渲染结果。
  - **所有栏目配置（如 `smallimg` 字段）的解析和基于这些配置的直接业务判断（例如，是否允许匿名回帖、回帖最小/最大字数限制等）必须保留在页面层。这些逻辑不应迁移到 Service 层或 Helper 层。**
  - 页面层可以通过调用私有方法来组织和简化其自身的逻辑，例如将复杂的初始化、参数处理、特定场景的验证逻辑（这些验证仍然依赖页面上下文或基类成员）封装在页面类内部。
- **服务层（Service）**：如 ReplyService.cs、BBSLikeService.cs、BBSGuessService.cs
  - 只做与栏目配置无关的通用业务（如纯粹的内容过滤算法、通用的DTO组装、不涉及特定页面上下文的消息推送、通用日志记录等）。
  - **应避免直接依赖 `HttpContext`、`HttpRequest`、`HttpResponse`、`Session`、`ViewState`、`Page` 基类成员等 ASP.NET Web Forms 特有的页面上下文对象。如果需要这些信息，应由页面层提取并作为简单参数传递给服务层。**
  - 依赖参数通过方法参数或上下文对象（DTOs/VOs）传递。
- **仓储层（Repository/DataAccess）**：如 ReplyRepository.cs、UserPreferencesRepository.cs
  - 负责所有自定义SQL、复杂分页查询、统计聚合等直接与数据库交互的操作。
  - **只能用 DataAccess/Repository 实现自定义SQL，自动生成的 BLL 类不可编辑，其提供的方法应视为原子操作。**
- **工具/辅助层（Helper）**：如 ReplyHelper.cs、BBSHelper.cs、BookViewHelper.cs
  - **只做纯粹的、无状态的、不依赖特定页面上下文的操作，例如字符串处理、通用数据模型转换（如 `List<ReplyData>` 到 `List<wap_bbsre_Model>`）、不涉及业务规则的URL构建等。**
  - **Helper 方法应该是静态的，并且不应直接访问或修改页面实例的状态（如 `this.bookVo`, `this.listVo`）。所有依赖都应通过参数传入。**
  - **避免在 Helper 中进行依赖 `base.siteid`、`base.userid` 等基类成员的数据库查询或业务逻辑判断。**
- **模型层（Model）**：如 GuessData.cs、ReplyData.cs
  - 只定义数据结构和DTO/VO，**建议全项目统一类型，避免重复定义**。

### 推荐BBS目录结构

```
BBS/
  ├─ Base/           // 页面/功能基类
  ├─ Model/          // 数据结构、DTO/VO
  ├─ Service/        // 业务服务（Service后缀）
  ├─ Repository/     // 数据访问（Repository后缀）
  └─ Helper/         // 无状态工具类（Helper后缀）
```

### 命名规范

- Service层：`XXXService.cs`，如 `ReplyService.cs`
- Repository层：`XXXRepository.cs`，如 `ReplyRepository.cs`
- Helper层：`XXXHelper.cs`，如 `ReplyHelper.cs`
- Model层：`XXXData.cs`、`XXXModel.cs`（如 `wap_bbsre_Model.cs` 如果独立定义）或 `XXXDto.cs`，如 `GuessData.cs`
- Base层：`BaseXXXPage.cs`，如 `BaseBBSListPage.cs`

### 渐进式重构与安全迁移原则

1.  **栏目配置相关的所有校验和判断，全部保留在页面层（Code-Behind），绝不迁移。** 这是因为这些配置直接影响特定页面的行为，并且通常与 `classVo` 等页面级对象紧密耦合。
2.  **Service层应专注于可重用的、与具体页面实现和栏目配置解耦的业务逻辑。**
3.  **Helper 工具类应保持无状态，不依赖页面实例，所有操作基于参数输入和返回值输出。** 它们是纯粹的功能提供者。
4.  **所有自定义SQL查询和复杂数据操作封装在Repository/DataAccess层。严禁在页面层或Service层直接拼接和执行SQL字符串。** 自动生成的BLL类方法是可信的数据操作单元。
5.  **优先在页面类内部通过提取私有方法来简化复杂方法（如 `Page_Load`, `add`, `showclass`）。** 这种内部重构风险较低，且能有效提升代码可读性和组织性，是迈向更大规模重构的安全第一步。
    *   例如，将 `Page_Load` 中特定功能的初始化逻辑（如 `tofloor` 参数处理、PostBack参数设置）提取为独立的私有方法。
    *   将核心业务处理方法（如 `ProcessAddReplyLogic`）中的主要步骤（如前置校验、核心校验、实际执行、消息通知、数据更新）进一步分解为更小的、职责单一的私有方法。
6.  **DTO/VO类型建议全项目统一，减少不必要的模型转换和重复定义。**
7.  **依赖传递建议用上下文对象（如ReplyContext/PageContext）封装，或者清晰地通过方法参数传递，以减少方法参数列表的混乱。** (对于Service层和Helper层尤其重要)
8.  **每次迁移或提取方法后，都要进行彻底的功能测试，确保行为与之前完全一致，无误后再继续下一步。** （如我们之前做的逐行对比检查）
9.  **如遇到依赖页面上下文过多、难以安全解耦的逻辑块，可以暂时保留在页面层（或其私有方法中），避免为了重构而重构，导致引入新的风险或过度复杂的参数传递。** 后续可以寻找机会进一步优化。
10. **对于适合静态化的纯工具方法（如模型构建工厂方法 `BuildReplyModel`），如果它们不依赖实例状态，可以考虑迁移到静态Helper类中，以促进复用和职责分离，即使方法本身较小。**

---

## 示例：页面与后端

<details>
<summary><strong>zgcwkj.aspx 示例</strong></summary>
<div>

```aspx
<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="zgcwkj.aspx.cs" Inherits="YaoHuo.Plugin.zgcwkj" %>
<%@ Import Namespace="System" %>
<%@ Import Namespace="System.Data" %>
<%
    switch (Action)
    {
        case "del":

            break;
        default:

            break;
    }

%>
<%--输出到页面--%>
<%= strhtml %>
```

</div>
</details>



<details>
<summary><strong>zgcwkj.aspx.cs 示例</strong></summary>
<div>

```csharp
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using KeLin.WebSite;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace YaoHuo.Plugin
{
	public partial class zgcwkj : BasePage
	{
		public string InstanceNameKey = "InstanceName";

		/// <summary>
		/// 实例名称
		/// </summary>
		private string InstanceName
		{
			get
			{
				return PubConstant.GetAppString(InstanceNameKey);
			}
		}

		/// <summary>
		/// 数据库连接字符串
		/// </summary>
		private string ConnectionString
		{
			get
			{
				return PubConstant.GetConnectionString(InstanceName);
			}
		}

		/// <summary>
		/// 路由（区分操作类型）
		/// </summary>
		public string Action { get; set; }

		/// <summary>
		/// 输出到页面的字符串
		/// </summary>
		public StringBuilder strhtml { get; set; }

		/// <summary>
		/// 加载时
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		protected void Page_Load(object sender, EventArgs e)
		{
			this.Action = GetRequestValue("action");
			var id = 0;//ID
			var count = 0;//数量
			switch (Action)
			{
				case "add"://新增操作
					id = GetRequestValue("action").ToInt();
					count = Delete(id);
					strhtml.Append("新增成功");
					break;

				case "del"://删除操作
					id = GetRequestValue("action").ToInt();
					count = Delete(id);
					strhtml.Append("删除成功");
					break;

				default://默认操作
					var dataTable = GetDataTable();
					foreach (DataRow row in dataTable.Rows)
					{
						string d = row["oper_nickname"].ToString();
						strhtml.Append(d);
					}
					break;
			}
		}

		/// <summary>
		/// 获取数据
		/// </summary>
		/// <returns></returns>
		public DataTable GetDataTable()
		{
			var stringBuilder = new StringBuilder();
			stringBuilder.Append("select top 10 oper_nickname from wap_log");
			var dataSet = DbHelperSQL.ExecuteDataset(ConnectionString, CommandType.Text, stringBuilder.ToString());
			if (dataSet.Tables.Count > 0)
			{
				var dataTable = dataSet.Tables[0];
				return dataTable;
			}
			return default(DataTable);
		}

		/// <summary>
		/// 新增数据
		/// </summary>
		/// <returns></returns>
		public int Install()
		{
			var stringBuilder = new StringBuilder();
			stringBuilder.Append("insert into XinZhang(");
			stringBuilder.Append("ID,XinZhangMingChen,XinZhangTuPian,XinZhangJiaGe,ChuangJianShiJian,siteid,ShiFouMoRen,HangBiaoShi)");
			stringBuilder.Append(" values (");
			stringBuilder.Append("@ID,@XinZhangMingChen,@XinZhangTuPian,@XinZhangJiaGe,@ChuangJianShiJian,@siteid,@ShiFouMoRen,@HangBiaoShi)");
			var array = new SqlParameter[]
			{
					new SqlParameter("@ID", SqlDbType.Int),
					new SqlParameter("@XinZhangMingChen", SqlDbType.NVarChar),
					new SqlParameter("@XinZhangTuPian", SqlDbType.NVarChar),
					new SqlParameter("@XinZhangJiaGe", SqlDbType.Int),
					new SqlParameter("@ChuangJianShiJian", SqlDbType.DateTime),
					new SqlParameter("@siteid", SqlDbType.Int),
					new SqlParameter("@ShiFouMoRen", SqlDbType.Bit),
					new SqlParameter("@HangBiaoShi", SqlDbType.Int)
			};
			//array[0].Value = model.ID;
			//array[1].Value = model.XinZhangMingChen;
			//array[2].Value = model.XinZhangTuPian;
			//array[3].Value = model.XinZhangJiaGe;
			//array[4].Value = model.ChuangJianShiJian;
			//array[5].Value = model.siteid;
			//array[6].Value = model.ShiFouMoRen;
			//array[7].Value = model.HangBiaoShi;
			//DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, stringBuilder.ToString(), array);
			return 0;
		}

		/// <summary>
		/// 删除数据
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public int Delete(int id)
		{
			var stringBuilder = new StringBuilder();
			stringBuilder.Append("delete wap_log where id = @ID");
			var array = new SqlParameter[]
			{
					new SqlParameter("@ID", SqlDbType.Int),
			};
			array[0].Value = id;
			var edCount = DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, stringBuilder.ToString(), array);
			return edCount;
		}
	}
}
```

</div>
</details>



---

## 常用地址

- /admin/addtopwapALL.aspx?siteid=1000&classid=0
- /bbs/admin_WAPadvertise.aspx?action=go&siteid=1000&classid=0
- /bbs/toMoneylvl.aspx?siteid=1000
- /bbs/book_list.aspx?action=new
- /bbs/book_view_add.aspx?classid=305

---

## 特色与安全

- 插件式扩展，支持多种业务场景
- 严格参数化SQL，防止注入
- 支持多语言与多端适配
- 采用缓存与性能优化机制
- 兼容SQL Server 2022（兼容级别100）
- 代码风格统一，注重可维护性
