namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// 用户页面数据模型
    /// </summary>
    public class UserPageModel
    {
        /// <summary>
        /// 用户基本信息
        /// </summary>
        public UserInfo UserInfo { get; set; } = new UserInfo();

        /// <summary>
        /// 统计数据
        /// </summary>
        public Statistics Statistics { get; set; } = new Statistics();

        /// <summary>
        /// 资产信息
        /// </summary>
        public Assets Assets { get; set; } = new Assets();

        /// <summary>
        /// 权限信息
        /// </summary>
        public Permissions Permissions { get; set; } = new Permissions();

        /// <summary>
        /// 勋章信息
        /// </summary>
        public Medals Medals { get; set; } = new Medals();

        /// <summary>
        /// 功能链接
        /// </summary>
        public Links Links { get; set; } = new Links();

        /// <summary>
        /// 站点信息
        /// </summary>
        public SiteInfo SiteInfo { get; set; } = new SiteInfo();
    }

    /// <summary>
    /// 用户基本信息
    /// </summary>
    public class UserInfo
    {
        public string UserId { get; set; }
        public string Nickname { get; set; }
        public string DisplayName { get; set; }
        public string Level { get; set; }
        public string Identity { get; set; }
        public string EndTime { get; set; }
        public long Experience { get; set; }
        public bool IsVip { get; set; }
        public bool HasEndTime { get; set; }
    }

    /// <summary>
    /// 统计数据
    /// </summary>
    public class Statistics
    {
        public string MessageCount { get; set; }
        public string MessageAll { get; set; }
        public string FriendCount { get; set; }
        public string PostCount { get; set; }
        public string ReplyCount { get; set; }
        public string MessageDisplay => $"{MessageCount}/{MessageAll}";
    }

    /// <summary>
    /// 资产信息
    /// </summary>
    public class Assets
    {
        public long Money { get; set; }
        public long BankMoney { get; set; }
        public string MoneyName { get; set; }
        public string MoneyDisplay => Money.ToString("N0");
        public string BankDisplay => BankMoney.ToString("N0");
        public bool HasBankMoney => BankMoney > 0;
    }

    /// <summary>
    /// 权限信息
    /// </summary>
    public class Permissions
    {
        public string ManagerLevel { get; set; }
        public string AdminDisplay { get; set; }
        public bool IsAdmin => ManagerLevel == "00" || ManagerLevel == "01";
        public bool IsSuperAdmin => ManagerLevel == "00";
        public bool HasAdminPermission => ManagerLevel != "普通" && !string.IsNullOrEmpty(ManagerLevel);
    }

    /// <summary>
    /// 勋章信息
    /// </summary>
    public class Medals
    {
        public string MedalHtml { get; set; }
        public bool HasMedals => !string.IsNullOrEmpty(MedalHtml);
    }

    /// <summary>
    /// 功能链接
    /// </summary>
    public class Links
    {
        public string MailboxUrl { get; set; }
        public string FriendsUrl { get; set; }
        public string PostsUrl { get; set; }
        public string RepliesUrl { get; set; }
        public string EditProfileUrl { get; set; }
        public string RechargeUrl { get; set; }
        public string AccountDetailUrl { get; set; }
        public string ApplyMedalUrl { get; set; }
        public string BuyMedalUrl { get; set; }
        public string FavoritesUrl { get; set; }
        public string AlbumUrl { get; set; }
        public string ClanUrl { get; set; }
        public string BlacklistUrl { get; set; }
        public string AdminUrl { get; set; }
        public string SuperAdminUrl { get; set; }
        public string LogoutUrl { get; set; }
    }

    /// <summary>
    /// 站点信息
    /// </summary>
    public class SiteInfo
    {
        public string SiteId { get; set; }
        public string HttpStart { get; set; }
        public string MoneyName { get; set; }
    }
} 