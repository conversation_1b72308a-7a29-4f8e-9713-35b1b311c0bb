﻿using System.Linq;
using System.Text.RegularExpressions;
using KeLin.ClassManager.Model;
using System.Collections.Generic;
using System;

namespace YaoHuo.Plugin.Tool
{
    public static class BBSHelper
    {
        // ====== 无意义回复判定条件（从 Book_View/Book_Re 提取） ======
        private static readonly string[] Length2Conditions = new string[] {
            // 符号、特殊字符
            "！", "。", "…", "，", ",", "？", "↑", "↓", ":", ".", "(", "、", "O", "@", "灬", "⑧", "+",
            // 汉字及中文词
            "七", "哈", "好", "哇", "车", "热", "出", "葱", "长", "陈", "才", "明", "比", "高", "六", "了", "口", "食", "看", "想", "裤", "日", "刺", "啊", "次", "得", "迟", "膜", "充", "汽", "屹", "开", "先", "此", "擦", "掐", "池", "这", "炽", "持", "是", "来", "个", "洽", "乞", "驰", "的", "号", "从", "嘿", "我", "赤", "甘", "布", "搜", "搞", "村", "洗", "加", "币", "老", "阿", "拆", "抽", "黑", "弛", "沫", "奇", "一", "床", "张", "和", "弄", "秒", "寄", "他", "发", "噢", "凑", "呀", "艸", "说", "呸", "吱", "有", "除", "厉", "纽", "还", "包", "翅", "赚", "下", "就", "吭", "吧", "田", "以", "疼", "寸", "揉", "茶", "喝", "内", "可", "叽", "成", "囷", "啃", "听", "刚", "R", "妞", "嘎", "整", "超", "鲁", "照", "夸", "混", "中", "昂", "恩", "戳", "把", "吞", "奴", "存", "姐", "捡", "咦", "啥", "额", "啥", "醋", "奥", "事", "必", "早", "绿", "抵", "层", "给", "低", "风", "善", "吹", "钞", "起", "你", "忙", "喂", "咛", "合", "拉", "～", "点", "不",
        };
        private static readonly string[] Length3Conditions = new string[] {
            "吃", "开始", "hd", "重新", "查查", "oh"
        };
        private static readonly string[] Length5Conditions = new string[] {
            // 符号、特殊字符
            "，，", "。。", "，。", "c'", "？？？", "~~", "？？", "$#", "&6", "，6", "丨ㄥ", "一丿","^_^",
            // 英文、字母组合
            "zz", "dd", "uu", "qq", "hi", "cc", "rou", "wwff", "eat", "cr", "aa", "ci", "ki", "cl", "h ", "cf", "xx", "di", "ii", "qi", "xr", "khh", "roy", "vv", "tt", "lail", "kk", "fc", "gd", "fg", "gg", "ee", "em", "du", "der", "ujj", "ktn", "yy", "KANK", "tank",
            // 数字、数字组合
            "00", "08", "22", "23", "11", "14", "66", "99", "200", "44", "55", "12", "64", "333", "+30", "33", "77", "76",
            // 汉字及中文词
            "吃了", "吃肉", "抽空", "尺", "来了", "来啦", "来个", "恰", "齿", "哦", "顶", "冲", "嗯", "肉", "六六", "常规", "还有", "回家", "方法", "馋了", "地方", "成绩", "比较", "压裂", "口气", "此时", "次了", "阿里", "出", "口了", "刚刚", "碧柔", "滴滴滴", "么哭", "阿奇", "超人", "注文", "合计", "抹黑", "餐厅", "壁挂", "我们", "蒂芙尼", "撑了", "必回", "十多分", "天天", "迟了", "七了", "阿斯", "下，", "崇拜", "似乎", "笔录", "的了", "口 乞", "啊啊", "伺候", "爱1", "辞了", "铲铲", "此刻", "图图", "阿狸", "摸摸", "乞了", "日产", "吐了", "踩踩踩", "几个", "加加加", "超级", "闪电鞭", "牛莉", "集合", "查询", "输入", "我也", "挨个", "大口", "巴啦啦", "宴请", "大柴", "法辅", "我女", "门票", "仓库", "总之", "了了", "吗压", "额而", "户号", "水水", "啊，", "信号", "恢复", "吹吹", "持来", "啦啦", "快块", "臭臭", "除了", "v个", "拉心", "v那", "次次", "了.", "蚩", ".吃", "。就", "牛", "伟业", "换个", "次数", "暗示", "萨达", "日日", "兔兔", "阿松大", "丫e9", "行动", "地瓜", "迟迟", "版本", "采集", "再次", "熟读", "赤。",
        };
        private static readonly string[] Length10Conditions = new string[] {
            // 符号、特殊字符
            "口乞", "吃--", "......",
            // 英文、字母组合
            "chi", "vv吃", "cccccc", "eeeeat", "chh", "hhh",
            // 数字、数字组合
            "7了", "66666", "777", "4444", "88888", "11111",
            // 汉字及中文词
            "充充", "仓储", "大地", "计较", "纯粹", "吃吃", "肉肉", "红红火火", "去去去", "新鲜的", "吃肉啦", "奥世达", "揉揉揉", "匆匆匆"
        };
        private static readonly Regex EmojiRegex =
            new Regex(@"^([\uD800-\uDBFF][\uDC00-\uDFFF]|\u2600-\u27BF|\uFE0F)+$", RegexOptions.Compiled);

        /// <summary>
        /// 去除UBB标签，提取纯文本内容
        /// </summary>
        public static string RemoveUBB(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            // 先去除所有 [xxx]...[/xxx] 结构，只去掉标签，保留内容
            string noPairTags = Regex.Replace(input, @"\[([^\]]+)\](.*?)\[/\1\]", "$2", RegexOptions.IgnoreCase);
            // 再去除单标签 [xxx]
            return Regex.Replace(noPairTags, @"\[[^\]]+\]", "", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 判断回复是否为无意义内容（如纯符号、单字、特定短语、纯 Emoji 等）
        /// </summary>
        /// <param name="content">回复内容</param>
        /// <returns>如果是无意义回复则返回 true，否则返回 false</returns>
        public static bool IsUselessReply(string content)
        {
            // 判断是否为纯媒体UBB标签（img、audio、movie）
            if (Regex.IsMatch(content.Trim(), @"^\[(img|audio|movie)(=[^\]]+)?\][\s\S]*?\[/\1\]$", RegexOptions.IgnoreCase))
            {
                return false; // 不过滤
            }

            // **新增步骤：先移除（表情包） [img]face/*.gif[/img] 模式**
            string processedContent = Regex.Replace(content, @"\[img\]face/[^\/]+\.gif\[/img\]", "", RegexOptions.IgnoreCase);

            // 1. 先去除UBB标签 (现在是对移除了特定 img 标签的内容进行处理)
            string pureText = RemoveUBB(processedContent);
            // 2. 再处理（去除空格、换行等）(使用 \n 和 \r 而不是 \\n 和 \\r)
            string trimmed = pureText.Replace(" ", "").Replace("\n", "").Replace("\r", "");

            // 3. 对处理后的结果进行空/空白判断
            if (string.IsNullOrWhiteSpace(trimmed))
            {
                return true;
            }

            // 4. 获取处理后内容的长度和转小写（用于后续判断）
            int len = trimmed.Length;
            string lower = trimmed.ToLower();

            // 5. 进行 Emoji 判断
            if (EmojiRegex.IsMatch(trimmed))
            {
                return true;
            }

            // 6. 进行长度条件判断
            if (len < 2 && Length2Conditions.Any(cond => lower.Contains(cond)))
            {
                return true;
            }
            if (len < 4 && Length3Conditions.Any(cond => lower.Contains(cond)))
            {
                return true;
            }
            if (len < 6 && Length5Conditions.Any(cond => lower.Contains(cond)))
            {
                return true;
            }
            if (len < 10 && Length10Conditions.Any(cond => lower.Contains(cond)))
            {
                return true;
            }

            // 7. 过滤单字母（不区分大小写）
            if (Regex.IsMatch(trimmed, @"^[a-z]$", RegexOptions.IgnoreCase))
            {
                return true;
            }
            // 过滤单数字
            if (Regex.IsMatch(trimmed, @"^[0-9]$"))
            {
                return true;
            }

            // 8. 如果以上都不满足，则不是无意义回复
            return false;
        }

        /// <summary>
        /// 获取表情列表、图片和随机回复提示
        /// </summary>
        public static (string[] FaceList, string[] FaceListImg, string ReShowInfo) GetFaceAndReShowInfo(class_Model classVo)
        {
            string[] facelist = null;
            string[] facelistImg = null;
            string reShowInfo = "";

            try
            {
                // 处理表情
                if (!string.IsNullOrEmpty(classVo.bbsFace) && classVo.bbsFace.Contains('_'))
                {
                    var parts = classVo.bbsFace.Split('_');
                    if (parts.Length >= 2)
                    {
                        facelist = parts[0].Split('|');
                        facelistImg = parts[1].Split('|');
                    }
                }
                else
                {
                    // bbsFace 数据无效或格式不正确，返回 null 或空数组
                    facelist = Array.Empty<string>();
                    facelistImg = Array.Empty<string>();
                }

                // 处理随机回复提示 (reShowInfo)，保持原逻辑但增加健壮性
                if (!string.IsNullOrEmpty(classVo.bbsType) && classVo.bbsType.Contains('_'))
                {
                    var parts = classVo.bbsType.Split('_');
                    if (parts.Length >= 2 && !string.IsNullOrEmpty(parts[1]))
                    {
                        string[] options = parts[1].Split('|');
                        if (options.Length > 0)
                        {
                            Random random = new Random();
                            reShowInfo = options[random.Next(0, options.Length)]; // 从有效选项中随机选一个
                        }
                    }
                }
                // 如果 bbsType 无效或未能提取有效选项，reShowInfo 保持为 ""
            }
            catch (Exception ex)
            {
                // 记录错误日志，例如：
                // Logger.LogError("Error in BBSHelper.GetFaceAndReShowInfo: " + ex.ToString());
                // 修改：记录完整异常信息，并重新抛出，而不是静默处理
                System.Diagnostics.Debug.WriteLine("Error in GetFaceAndReShowInfo: " + ex.ToString()); // 保留日志输出
                // 发生异常时，确保返回安全的值
                // facelist = facelist ?? Array.Empty<string>(); // IDE0059 - Redundant, will be removed
                // facelistImg = facelistImg ?? Array.Empty<string>(); // IDE0059 - Redundant, will be removed
                // reShowInfo = reShowInfo ?? ""; // IDE0059 - Redundant, will be removed
                throw; // 重新抛出异常，让调用者知道出错了
            }

            return (facelist, facelistImg, reShowInfo);
        }

        /// <summary>
        /// 用于承载处理后的表情列表、图片和随机回复提示信息。
        /// </summary>
        public readonly struct FaceAndReplyDisplayData
        {
            public string[] FaceList { get; }
            public string[] FaceListImg { get; }
            public string ReShowInfo { get; }

            public FaceAndReplyDisplayData(string[] faceList, string[] faceListImg, string reShowInfo)
            {
                FaceList = faceList ?? Array.Empty<string>();
                FaceListImg = faceListImg ?? Array.Empty<string>();
                ReShowInfo = reShowInfo ?? "";
            }
        }

        /// <summary>
        /// 获取并处理表情列表、图片和随机回复提示信息，包含null检查和异常处理。
        /// </summary>
        /// <param name="classVo">栏目对象</param>
        /// <returns>包含处理后数据的 FaceAndReplyDisplayData 对象。</returns>
        public static FaceAndReplyDisplayData GetProcessedFaceAndReShowInfo(class_Model classVo)
        {
            try
            {
                (string[] faceListResult, string[] faceListImgResult, string reShowInfoResult) = GetFaceAndReShowInfo(classVo);
                return new FaceAndReplyDisplayData(faceListResult, faceListImgResult, reShowInfoResult);
            }
            catch (Exception ex) // CS0168 & IDE0059 - ex is now used for Debug.WriteLine
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetProcessedFaceAndReShowInfo: {ex}");
                // 返回包含默认值的对象，确保调用方安全
                return new FaceAndReplyDisplayData(null, null, null);
            }
        }

        /// <summary>
        /// 格式化带颜色的用户昵称
        /// </summary>
        public static string FormatColoredNickname(long userId, string nickname, List<user_Model> userList, string lang, string ver)
        {
            if (userList == null) return nickname;

            foreach (var item in userList)
            {
                if (item.userid == userId)
                {
                    // 假设 WapTool.GetColorNickName 可直接调用，或可在此处复用其逻辑
                    // 目前直接调用，如不可用需调整
                    // 注意：如果 WapTool 需要实例方法或属性，可能需要调整此辅助方法，或重构 WapTool
                    try
                    {
                        return WapTool.GetColorNickName(item.idname, nickname, lang, ver, item.endTime);
                    }
                    catch (Exception ex)
                    {
                        // 日志：调用 WapTool.GetColorNickName 失败
                        Console.WriteLine("调用 WapTool.GetColorNickName 出错，用户ID: " + userId + ", 错误: " + ex.Message);
                        return nickname; // 回退为普通昵称
                    }
                }
            }
            return nickname; // 未找到用户则返回普通昵称
        }
    }
}