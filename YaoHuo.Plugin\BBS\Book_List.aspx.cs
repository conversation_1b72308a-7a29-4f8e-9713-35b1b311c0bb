﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using YaoHuo.Plugin.Tool;
using System.Linq;

namespace YaoHuo.Plugin.BBS
{
    public class Book_List : BaseBBSListPage
    {
        public string stypename = "";
        public string stypelink = "";
        public string type = "";
        public string key = "";
        public List<wap_bbs_Model> listVoTop = null;
        public string downLink = "";
        public string titlecolor = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            hots = WapTool.GetSiteDefault(siteVo.Version, 41);
            if (!WapTool.IsNumeric(hots))
            {
                hots = "5000";
            }
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            if (classid == "0")
            {
                classVo.introduce = "";
                classVo.sitedowntip = "";
            }
            if (!IsCheckManagerLvl("|00|01|03|04|", "") && "1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 0)))
            {
                ShowTipInfo("此版块已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            titlecolor = WapTool.GetArryString(classVo.smallimg, '|', 42);
            switch (action)
            {
                case "search":
                    ShowSearch();
                    break;

                case "good":
                    ShowGood();
                    break;

                case "class":
                    ShowClass();
                    break;

                case "hot":
                    ShowHot();
                    break;

                case "new":
                    ShowNew();
                    break;

                default:
                    ShowClass();
                    break;
            }
            downLink = WapTool.GetArryString(classVo.smallimg, '|', 19).Trim().Replace("[stype]", stype);
            LoadUserInfo();
        }
        public void ShowClass()
        {
            if (classid == "0")
            {
                ShowTipInfo("无此版块ID", "");
            }
            condition = " userid=" + siteid + " and book_classid=" + classid + " and ischeck=0 ";
            stype = GetRequestValue("stype");
            if (WapTool.IsNumeric(stype))
            {
                condition = condition + " and topic=" + stype;
                stypename = WapTool.GetSmallTypeName(siteid, stype);
                stypelink = "&amp;stype=" + stype;
            }
            try
            {
                if (classVo.ismodel < 1L)
                {
                    pageSize = siteVo.MaxPerPage_Default;
                }
                else
                {
                    pageSize = classVo.ismodel;
                }
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else if ("1".Equals(WapTool.KL_OpenCache))
                {
                    string value = null;
                    if (stype == "")
                    {
                        linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
                        WapTool.DataTempArray.TryGetValue("bbsTotal" + siteid + classid, out value);
                    }
                    if (value == null)
                    {
                        total = wap_bbs_BLL.GetListCount(condition);
                        if (stype == "")
                        {
                            WapTool.DataTempArray.Add("bbsTotal" + siteid + classid, total.ToString());
                        }
                    }
                    else
                    {
                        total = long.Parse(value);
                    }
                }
                else
                {
                    total = wap_bbs_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CheckFunction("bbs", CurrentPage);
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/book_list.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total + stypelink;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
                if (CurrentPage == 1L && stype == "")
                {
                    if ("1".Equals(WapTool.KL_OpenCache))
                    {
                        WapTool.DataBBSArray.TryGetValue("bbsTop" + siteid + classid, out listVoTop);
                        if (listVoTop == null)
                        {
                            listVoTop = wap_bbs_BLL.GetListVoTop("userid=" + siteid + " and book_classid=" + classid + " and ischeck=0 and book_top =1 or (userid=" + siteid + " and ischeck=0 and book_top=2)");
                            try
                            {
                                WapTool.DataBBSArray.Add("bbsTop" + siteid + classid, listVoTop);
                            }
                            catch (Exception)
                            {
                            }
                        }
                    }
                    else
                    {
                        listVoTop = wap_bbs_BLL.GetListVoTop("userid=" + siteid + " and book_classid=" + classid + " and ischeck=0 and book_top =1 or (userid=" + siteid + " and ischeck=0 and book_top=2)");
                    }
                }
                if ("1".Equals(WapTool.KL_OpenCache))
                {
                    if (CurrentPage < 30L && stype == "")
                    {
                        WapTool.DataBBSArray.TryGetValue("bbs" + siteid + classid + CurrentPage, out listVo);
                    }
                    if (listVo == null)
                    {
                        if (stype != "")
                        {
                            int orderType = 0;
                            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 14)))
                            {
                                orderType = 1;
                            }
                            listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, condition, "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,redate,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "id", total, orderType);
                        }
                        else
                        {
                            listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, condition + " and book_top =0 ", "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,redate,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "redate", total, 1);
                        }
                        if (CurrentPage < 30L && stype == "")
                        {
                            try
                            {
                                WapTool.DataBBSArray.Add("bbs" + siteid + classid + CurrentPage, listVo);
                            }
                            catch (Exception)
                            {
                            }
                        }
                    }
                }
                else if (stype != "")
                {
                    int orderType = 0;
                    if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 14)))
                    {
                        orderType = 1;
                    }
                    listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, condition, "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,redate,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "id", total, orderType);
                }
                else
                {
                    var sreWhere = $@"{condition} and book_top = 0";
                    sreWhere += UserBlockingService.GetExcludeUserSql(userid, "book_pub");//排除拉黑的用户
                    listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, sreWhere, "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,redate,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "redate", total, 1);
                }
                sys_ad_show_BLL sys_ad_show_BLL = new sys_ad_show_BLL(a);
                adVo = sys_ad_show_BLL.GetModelBySQL(" and systype='bbs' and siteid=" + siteid);
                VisiteCount("正在浏览:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
        public void ShowNew()
        {
            if (classid == "0")
            {
                // 全站新帖
                condition = " ischeck=0 and userid=" + siteid;

                string excludedClassIDsConfig = PubConstant.GetAppString("KL_BBS_ExcludeClassIDs");
                if (!string.IsNullOrEmpty(excludedClassIDsConfig))
                {
                    string[] idsToExclude = excludedClassIDsConfig.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    if (idsToExclude.Length > 0)
                    {
                        // 确保每个ID都是数字，防止SQL注入。如果您的栏目ID保证是纯数字，可以直接拼接。
                        // 为安全起见，最好进行验证或参数化（如果框架支持对IN子句参数化）。
                        // 这里我们假设ID都是数字，并进行简单拼接。
                        condition += " and book_classid NOT IN (" + string.Join(",", idsToExclude.Where(id => long.TryParse(id.Trim(), out _)).Select(id => id.Trim())) + ")";
                    }
                }

                condition += UserBlockingService.GetExcludeUserSql(userid, "book_pub");//排除拉黑的用户
                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "所有最新帖子";
                classVo.siteimg = "NetImages/no.gif";
                classVo.introduce = "";
            }
            else
            {
                classVo.classname += "・最新帖子";
                condition = $" ischeck=0 and userid={siteid} and book_classid in (select classid from [class] where childid={classid} union select '{classid}') ";
            }
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap_bbs_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = int.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/book_list.aspx?action=new&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
                listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, condition, "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "id", total, 1);
                sys_ad_show_BLL sys_ad_show_BLL = new sys_ad_show_BLL(a);
                adVo = sys_ad_show_BLL.GetModelBySQL(" and systype='bbs' and siteid=" + siteid);
                VisiteCount("正在浏览最新:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
        public void ShowHot()
        {
            Response.Redirect(http_start + "bbs/book_list_hot.aspx?siteid=" + siteid + "&classid=" + classid + "&type=" + GetRequestValue("type") + "&days=" + GetRequestValue("days"));
        }

        public void ShowSearch()
        {
            Response.Redirect(http_start + "bbs/book_list_search.aspx?action=search&siteid=" + siteid + "&classid=" + classid + "&type=" + GetRequestValue("type") + "&key=" + HttpUtility.UrlEncode(GetRequestValue("key")));
        }

        public void ShowGood()
        {
            if (classid == "0")
            {
                condition = " ischeck=0 and book_good=1 and userid=" + siteid;
                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "所有精华帖子";
                classVo.siteimg = "NetImages/no.gif";
                classVo.introduce = "";
            }
            else
            {
                classVo.classname += "・精华帖子";
                condition = "  ischeck=0 and book_good=1 and userid=" + siteid + " and   book_classid in (select classid from [class] where childid=" + classid + " union select '" + classid + "') ";
            }
            try
            {
                if (classVo.ismodel < 1L)
                {
                    pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                }
                else
                {
                    pageSize = Convert.ToInt32(classVo.ismodel);
                }
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap_bbs_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = int.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/book_list.aspx?action=good&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
                listVo = wap_bbs_BLL.GetListVo(pageSize, CurrentPage, condition, "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,isdown,hangbiaoshi,freeMoney,book_img,MarkSixBetID,MarkSixWin", "id", total, 1);
                sys_ad_show_BLL sys_ad_show_BLL = new sys_ad_show_BLL(a);
                adVo = sys_ad_show_BLL.GetModelBySQL(" and systype='bbs' and siteid=" + siteid);
                VisiteCount("正在浏览精华:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        private new void LoadUserInfo()
        {
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 33);
            if (!(siteDefault != "1") || (listVo == null && listVoTop == null))
            {
                return;
            }

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("siteid=" + siteid + " and userid in(");

            if (listVo != null)
            {
                foreach (var item in listVo.Where(x => !string.IsNullOrEmpty(x.book_pub)))
                {
                    stringBuilder.Append(item.book_pub + ",");
                }
            }

            if (listVoTop != null)
            {
                foreach (var item in listVoTop.Where(x => !string.IsNullOrEmpty(x.book_pub)))
                {
                    stringBuilder.Append(item.book_pub + ",");
                }
            }

            stringBuilder.Append("0)");
            userListVo_IDName = MainBll.GetUserListVo(stringBuilder.ToString());
        }
    }
}