# FriendQueryService 好友查询服务

## 概述

`FriendQueryService` 是一个专为 YaoHuo.Plugin 项目设计的安全、高效的好友/黑名单查询服务。它使用参数化查询防止 SQL 注入，支持智能搜索功能。

## 主要特性

### 1. 安全的参数化查询
- 所有 SQL 查询都使用 `SqlParameter` 参数化
- 完全防止 SQL 注入攻击
- 输入验证和类型安全

### 2. 智能搜索功能
- **纯数字输入**：按用户ID精确搜索
- **文字输入**：按昵称模糊搜索
- 自动判断搜索类型，无需手动指定

### 3. 完整的好友类型支持
- 好友列表 (friendtype = 0)
- 黑名单 (friendtype = 1)  
- 追求列表 (friendtype = 2)
- 追求我的人 (friendtype = 4)
- 推荐好友 (friendtype = 5)

### 4. 高效分页支持
- SQL Server 2012+ 的 `OFFSET...FETCH` 分页
- 准确的总数统计
- 灵活的页面大小配置

## 使用方法

### 基本查询

```csharp
// 创建服务实例
var service = new FriendQueryService(connectionString);

// 查询好友列表（第1页，每页10条）
var result = service.SearchFriends("1000", "123456", "0", null, 10, 1);

// 检查结果
if (!result.HasError)
{
    Console.WriteLine($"总数: {result.Total}");
    Console.WriteLine($"当前页: {result.CurrentPage}/{result.TotalPages}");
    
    foreach (var friend in result.Friends)
    {
        Console.WriteLine($"好友: {friend.friendnickname} (ID: {friend.frienduserid})");
    }
}
```

### 智能搜索

```csharp
// 按用户ID搜索（输入纯数字）
var result1 = service.SearchFriends("1000", "123456", "0", "789012", 10, 1);

// 按昵称搜索（输入文字）
var result2 = service.SearchFriends("1000", "123456", "0", "张三", 10, 1);

// 按昵称模糊搜索（支持部分匹配）
var result3 = service.SearchFriends("1000", "123456", "0", "管理", 10, 1);
```

### 查询"追求我的人"

```csharp
// 特殊的查询方法，查询追求当前用户的人
var result = service.SearchWhoLovesMe("1000", "123456", null, 10, 1);
```

### 工具方法

```csharp
// 验证好友关系是否存在
bool exists = service.FriendExists("1000", "123456", "789012", "0");

// 获取好友数量
int friendCount = service.GetFriendCount("1000", "123456", "0");
int blacklistCount = service.GetFriendCount("1000", "123456", "1");
```

## 搜索逻辑说明

### 搜索类型判断

| 输入示例 | 判断结果 | 搜索方式 |
|---------|---------|---------|
| `123456` | 纯数字 | 同时按用户ID和昵称搜索 |
| `张三` | 包含文字 | 按昵称模糊搜索 |
| `admin` | 包含文字 | 按昵称模糊搜索 |
| `0` | 纯数字 | 同时按用户ID和昵称搜索 |
| 空字符串 | 无搜索条件 | 返回所有数据 |

### 数据库查询示例

```sql
-- 按用户ID和昵称同时搜索（纯数字输入）
SELECT * FROM wap_friends 
WHERE siteid = @siteid AND userid = @userid AND friendtype = @friendtype 
AND (frienduserid = @searchUserId OR friendnickname LIKE @searchNickname)

-- 按昵称搜索（非纯数字输入）
SELECT * FROM wap_friends 
WHERE siteid = @siteid AND userid = @userid AND friendtype = @friendtype 
AND friendnickname LIKE @searchNickname
```

## 在 FriendList 页面中的应用

新的查询服务已经集成到 `FriendList.aspx.cs` 的 `LoadDataForNewUI()` 方法中：

```csharp
private void LoadDataForNewUI()
{
    // 智能搜索逻辑已内嵌
    // 支持按ID或昵称搜索
    // 使用参数化查询确保安全
}
```

### 前端搜索提示

前端输入框现在显示：**"输入用户ID或昵称搜索"**

搜索逻辑自动优化：
- 输入纯数字时，同时搜索用户ID和昵称，确保不遗漏昵称为数字的用户
- 输入非纯数字时，仅按昵称模糊搜索

## 性能优化

### 建议的数据库索引

```sql
-- 基础查询索引
CREATE INDEX IX_wap_friends_user_type ON wap_friends(siteid, userid, friendtype);

-- 好友ID搜索索引  
CREATE INDEX IX_wap_friends_frienduser ON wap_friends(siteid, userid, friendtype, frienduserid);

-- 昵称搜索索引
CREATE INDEX IX_wap_friends_nickname ON wap_friends(siteid, userid, friendtype, friendnickname);

-- 追求我的人查询索引
CREATE INDEX IX_wap_friends_loveme ON wap_friends(siteid, frienduserid, friendtype);
```

### 性能监控

使用 `FriendQueryServiceTest.PerformanceTest()` 方法可以测试查询性能：

```csharp
FriendQueryServiceTest.PerformanceTest(connectionString);
```

## 安全考虑

1. **SQL注入防护**：所有查询使用参数化
2. **输入验证**：严格验证所有输入参数
3. **权限控制**：仅查询当前用户的数据
4. **错误处理**：安全的异常处理，不泄露敏感信息

## 未来扩展

### 黑名单搜索
目前黑名单页面不显示搜索框（按用户需求），但后端已支持搜索功能。如需启用：

```csharp
// 在页面模型中设置
model.EnableBlacklistSearch = true;
```

### 高级搜索
可扩展支持：
- 时间范围搜索
- 组合条件搜索
- 排序选项
- 批量操作

## 测试

运行测试以验证功能：

```csharp
// 功能测试
FriendQueryServiceTest.TestSmartSearch(connectionString);

// 搜索逻辑演示
FriendQueryServiceTest.DemonstrateSearchLogic();

// 性能测试
FriendQueryServiceTest.PerformanceTest(connectionString);
```

## 注意事项

1. 需要 SQL Server 2012 或更高版本（支持 OFFSET...FETCH）
2. 确保数据库连接字符串正确配置
3. 建议在生产环境中添加适当的索引
4. 定期监控查询性能，根据需要调整分页大小 