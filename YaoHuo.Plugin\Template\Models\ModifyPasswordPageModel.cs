namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// ModifyPassword 页面数据模型
    /// </summary>
    public class ModifyPasswordPageModel
    {
        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; } = "修改密码";

        /// <summary>
        /// 表单数据
        /// </summary>
        public ModifyPasswordFormModel FormData { get; set; } = new ModifyPasswordFormModel();

        /// <summary>
        /// 消息状态
        /// </summary>
        public YaoHuo.Plugin.Template.Models.MessageModel Message { get; set; } = new YaoHuo.Plugin.Template.Models.MessageModel();

        /// <summary>
        /// 站点信息
        /// </summary>
        public YaoHuo.Plugin.Template.Models.SiteInfoModel SiteInfo { get; set; } = new YaoHuo.Plugin.Template.Models.SiteInfoModel();
    }

    /// <summary>
    /// 表单数据模型
    /// </summary>
    public class ModifyPasswordFormModel
    {
        /// <summary>
        /// 表单操作URL
        /// </summary>
        public string ActionUrl { get; set; }

        /// <summary>
        /// 隐藏字段
        /// </summary>
        public YaoHuo.Plugin.Template.Models.HiddenFieldsModel HiddenFields { get; set; } = new YaoHuo.Plugin.Template.Models.HiddenFieldsModel();
    }
}