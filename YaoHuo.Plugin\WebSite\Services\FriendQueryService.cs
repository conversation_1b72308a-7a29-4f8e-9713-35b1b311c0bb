using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WebSite.Services
{
    /// <summary>
    /// 好友查询服务类 - 使用参数化查询，支持智能搜索
    /// </summary>
    public class FriendQueryService
    {
        private readonly string _connectionString;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public FriendQueryService(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 智能搜索好友/黑名单 - 纯数字搜索ID，其他字符搜索昵称
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">当前用户ID</param>
        /// <param name="friendtype">好友类型（0=好友，1=黑名单，2=追求等）</param>
        /// <param name="searchKey">搜索关键字</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="currentPage">当前页码</param>
        /// <returns>查询结果</returns>
        public FriendQueryResult SearchFriends(
            string siteid, 
            string userid, 
            string friendtype, 
            string searchKey = null, 
            int pageSize = 10, 
            int currentPage = 1)
        {
            var result = new FriendQueryResult
            {
                Friends = new List<wap_friends_Model>(),
                Total = 0,
                CurrentPage = currentPage,
                PageSize = pageSize
            };

            try
            {
                // 输入验证
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || !WapTool.IsNumeric(friendtype))
                {
                    System.Diagnostics.Debug.WriteLine("FriendQueryService: 参数验证失败");
                    return result;
                }

                // 构建基础查询条件
                var whereClause = "siteid = @siteid AND userid = @userid AND friendtype = @friendtype";
                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
                    new SqlParameter("@friendtype", SqlDbType.Int) { Value = int.Parse(friendtype) }
                };

                // 处理搜索条件
                if (!string.IsNullOrWhiteSpace(searchKey))
                {
                    searchKey = searchKey.Trim();
                    
                    // 判断是否为纯数字
                    if (WapTool.IsNumeric(searchKey))
                    {
                        // 纯数字时同时搜索用户ID和昵称
                        whereClause += " AND (frienduserid = @searchUserId OR friendnickname LIKE @searchNickname)";
                        parameters.Add(new SqlParameter("@searchUserId", SqlDbType.BigInt) { Value = long.Parse(searchKey) });
                        parameters.Add(new SqlParameter("@searchNickname", SqlDbType.NVarChar, 50) { Value = $"%{searchKey}%" });
                        System.Diagnostics.Debug.WriteLine($"FriendQueryService: 按用户ID和昵称同时搜索 - {searchKey}");
                    }
                    else
                    {
                        // 非纯数字时只按昵称模糊搜索
                        whereClause += " AND friendnickname LIKE @searchNickname";
                        parameters.Add(new SqlParameter("@searchNickname", SqlDbType.NVarChar, 50) { Value = $"%{searchKey}%" });
                        System.Diagnostics.Debug.WriteLine($"FriendQueryService: 按昵称搜索 - {searchKey}");
                    }
                }

                // 查询总数
                string countSql = $"SELECT COUNT(id) FROM wap_friends WHERE {whereClause}";
                var countResult = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, countSql, parameters.ToArray());
                result.Total = countResult != null ? Convert.ToInt32(countResult) : 0;

                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 查询总数 - {result.Total}");

                // 如果没有数据，直接返回
                if (result.Total == 0)
                {
                    return result;
                }

                // 计算分页
                var totalPages = (int)Math.Ceiling((double)result.Total / pageSize);
                currentPage = Math.Max(1, Math.Min(currentPage, totalPages));
                result.CurrentPage = currentPage;
                result.TotalPages = totalPages;

                var offset = (currentPage - 1) * pageSize;

                // 查询数据
                string dataSql = $@"
                    SELECT id, siteid, userid, frienduserid, friendusername, friendnickname, friendtype, addtime
                    FROM wap_friends 
                    WHERE {whereClause}
                    ORDER BY id DESC
                    OFFSET @offset ROWS 
                    FETCH NEXT @pageSize ROWS ONLY";

                var dataParameters = new List<SqlParameter>(parameters)
                {
                    new SqlParameter("@offset", SqlDbType.Int) { Value = offset },
                    new SqlParameter("@pageSize", SqlDbType.Int) { Value = pageSize }
                };

                var dataTable = DbHelperSQL.ExecuteDataset(_connectionString, CommandType.Text, dataSql, dataParameters.ToArray()).Tables[0];
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var friend = new wap_friends_Model
                    {
                        id = Convert.ToInt64(row["id"]),
                        siteid = Convert.ToInt64(row["siteid"]),
                        userid = Convert.ToInt64(row["userid"]),
                        frienduserid = Convert.ToInt64(row["frienduserid"]),
                        friendusername = row["friendusername"]?.ToString() ?? "",
                        friendnickname = row["friendnickname"]?.ToString() ?? "",
                        friendtype = Convert.ToInt32(row["friendtype"]),
                        addtime = Convert.ToDateTime(row["addtime"])
                    };
                    result.Friends.Add(friend);
                }

                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 查询完成 - 当前页:{currentPage}, 数据量:{result.Friends.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 查询异常 - {ex.Message}");
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 查询"追求我的人"列表（friendtype=4的特殊情况）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">当前用户ID（作为被追求的人）</param>
        /// <param name="searchKey">搜索关键字</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="currentPage">当前页码</param>
        /// <returns>查询结果</returns>
        public FriendQueryResult SearchWhoLovesMe(
            string siteid, 
            string userid, 
            string searchKey = null, 
            int pageSize = 10, 
            int currentPage = 1)
        {
            var result = new FriendQueryResult
            {
                Friends = new List<wap_friends_Model>(),
                Total = 0,
                CurrentPage = currentPage,
                PageSize = pageSize
            };

            try
            {
                // 输入验证
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid))
                {
                    System.Diagnostics.Debug.WriteLine("FriendQueryService: 参数验证失败（追求我的人）");
                    return result;
                }

                // 构建基础查询条件（注意：这里是 frienduserid = userid，表示别人追求当前用户）
                var whereClause = "siteid = @siteid AND frienduserid = @frienduserid AND friendtype = 2";
                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@frienduserid", SqlDbType.BigInt) { Value = long.Parse(userid) }
                };

                // 处理搜索条件
                if (!string.IsNullOrWhiteSpace(searchKey))
                {
                    searchKey = searchKey.Trim();
                    
                    // 判断是否为纯数字
                    if (WapTool.IsNumeric(searchKey))
                    {
                        // 纯数字时同时搜索追求者ID和通过用户表查询昵称
                        whereClause += " AND (userid = @searchUserId OR EXISTS (SELECT 1 FROM [user] u WHERE u.userid = wap_friends.userid AND u.nickname LIKE @searchNickname))";
                        parameters.Add(new SqlParameter("@searchUserId", SqlDbType.BigInt) { Value = long.Parse(searchKey) });
                        parameters.Add(new SqlParameter("@searchNickname", SqlDbType.NVarChar, 50) { Value = $"%{searchKey}%" });
                        System.Diagnostics.Debug.WriteLine($"FriendQueryService: 按追求者ID和昵称同时搜索 - {searchKey}");
                    }
                    else
                    {
                        // 非纯数字时只按昵称搜索
                        whereClause += " AND EXISTS (SELECT 1 FROM [user] u WHERE u.userid = wap_friends.userid AND u.nickname LIKE @searchNickname)";
                        parameters.Add(new SqlParameter("@searchNickname", SqlDbType.NVarChar, 50) { Value = $"%{searchKey}%" });
                        System.Diagnostics.Debug.WriteLine($"FriendQueryService: 按追求者昵称搜索 - {searchKey}");
                    }
                }

                // 查询总数
                string countSql = $"SELECT COUNT(id) FROM wap_friends WHERE {whereClause}";
                var countResult = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, countSql, parameters.ToArray());
                result.Total = countResult != null ? Convert.ToInt32(countResult) : 0;

                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 追求我的人总数 - {result.Total}");

                // 如果没有数据，直接返回
                if (result.Total == 0)
                {
                    return result;
                }

                // 计算分页
                var totalPages = (int)Math.Ceiling((double)result.Total / pageSize);
                currentPage = Math.Max(1, Math.Min(currentPage, totalPages));
                result.CurrentPage = currentPage;
                result.TotalPages = totalPages;

                var offset = (currentPage - 1) * pageSize;

                // 查询数据
                string dataSql = $@"
                    SELECT id, siteid, userid, frienduserid, friendusername, friendnickname, friendtype, addtime
                    FROM wap_friends 
                    WHERE {whereClause}
                    ORDER BY id DESC
                    OFFSET @offset ROWS 
                    FETCH NEXT @pageSize ROWS ONLY";

                var dataParameters = new List<SqlParameter>(parameters)
                {
                    new SqlParameter("@offset", SqlDbType.Int) { Value = offset },
                    new SqlParameter("@pageSize", SqlDbType.Int) { Value = pageSize }
                };

                var dataTable = DbHelperSQL.ExecuteDataset(_connectionString, CommandType.Text, dataSql, dataParameters.ToArray()).Tables[0];
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var friend = new wap_friends_Model
                    {
                        id = Convert.ToInt64(row["id"]),
                        siteid = Convert.ToInt64(row["siteid"]),
                        userid = Convert.ToInt64(row["userid"]),
                        frienduserid = Convert.ToInt64(row["frienduserid"]),
                        friendusername = row["friendusername"]?.ToString() ?? "",
                        friendnickname = row["friendnickname"]?.ToString() ?? "",
                        friendtype = Convert.ToInt32(row["friendtype"]),
                        addtime = Convert.ToDateTime(row["addtime"])
                    };
                    result.Friends.Add(friend);
                }

                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 追求我的人查询完成 - 当前页:{currentPage}, 数据量:{result.Friends.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 追求我的人查询异常 - {ex.Message}");
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 验证好友是否存在
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="frienduserid">好友用户ID</param>
        /// <param name="friendtype">好友类型</param>
        /// <returns>是否存在</returns>
        public bool FriendExists(string siteid, string userid, string frienduserid, string friendtype)
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || 
                    !WapTool.IsNumeric(frienduserid) || !WapTool.IsNumeric(friendtype))
                {
                    return false;
                }

                string sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @siteid AND userid = @userid AND frienduserid = @frienduserid AND friendtype = @friendtype";
                
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                    new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
                    new SqlParameter("@frienduserid", SqlDbType.BigInt) { Value = long.Parse(frienduserid) },
                    new SqlParameter("@friendtype", SqlDbType.Int) { Value = int.Parse(friendtype) }
                };

                var result = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, sql, parameters);
                return result != null && Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 验证好友存在异常 - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取好友数量
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="friendtype">好友类型</param>
        /// <returns>好友数量</returns>
        public int GetFriendCount(string siteid, string userid, string friendtype)
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || !WapTool.IsNumeric(friendtype))
                {
                    return 0;
                }

                string sql;
                SqlParameter[] parameters;

                if (friendtype == "4") // 追求我的人
                {
                    sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @siteid AND frienduserid = @frienduserid AND friendtype = 2";
                    parameters = new SqlParameter[]
                    {
                        new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                        new SqlParameter("@frienduserid", SqlDbType.BigInt) { Value = long.Parse(userid) }
                    };
                }
                else
                {
                    sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @siteid AND userid = @userid AND friendtype = @friendtype";
                    parameters = new SqlParameter[]
                    {
                        new SqlParameter("@siteid", SqlDbType.BigInt) { Value = long.Parse(siteid) },
                        new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) },
                        new SqlParameter("@friendtype", SqlDbType.Int) { Value = int.Parse(friendtype) }
                    };
                }

                var result = DbHelperSQL.ExecuteScalar(_connectionString, CommandType.Text, sql, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FriendQueryService: 获取好友数量异常 - {ex.Message}");
                return 0;
            }
        }
    }

    /// <summary>
    /// 好友查询结果
    /// </summary>
    public class FriendQueryResult
    {
        /// <summary>
        /// 好友列表
        /// </summary>
        public List<wap_friends_Model> Friends { get; set; } = new List<wap_friends_Model>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 是否为第一页
        /// </summary>
        public bool IsFirstPage => CurrentPage <= 1;

        /// <summary>
        /// 是否为最后一页
        /// </summary>
        public bool IsLastPage => CurrentPage >= TotalPages;
    }
} 