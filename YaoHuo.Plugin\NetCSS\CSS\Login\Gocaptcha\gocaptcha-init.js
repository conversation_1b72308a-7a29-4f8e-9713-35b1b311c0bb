﻿document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.forms['login'];
    const gocaptchaTokenInput = document.getElementById('gocaptcha-token');
    const originalWrap = document.getElementById('gocaptcha-wrap');

    if (!loginForm || !gocaptchaTokenInput || !originalWrap) {
        return;
    }

    let captchaInstance = null;
    let currentCaptchaKey = null;
    let isSubmittingForm = false;
    let modalOverlay = null;
    let modalWrapper = null;
    let turnstileLoaded = false;

    // 创建弹窗HTML结构
    function createModal() {
        if (modalOverlay) return;

        modalOverlay = document.createElement('div');
        modalOverlay.id = 'gocaptcha-modal-overlay';
        modalOverlay.className = 'gocaptcha-modal-overlay';

        modalWrapper = document.createElement('div');
        modalWrapper.className = 'gocaptcha-modal-wrapper';

        modalOverlay.appendChild(modalWrapper);
        document.body.appendChild(modalOverlay);

        // 点击遮罩层关闭弹窗
        modalOverlay.addEventListener('click', function(event) {
            if (event.target === modalOverlay) {
                hideModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && modalOverlay.classList.contains('show')) {
                hideModal();
            }
        });
    }

    // 异步加载 Cloudflare Turnstile 脚本
    function loadTurnstileScript() {
        return new Promise((resolve, reject) => {
            if (turnstileLoaded || window.turnstile) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
            script.async = true;
            script.defer = true;
            script.onload = function() {
                turnstileLoaded = true;
                resolve();
            };
            script.onerror = function() {
                reject(new Error('Cloudflare Turnstile 脚本加载失败'));
            };
            document.head.appendChild(script);
        });
    }

    // 切换到 Cloudflare Turnstile
    function switchToTurnstile() {
        // 隐藏弹窗，直接在原位置显示 Turnstile
        hideModal(false);

        loadTurnstileScript()
            .then(() => {
                // 添加验证码类型标识
                let captchaTypeInput = document.getElementById('captcha-type');
                if (!captchaTypeInput) {
                    captchaTypeInput = document.createElement('input');
                    captchaTypeInput.type = 'hidden';
                    captchaTypeInput.id = 'captcha-type';
                    captchaTypeInput.name = 'captchaType';
                    loginForm.appendChild(captchaTypeInput);
                }
                captchaTypeInput.value = 'cloudflare';

                // 清理 GoCaptcha 实例
                if (captchaInstance) {
                    try {
                        captchaInstance.destroy();
                    } catch (e) {
                        // 忽略销毁错误
                    }
                    captchaInstance = null;
                }

                // 清空原始容器并显示它
                originalWrap.innerHTML = '';
                originalWrap.style.display = 'block';
                originalWrap.classList.add('show-turnstile');

                // 创建 Turnstile 容器，使用和原生一致的结构
                const turnstileDiv = document.createElement('div');
                turnstileDiv.className = 'cf-turnstile';
                turnstileDiv.setAttribute('data-sitekey', getTurnstileSiteKey());
                turnstileDiv.setAttribute('data-theme', 'light');
                turnstileDiv.setAttribute('data-language', 'zh-cn');

                originalWrap.appendChild(turnstileDiv);

                // 渲染 Turnstile 组件
                if (window.turnstile) {
                    try {
                        window.turnstile.render(turnstileDiv, {
                            sitekey: getTurnstileSiteKey(),
                            theme: 'light',
                            language: 'zh-cn',
                            callback: function(token) {
                                // 验证成功
                                let cfTokenInput = document.getElementById('cf-turnstile-response');
                                if (!cfTokenInput) {
                                    cfTokenInput = document.createElement('input');
                                    cfTokenInput.type = 'hidden';
                                    cfTokenInput.id = 'cf-turnstile-response';
                                    cfTokenInput.name = 'cf-turnstile-response';
                                    loginForm.appendChild(cfTokenInput);
                                }
                                cfTokenInput.value = token;

                                // 直接提交表单，不需要弹窗提示
                                isSubmittingForm = true;
                                loginForm.submit();
                            },
                            'error-callback': function() {
                                // 可以在这里添加错误提示，但不使用弹窗
                                console.error('Turnstile 验证失败');
                            }
                        });
                    } catch (error) {
                        console.error('Turnstile 渲染失败:', error);
                        // 可以在原位置显示错误信息
                        originalWrap.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">验证码加载失败，请刷新页面重试</div>';
                    }
                } else {
                    console.error('Turnstile 脚本未加载');
                    originalWrap.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">验证码服务不可用，请刷新页面重试</div>';
                }
            })
            .catch(error => {
                console.error('Turnstile 脚本加载失败:', error);
                // 隐藏弹窗并在原位置显示错误
                hideModal(false);
                originalWrap.innerHTML = '<div style="color: red; text-align: center; padding: 10px;">验证码加载失败，请检查网络连接</div>';
                originalWrap.style.display = 'block';
                originalWrap.classList.add('show-turnstile');
            });
    }

    // 获取 Turnstile Site Key（从页面中获取或使用默认值）
    function getTurnstileSiteKey() {
        // 这里可以从页面的某个元素或全局变量中获取，暂时使用一个默认值
        return window.TURNSTILE_SITE_KEY || '0x4AAAAAAA2TqWOTWYygNo4a';
    }

    // 显示验证失败错误提示
    function showCaptchaError() {
        const gcBody = modalWrapper.querySelector('.gc-body');
        const goCaptcha = modalWrapper.querySelector('.go-captcha');
        
        if (!gcBody || !goCaptcha) return;

        // 创建错误提示覆盖层
        let errorOverlay = gcBody.querySelector('.gocaptcha-error-overlay');
        if (!errorOverlay) {
            errorOverlay = document.createElement('div');
            errorOverlay.className = 'gocaptcha-error-overlay';
            errorOverlay.textContent = '验证错误，请重试';
            gcBody.appendChild(errorOverlay);
        }

        // 显示错误提示
        errorOverlay.classList.add('show');

        // 添加抖动动画
        goCaptcha.classList.add('gocaptcha-shake-animation');

        // 1.5秒后隐藏提示并移除动画，然后重新加载验证码
        setTimeout(() => {
            errorOverlay.classList.remove('show');
            goCaptcha.classList.remove('gocaptcha-shake-animation');
            
            // 再延迟一点时间让动画完成，然后重新加载
            setTimeout(() => {
                initGoCaptchaInModal();
            }, 300);
        }, 1500);
    }

    // 显示弹窗
    function showModal() {
        createModal();
        document.body.classList.add('gocaptcha-modal-open');
        modalOverlay.classList.add('show');
    }

    // 隐藏弹窗
    function hideModal(clearToken = true) {
        if (modalOverlay) {
            document.body.classList.remove('gocaptcha-modal-open');
            modalOverlay.classList.remove('show');
            
            // 清理验证码实例
            if (captchaInstance) {
                try {
                    captchaInstance.destroy();
                } catch (e) {
                    // 忽略销毁错误
                }
                captchaInstance = null;
            }

            // 移除 Turnstile 弹窗特殊类
            modalOverlay.classList.remove('turnstile-modal');


            // 清空弹窗内容
            modalWrapper.innerHTML = '';

            currentCaptchaKey = null;

            // 清理原始容器的 Turnstile 显示状态
            if (originalWrap) {
                originalWrap.classList.remove('show-turnstile');
                // 如果容器中有 Turnstile 验证码，清空它并隐藏容器
                if (originalWrap.querySelector('.cf-turnstile')) {
                    originalWrap.innerHTML = '';
                    originalWrap.style.display = 'none';
                }
            }

            // 只有在明确要求清空token时才清空（比如用户主动关闭弹窗）
            if (clearToken) {
                gocaptchaTokenInput.value = '';
                // 清理验证码类型
                const captchaTypeInput = document.getElementById('captcha-type');
                if (captchaTypeInput) {
                    captchaTypeInput.value = '';
                }
                const cfTokenInput = document.getElementById('cf-turnstile-response');
                if (cfTokenInput) {
                    cfTokenInput.value = '';
                }
            }
        }
    }

    // 显示加载状态
    function showLoading(message = '正在加载验证码...') {
        createModal();
        modalWrapper.innerHTML = `
            <div class="gocaptcha-modal-loading">
                <div class="gocaptcha-modal-loading-spinner"></div>
                <div>${message}</div>
            </div>
        `;
    }

    // 显示错误状态
    function showError(message, showTurnstileOption = true) {
        createModal();
        
        // 根据错误类型决定按钮文本和行为
        let isInitializationError = message.includes('初始化失败') || message.includes('挂载失败');
        let reloadButtonText = isInitializationError ? '刷新页面' : '重新加载';
        let reloadButtonAction = isInitializationError ? 'window.location.reload()' : 'window.initGoCaptchaInModal()';
        
        let buttons = `<button type="button" onclick="${reloadButtonAction}">${reloadButtonText}</button>`;
        
        if (showTurnstileOption) {
            buttons += `<button type="button" onclick="window.switchToTurnstile()">使用备用验证码</button>`;
        }
        
        modalWrapper.innerHTML = `
            <div class="gocaptcha-modal-error">
                <div>${message}</div>
                <div class="gocaptcha-modal-error-buttons">
                    ${buttons}
                </div>
            </div>
        `;
    }

    // 显示成功状态
    function showSuccess() {
        if (modalWrapper) {
            modalWrapper.innerHTML = `
                <div class="gocaptcha-modal-success">
                    <div>✓ 验证成功</div>
                    <div>正在登录...</div>
                </div>
            `;
        }
    }

    // 在弹窗中初始化验证码
    function initGoCaptchaInModal() {
        // 移除 Turnstile 弹窗特殊类，确保 GoCaptcha 弹窗样式正确
        if (modalOverlay) {
            modalOverlay.classList.remove('turnstile-modal');
        }

        // 重置验证码类型为 GoCaptcha
        let captchaTypeInput = document.getElementById('captcha-type');
        if (!captchaTypeInput) {
            captchaTypeInput = document.createElement('input');
            captchaTypeInput.type = 'hidden';
            captchaTypeInput.id = 'captcha-type';
            captchaTypeInput.name = 'captchaType';
            loginForm.appendChild(captchaTypeInput);
        }
        captchaTypeInput.value = 'gocaptcha';
        
        // 显示加载状态
        showLoading();
        
        // 清理之前的实例
        if (captchaInstance) {
            try {
                captchaInstance.destroy();
            } catch (e) {
                // 忽略销毁错误
            }
            captchaInstance = null;
        }
        
        // 重置状态
        currentCaptchaKey = null;
        gocaptchaTokenInput.value = '';

        // 创建新的验证码实例
        try {
            captchaInstance = new GoCaptcha.Slide({
                width: 300,
                height: 220,
            });
        } catch (error) {
            showError('验证码组件初始化失败，请刷新页面重试。');
            return;
        }

        captchaInstance.setEvents({
            confirm: function(point, reset) {
                const pointValue = `${point.x},${point.y}`;

                if (!currentCaptchaKey) {
                    showCaptchaError();
                    return false;
                }

                const verifyBody = {
                    id: 'slide-default',
                    captchaKey: currentCaptchaKey,
                    value: pointValue
                };

                fetch('/GoCaptchaProxy.ashx?path=check-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(verifyBody)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(verificationResult => {
                    if (verificationResult && verificationResult.code === 200 && verificationResult.data === 'ok') {
                        gocaptchaTokenInput.value = 'verified';
                        
                        showSuccess();
                        
                        setTimeout(() => {
                            hideModal(false);
                            isSubmittingForm = true;
                            loginForm.submit();
                        }, 1500);
                        
                    } else {
                        // 显示错误提示和抖动动画
                        showCaptchaError();
                    }
                })
                .catch(error => {
                    // 网络错误时也显示错误提示
                    showCaptchaError();
                });

                return false;
            },
            refresh: function() {
                initGoCaptchaInModal();
            },
            close: function() {
                hideModal();
            }
        });

        // 克隆原始容器到弹窗中
        const clonedWrap = originalWrap.cloneNode(false);
        clonedWrap.innerHTML = '';
        
        modalWrapper.innerHTML = '';
        modalWrapper.appendChild(clonedWrap);
        
        // 挂载验证码框架到克隆的容器
        try {
            captchaInstance.mount(clonedWrap);
        } catch (error) {
            showError('验证码挂载失败，请刷新页面重试。');
            return;
        }

        // 获取验证码数据
        fetch('/GoCaptchaProxy.ashx?path=get-data&id=slide-default')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (captchaInstance && data && data.code === 200 && data.data && data.data.captcha_key) {
                    currentCaptchaKey = data.data.captcha_key;

                    const frontendData = {
                        image: data.data.master_image_base64,
                        thumb: data.data.thumb_image_base64,
                        thumbWidth: data.data.thumb_width || 60,
                        thumbHeight: data.data.thumb_height || 60,
                        thumbX: data.data.display_x || 0,
                        thumbY: data.data.display_y || 0,
                    };

                    try {
                        captchaInstance.setData(frontendData);
                    } catch (error) {
                        showError('验证码数据设置失败，请重新加载。');
                    }
                    
                } else {
                    showError('验证码加载失败，请点击重新加载。');
                }
            })
            .catch(error => {
                showError('验证码服务不可用，请稍后重试。');
            });
    }

    // 表单提交拦截
    function handleFormSubmit(event) {
        if (isSubmittingForm) {
            isSubmittingForm = false;
            return;
        }

        const captchaTypeInput = document.getElementById('captcha-type');
        const captchaType = captchaTypeInput ? captchaTypeInput.value : '';

        // 检查是否已经通过任一验证码验证
        const hasGoCaptchaToken = gocaptchaTokenInput.value;
        const cfTokenInput = document.getElementById('cf-turnstile-response');
        const hasTurnstileToken = cfTokenInput && cfTokenInput.value;

        // 检查是否已经显示了 Turnstile 验证码（在原位置）
        const hasTurnstileInPlace = originalWrap && originalWrap.querySelector('.cf-turnstile');

        if (!hasGoCaptchaToken && !hasTurnstileToken && !hasTurnstileInPlace) {
            event.preventDefault();
            showModal();
            initGoCaptchaInModal();
        } else if (hasTurnstileInPlace && !hasTurnstileToken) {
            // 如果已经显示了 Turnstile 但还没有验证通过，阻止提交
            event.preventDefault();
        }
    }

    // 绑定表单提交事件
    loginForm.addEventListener('submit', handleFormSubmit);
    
    // 暴露函数到全局
    window.initGoCaptchaInModal = initGoCaptchaInModal;
    window.switchToTurnstile = switchToTurnstile;
    window.hideGoCaptchaModal = hideModal;
});