---
description: 
globs: *.css,*.html,*.js,*.aspx,*.ascx
alwaysApply: false
---
# 前端UI原则与CSS最佳实践规则

## 使用场景
当设计新的用户界面、开发前端功能、编写CSS代码、确保用户体验时使用此规则。特别适用于需要遵循系统可见性原则的交互设计场景。

## 核心原则

### 1. 系统可见性原则 (MUST)
**用户在任何交互时刻都应清楚知道系统状态**
- 所有用户操作必须提供即时、清晰的反馈
- 使用加载指示器、状态消息、按钮状态变化等多种反馈形式
- 反馈应在毫秒级时间内呈现
- 长时间操作必须提供进度指示或取消选项

### 2. CSS 组织与维护 (MUST)
- 按功能、模块或页面组织 CSS 文件
- 使用一致的命名约定（推荐 BEM 思想）
- 避免单一巨大 CSS 文件
- 禁止过度依赖 `!important`
- 父容器设置 `border-radius` 时需添加 `overflow: hidden`

### 3. 响应式设计 (MUST)
- 确保在所有常见设备上提供良好体验
- 使用媒体查询针对不同屏幕尺寸优化
- 采用 Flexbox、Grid 等现代布局技术
- 确保触摸目标在小屏幕上足够大

### 4. 可访问性 (SHOULD)
- 确保足够的色彩对比度（WCAG AA 级别 4.5:1）
- 为表单控件提供关联的 `<label>` 标签
- 支持键盘导航
- 使用语义化 HTML 标签

### 5. 性能优化 (SHOULD)
- 优化 CSS 选择器性能
- 生产环境压缩 CSS 文件
- 优先使用 `transform` 和 `opacity` 做动画
- 避免使用 `@import` 指令

## 关键约束

### 必须禁止 (MUST NOT)
- 用户操作后界面无任何反馈
- 使用过于通用的标签选择器进行大范围样式定义
- 仅通过颜色区分信息或状态
- 移除 `outline` 样式而无替代方案

### 应该避免 (SHOULD NOT)
- 在没有充分理由的情况下过度使用动画
- 为固定宽度设计导致小屏幕内容截断
- 创建无法键盘访问的交互组件

## 实用技巧

### CSS 缓存问题解决
在 ASP.NET Web Forms 项目中，为 CSS 文件添加版本号查询字符串：
```html
<link rel="stylesheet" href="/Template/CSS/output.css?v=1">
```

### Lucide 图标处理
使用 Lucide 时，CSS 选择器应匹配动态生成的 `<svg>` 元素：
```css
/* 正确：匹配转换后的 svg */
.my-button svg { }
/* 错误：匹配原始的 i 标签 */
.my-button i { }
```

### 下拉菜单层级管理
建立清晰的 z-index 层级体系：
- header: 100
- modal: 200  
- dropdown: 90
- button: 50

## 检查清单
开发时必须验证：
- [ ] 所有交互操作都有即时反馈
- [ ] CSS 文件按模块化组织
- [ ] 响应式布局在主要设备上正常显示
- [ ] 色彩对比度符合可访问性要求
- [ ] 动画性能良好，无卡顿
- [ ] 键盘导航功能正常

## 参考文档
详细的技术实现、示例代码、常见问题解决方案请参考：
**`.cursor/rules/frontend-ui-principles-reference.md`**

该文档包含完整的：
- 核心原则详细说明和示例代码
- 常见问题与解决方案
- FriendList.aspx 实践案例分析
- 系统可见性原则的具体实现
- CSS 组织与维护最佳实践
- 响应式设计和可访问性指南