{{#if Message.HasMessage}}
{{#unless Message.IsSuccess}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/unless}}
{{/if}}

{{#if Message.IsSuccess}}
<!-- 成功状态卡片 -->
<div class="card text-center border border-border-light">
    <div class="bg-white text-text-primary py-8 px-4 pb-6 border-b border-border-light">
        <div class="mb-4">
            <i data-lucide="check-circle" class="w-16 h-16 stroke-2 text-primary mx-auto"></i>
        </div>
        <h3 class="text-xl font-semibold m-0 text-text-primary">密码修改成功</h3>
    </div>
    <div class="py-6 px-4 pb-8 bg-white">
        <p class="text-text-secondary mb-4 leading-6">您的密码已成功修改，请使用新密码重新登录。</p>
        <p class="text-text-secondary text-sm mb-6 animate-pulse">
            <span id="countdown-text">10秒后自动返回登录</span>
        </p>
        <div class="flex justify-center items-center">
            <a href="/waplogin.aspx" class="btn btn-primary py-3 px-6 text-base">
                <i data-lucide="log-in" class="mr-2"></i>
                重新登录
            </a>
        </div>
    </div>
    <div class="bg-bg-gray-50 border-t border-border-light p-4 text-center">
        <p class="flex items-center justify-center text-text-light text-sm m-0 gap-2">
            <i data-lucide="alert-triangle" class="w-4 h-4"></i>
            请勿在公共设备保存密码
        </p>
    </div>
</div>
{{else}}
<!-- 修改密码表单 -->
<form id="password-form" action="{{FormData.ActionUrl}}" method="post">
    <div class="card mt-4">
        <div class="card-header">
            <div class="card-title">
                <i data-lucide="shield" class="card-icon"></i>
                修改密码
            </div>
        </div>
        
        <div class="card-body">
            <div class="form-group">
                <label class="form-label required">原密码</label>
                <input type="password" 
                       name="txtoldPW" 
                       id="old-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请输入原密码"
                       autocomplete="current-password"
                       required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">新密码</label>
                <input type="password" 
                       name="txtnewPW" 
                       id="new-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请输入新密码"
                       autocomplete="new-password"
                       required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">确认新密码</label>
                <input type="password" 
                       name="txtrePW" 
                       id="confirm-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请再次输入新密码"
                       autocomplete="new-password"
                       required>
            </div>
            
            <div class="bg-bg-gray-50 rounded p-4 mb-4">
                <h4 class="text-sm font-medium text-text-secondary mb-3">新密码要求</h4>
                <ul class="list-none p-0 m-0">
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-length">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>至少6个字符</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-uppercase">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含大写字母</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-lowercase">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含小写字母</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-number">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含数字</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 隐藏字段 -->
    <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
    <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
    <input type="hidden" name="classid" value="{{FormData.HiddenFields.ClassId}}">

    <!-- 提交按钮 -->
    <div class="flex justify-center mt-4 px-4">
        <button type="submit" class="btn btn-primary w-full py-3 px-6 text-base" id="submit-btn">
            <i data-lucide="shield-check" class="w-5 h-5"></i>
            修改密码
        </button>
    </div>
</form>
{{/if}}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 如果是成功状态，启动倒计时
        if (document.querySelector('.bg-white.rounded-md.shadow-md.mx-4.overflow-hidden.text-center')) {
            lucide.createIcons();
            startCountdown();
            return;
        }

        // 倒计时功能
        function startCountdown() {
            let countdown = 10;
            const countdownElement = document.getElementById('countdown-text');
            
            const timer = setInterval(function() {
                countdown--;
                countdownElement.textContent = countdown + '秒后自动返回登录';
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    window.location.href = '/waplogin.aspx';
                }
            }, 1000);
        }

        // 表单验证和提交处理
        const form = document.getElementById('password-form');
        const submitButton = document.getElementById('submit-btn');
        const oldPasswordInput = document.getElementById('old-password');
        const newPasswordInput = document.getElementById('new-password');
        const confirmPasswordInput = document.getElementById('confirm-password');

        // 密码规则验证元素
        const ruleLengthEl = document.getElementById('rule-length');
        const ruleUppercaseEl = document.getElementById('rule-uppercase');
        const ruleLowercaseEl = document.getElementById('rule-lowercase');
        const ruleNumberEl = document.getElementById('rule-number');
        const ruleMatchEl = document.getElementById('rule-match');

        // 验证规则函数
        function validatePasswordRules() {
            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            // 长度验证
            const hasLength = newPassword.length >= 6;
            updateRuleStatus(ruleLengthEl, hasLength);

            // 大写字母验证
            const hasUppercase = /[A-Z]/.test(newPassword);
            updateRuleStatus(ruleUppercaseEl, hasUppercase);

            // 小写字母验证
            const hasLowercase = /[a-z]/.test(newPassword);
            updateRuleStatus(ruleLowercaseEl, hasLowercase);

            // 数字验证
            const hasNumber = /\d/.test(newPassword);
            updateRuleStatus(ruleNumberEl, hasNumber);

            // 密码匹配验证
            const passwordsMatch = newPassword.length > 0 && newPassword === confirmPassword;
            updateRuleStatus(ruleMatchEl, passwordsMatch);

            return hasLength && hasUppercase && hasLowercase && hasNumber && passwordsMatch;
        }

        function updateRuleStatus(ruleElement, isValid) {
            const icon = ruleElement.querySelector('.w-4.h-4');
            if (isValid) {
                ruleElement.classList.add('text-success');
                ruleElement.classList.remove('text-text-light');
                icon.classList.remove('opacity-30');
                icon.classList.add('opacity-100');
                icon.classList.add('text-success');
            } else {
                ruleElement.classList.remove('text-success');
                ruleElement.classList.add('text-text-light');
                icon.classList.add('opacity-30');
                icon.classList.remove('opacity-100');
                icon.classList.remove('text-success');
            }
        }

        // 实时验证
        newPasswordInput.addEventListener('input', validatePasswordRules);
        confirmPasswordInput.addEventListener('input', validatePasswordRules);

        // 表单提交处理
        form.addEventListener('submit', function(e) {
            // 基础验证
            if (!oldPasswordInput.value.trim()) {
                e.preventDefault();
                showFieldError(oldPasswordInput, '请输入原密码');
                return;
            }

            if (!newPasswordInput.value.trim()) {
                e.preventDefault();
                showFieldError(newPasswordInput, '请输入新密码');
                return;
            }

            if (!confirmPasswordInput.value.trim()) {
                e.preventDefault();
                showFieldError(confirmPasswordInput, '请确认新密码');
                return;
            }

            // 显示提交状态
            submitButton.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin"></i>修改中...';
            submitButton.disabled = true;
            
            // 重新创建图标
            setTimeout(() => {
                lucide.createIcons();
            }, 10);
        });

        // 输入框失焦验证
        const inputs = [oldPasswordInput, newPasswordInput, confirmPasswordInput];
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                // 清除错误状态
                if (this.classList.contains('border-danger')) {
                    this.classList.remove('border-danger');
                    clearFieldError(this);
                }
            });
        });

        // 字段验证函数
        function validateField(field) {
            const value = field.value.trim();
            const name = field.name;
            let isValid = true;
            let errorMessage = '';

            if (name === 'txtoldPW' && value.length === 0) {
                isValid = false;
                errorMessage = '原密码不能为空';
            } else if (name === 'txtnewPW' && value.length === 0) {
                isValid = false;
                errorMessage = '新密码不能为空';
            } else if (name === 'txtrePW' && value.length === 0) {
                isValid = false;
                errorMessage = '确认密码不能为空';
            } else if (name === 'txtrePW' && value !== newPasswordInput.value) {
                isValid = false;
                errorMessage = '两次密码输入不一致';
            }

            // 显示验证结果
            if (!isValid) {
                field.classList.add('border-danger');
                showFieldError(field, errorMessage);
            } else {
                field.classList.remove('border-danger');
                clearFieldError(field);
            }

            return isValid;
        }

        // 显示字段错误
        function showFieldError(field, message) {
            clearFieldError(field);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-xs text-danger mt-1';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        // 清除字段错误
        function clearFieldError(field) {
            const existingError = field.parentNode.querySelector('.text-xs.text-danger');
            if (existingError) {
                existingError.remove();
            }
        }

        // 初始化图标
        lucide.createIcons();
    });
</script> 