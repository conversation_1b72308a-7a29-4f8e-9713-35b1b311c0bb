---
description: "针对 ASP.NET Web Forms 的特定样式、结构和最佳实践，包括 .aspx, .ascx 文件处理、页面生命周期、服务器控件、ViewState 和 PostBack 注意事项。"
globs: *.aspx,*.ascx,*.aspx.cs,*.ascx.cs
alwaysApply: false
---
## Web Forms Style & Structure
- 理解 .aspx 标记（服务器控件、指令如 <%@ Page %>、<%@ Import %>、<%@ Register %>）及 .aspx.cs 代码后置。
- 熟悉页面生命周期（如 Page_Load, Page_Init）。
- 使用服务器控件（如 <asp:Label>, <asp:Button>）和用户控件（.ascx），遵循项目既有模式（如 FaceAndReply.ascx, LikeModule.ascx）。
- 理解 ViewState 和 PostBack，尤其在如 Book_Re.aspx.cs 中 ViewState["OriginalFloors"] 的用法。
- 善用 <% ... %> 代码块和 StringBuilder 动态渲染 HTML。
- 关注WAP/移动端渲染和逻辑。
- 复杂页面建议分层渲染，减少ViewState体积，优化PostBack性能。

**新增规则点 (基于重构经验):**

- **Code-Behind 文件内部重构优先原则**: 对于复杂或冗长的 Code-Behind 文件（例如包含大量逻辑的 `Page_Load`、`add` 等方法），应优先考虑将其内部逻辑提取到同一类中的、职责清晰的 `private` 方法中。这是降低直接修改风险、同时改善代码结构的第一步。这种内部重构有助于保持对页面上下文 (`this`, `base` 成员, `HttpContext` 等) 的直接访问，减少初期重构的复杂性。只有在内部逻辑得到充分简化和梳理后，再评估是否可以将无状态、与页面上下文解耦的逻辑迁移到外部的静态帮助类或服务层。

- **栏目/站点配置 (`classVo.smallimg`, `siteVo`) 处理指南**: 直接依赖解析和解释 `classVo.smallimg` 字段（栏目特定配置）或 `siteVo` 设置的业务逻辑，原则上应保留在页面的 Code-Behind (`.aspx.cs`) 文件中。如果为了组织代码而将这部分逻辑提取到私有辅助方法，这些方法也应作为页面类的成员。避免将此类强依赖上下文配置的逻辑迁移到通用的服务层或帮助类中，除非这些配置值作为显式参数传递，并且处理逻辑本身已变得通用化。

    - **示例：动态配置 BBS 列表排除栏目**
        - **场景**: 在 BBS 列表中，需要根据配置排除某些特定的栏目帖子。
        - **问题**: 硬编码的排除栏目 ID 不易维护和调整。
        - **解决方案**: 将需要排除的栏目 ID 配置到 `web.config`，并在代码中动态读取和构建查询条件。
        - **`web.config` 配置示例**:
          ```xml
          <appSettings>
            <!-- 需要排除的 BBS 栏目 ID，多个用逗号分隔 -->
            <add key="KL_BBS_ExcludeClassIDs" value="299,300" />
          </appSettings>
          ```
        - **代码实现 (`Book_List.aspx.cs` 片段)**:
          ```csharp
          // ... existing code ...
          string excludedClassIDsConfig = PubConstant.GetAppString("KL_BBS_ExcludeClassIDs");
          if (!string.IsNullOrEmpty(excludedClassIDsConfig))
          {
              string[] idsToExclude = excludedClassIDsConfig.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
              if (idsToExclude.Length > 0)
              {
                  // 构建 NOT IN 子句，并确保 ID 为数字以防注入
                  condition += " and book_classid NOT IN (" + string.Join(",", idsToExclude.Where(id => long.TryParse(id.Trim(), out _)).Select(id => id.Trim())) + ")";
              }
          }
          // ... existing code ...
          ```
        - **要点**:
            - 利用 `PubConstant.GetAppString` 读取 `web.config` 配置。
            - 使用 `Split` 和 `StringSplitOptions.RemoveEmptyEntries` 处理逗号分隔的多个 ID。
            - 在构建 SQL 的 `NOT IN` 子句时，务必对 ID 进行数字验证（如 `long.TryParse`）以增强安全性。
            - 动态生成的 SQL 条件应追加到现有 `condition` 字符串中。

- **`ViewState`, `Session`, `Request` 对象使用规范**: 对 `ViewState`、`Session`、`Request.QueryString`、`Request.Form` 等 ASP.NET 内置对象的访问，建议集中在方法的起始部分（例如 `Page_Load` 或具体操作处理方法的开头）。获取到的值应作为参数传递给后续的私有辅助方法或业务逻辑服务，而不是让深层嵌套的方法反复访问这些全局状态对象。这样做可以提高代码的可测试性，并使数据流向更明确。

- AJAX 请求处理规范: 在 ASP.NET Web Forms 中实现 AJAX 处理程序时，应遵循以下规范：
    - 清晰地识别 AJAX 请求（例如，通过特定的查询参数 `ajax=1` 或表单字段）。
    - 确保处理方法在输出响应前调用 `Response.Clear()` 清除任何现有输出。
    - 正确设置响应的 `ContentType` (例如, `text/html` 用于 `FastC.js` 或 `application/json` 用于其他场景)。
    - 使用 `Response.Write()` 输出响应内容。
    - 必须调用 `Response.End()` 来终止当前页面的处理流程，防止完整页面的其余部分被渲染。
    - 建议将 AJAX 响应的构建逻辑封装到独立的私有方法中，以保持主处理流程的清晰。

- HTML5 文档类型和命名空间: 在使用 HTML5 文档类型 (`<!DOCTYPE html>`) 时，`<html>` 标签上的 `xmlns="http://www.w3.org/1999/xhtml"` 属性是不必要的，可以移除。它主要用于 XHTML，而在以 `text/html` 提供的 HTML5 页面中会被忽略。


相关C#风格、命名、性能、安全等请参见主规则。