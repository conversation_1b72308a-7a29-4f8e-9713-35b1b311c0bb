﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 回复状态扩展方法
    /// </summary>
    public static class ReplyExtensions
    {
        /// <summary>
        /// 检查回复是否为已被用户删除
        /// </summary>
        public static bool IsUserDeleted(this ReplyData reply) => reply != null && (reply.CheckStatus == 2);

        /// <summary>
        /// 检查回复是否为已被管理员删除
        /// </summary>
        public static bool IsAdminDeleted(this ReplyData reply) => reply != null && (reply.CheckStatus == 3);

        /// <summary>
        /// 检查回复是否已被删除（无论是用户还是管理员删除）
        /// </summary>
        public static bool IsDeleted(this ReplyData reply) => reply != null && (reply.CheckStatus == 2 || reply.CheckStatus == 3);

        /// <summary>
        /// 获取删除类型描述文本
        /// </summary>
        public static string GetDeletedText(this ReplyData reply)
        {
            if (reply == null) return string.Empty;
            if (reply.CheckStatus == 2) return "[此回复已被用户删除]";
            if (reply.CheckStatus == 3) return "[此回复已被管理员删除]";
            return string.Empty;
        }
    }

    /// <summary>
    /// 帖子回复页（包括AJAX处理）
    /// </summary>
    public class Book_Re : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string KL_BBSRE_Anonymous_Open = PubConstant.GetAppString("KL_BBSRE_Anonymous_Open");

        public string KL_CheckBBSreCount = PubConstant.GetAppString("KL_CheckBBSreCount");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public string KelinkWAP_Check = PubConstant.GetConnectionString("kelinkWAP_Check");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string INFO = "";

        public string id = "0";

        public string lpage = "";

        public string ot = "0";

        public string[] facelist;

        public string[] facelistImg;

        public string reShowInfo = "";

        public List<user_Model> userListVo_IDName = null;

        public List<wap_bbsre_Model> listVo = null;

        public List<wap_bbsre_Model> listVoTop = null;

        public wap_bbs_Model bookVo = null;

        public wap_bbs_BLL bbsbll = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        public string getmoney = "0";

        public string getexpr = "0";

        public long getTodayMoney = 0L;

        public long allMoney = 0L;

        public string reply = "";

        public string needpwFlag = "";

        public string contentmax = "2";

        public string content_max = "2";

        public string sendmsg = "";

        public string sendmsg2 = "";

        public string fromuserid = "";

        public string mainuserid = "";

        public string showhead = "0";

        public string getmoney2 = "";

        public bool isNeedSecret = false;

        public string orderby = "0";

        // 新增：用于存储每个回帖ID到其真实原始楼层号的映射
        /// <summary>
        /// 回复ID到原始楼层号的映射字典。
        /// 用途：
        /// 1. 存储所有回复（包括正常和已删除状态）的楼层号信息
        /// 2. 确保"回复X楼"中的X始终指向原始准确的楼层，即使该楼已被删除
        /// 3. 支持前端根据回复ID正确显示其原始楼层号，保证楼层显示的稳定性
        /// </summary>
        public Dictionary<long, int> ReplyIdToOriginalFloorMap { get; private set; }

        // --- 新增：AJAX 响应结果类 ---
        private class ReplyResult
        {
            public string Status { get; set; } // "OK", "NULL", "WAITING", "NOMONEY", etc.
            public long EarnedMoney { get; set; }
            public long EarnedExperience { get; set; }
            public string ErrorMessage { get; set; } // 具体的错误文本
        }
        // --- 结束：AJAX 响应结果类 ---

        // 新增：新版回帖开关，默认开启
        public bool IsNewReplyUIEnabled { get; private set; } = true;

        /// <summary>
        /// 初始化回帖相关的配置设置 (如内容长度、奖励等)
        /// </summary>
        private void InitializeReplySettings()
        {
            this.contentmax = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 26);
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.contentmax) || this.contentmax == "0")
            {
                this.contentmax = "0"; // 最小长度限制
            }
            this.content_max = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 32);
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.content_max))
            {
                this.content_max = "0"; // 最大长度限制
            }
            this.getmoney2 = (KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 36) + ",").Split(',')[0]; // 回帖扣费/奖励 (栏目设置，可能为负数)
            this.getmoney = KeLin.ClassManager.Tool.WapTool.GetSiteDefault(base.siteVo.moneyregular, 1); // 站点默认回帖奖励金币
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.getmoney))
            {
                this.getmoney = "0";
            }
            this.getexpr = KeLin.ClassManager.Tool.WapTool.GetSiteDefault(base.siteVo.lvlRegular, 1); // 站点默认回帖奖励经验
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.getexpr))
            {
                this.getexpr = "0";
            }
            // 覆盖站点默认值（如果栏目有特定设置）
            string[] classMoneyExpr = (KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 36) + ",").Split(',');
            if (KeLin.ClassManager.Tool.WapTool.IsNumeric(classMoneyExpr[0].Replace("-", "")))
            {
                this.getmoney = classMoneyExpr[0];
            }
            if (classMoneyExpr.Length > 1 && KeLin.ClassManager.Tool.WapTool.IsNumeric(classMoneyExpr[1].Replace("-", "")))
            {
                this.getexpr = classMoneyExpr[1]; // 栏目经验奖励
            }
        }

        /// <summary>
        /// 初始化常规PostBack请求所需的页面参数和状态。
        /// </summary>
        /// <returns>true 如果初始化成功并且可以继续处理页面逻辑，否则 false。</returns>
        private bool InitializePageForPostBack()
        {
            if (base.classid != "0" && base.classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                base.ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
                return false; // 指示 Page_Load 应提前返回
            }

            this.lpage = base.GetRequestValue("lpage");
            this.ot = base.GetRequestValue("ot");
            this.reply = base.GetRequestValue("reply");
            this.needpwFlag = KeLin.ClassManager.Tool.WapTool.getArryString(base.siteVo.Version, '|', 31);
            this.showhead = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 38);
            this.orderby = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 43);
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.orderby))
            {
                this.orderby = "0";
            }
            if (base.classVo.topicID != "" && base.classVo.topicID != "0" && base.IsCheckManagerLvl("|00|01|03|04|", ""))
            {
                this.isNeedSecret = true;
            }

            // 加载和验证 bookVo (PostBack)
            if (!LoadAndValidateBookVo()) // LoadAndValidateBookVo 内部会 ShowTipInfo
            {
                return false; // 指示 Page_Load 应提前返回
            }

            // 加载表情信息
            try
            {
                // 使用新的 BBSHelper.GetProcessedFaceAndReShowInfo 方法
                var processedFaceInfo = BBSHelper.GetProcessedFaceAndReShowInfo(this.classVo);
                this.facelist = processedFaceInfo.FaceList;
                this.facelistImg = processedFaceInfo.FaceListImg;
                this.reShowInfo = processedFaceInfo.ReShowInfo;
            }
            catch
            {
                // 此处的catch块理论上可以移除，因为新的辅助方法内部已经处理了异常并返回默认值
                // 但为保持结构，暂时保留，确保即使辅助方法抛出未预期的异常（不太可能），也有回退
                this.facelist = Array.Empty<string>();
                this.facelistImg = Array.Empty<string>();
                this.reShowInfo = "";
            }

            // 新增：读取新版回帖开关状态，从数据库读取而非Cookie
            this.IsNewReplyUIEnabled = UserPreferencesRepository.GetUserNewReplyUIEnabled(userid, a, Request);

            return true; // 初始化成功
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            this.action = base.Request.Form.Get("action");
            this.id = base.GetRequestValue("id");
            this.sendmsg = base.GetRequestValue("sendmsg");
            this.sendmsg2 = base.GetRequestValue("sendmsg2");
            this.fromuserid = base.GetRequestValue("fromuserid");
            if (string.IsNullOrEmpty(this.fromuserid))
            {
                this.fromuserid = base.GetRequestValue("touserid");
            }

            // --- 新增：调用处理tofloor参数的逻辑 ---
            HandleToFloorNavigationLogic();
            // 如果HandleToFloorNavigationLogic内部发生了Response.Redirect(..., true)，
            // 当前Page_Load的执行会在此处中止，后续代码不会运行。

            // --- 新增：AJAX 快速回复处理 ---
            if ("1".Equals(base.GetRequestValue("ajax")) && "add".Equals(this.action))
            {
                // 在处理 AJAX 前，也需要加载 bookVo 并进行基本校验
                if (!LoadAndValidateBookVoForAjax())
                {
                    return; // LoadAndValidateBookVoForAjax 内部会发送响应并 End
                }
                HandleAjaxAddReply();
                // HandleAjaxAddReply 内部会调用 Response.End()
                return; // 明确返回，避免执行后续 PostBack 逻辑
            }
            // --- 结束：AJAX 快速回复处理 ---

            // --- 常规 PostBack 请求的初始化 ---
            if (!InitializePageForPostBack())
            {
                return; // 如果初始化失败，则终止后续处理
            }

            // --- PostBack Action 处理 ---
            switch (this.action)
            {
                case "add":
                    this.Add(); // 只处理 PostBack 的 add
                    break;

                case "class":
                    this.ShowClass();
                    break;

                default:
                    this.ShowClass();
                    break;
            }
        }

        /// <summary>
        /// 处理 tofloor URL参数导航逻辑，计算目标页并可能重定向。
        /// </summary>
        private void HandleToFloorNavigationLogic()
        {
            string targetFloorParam = Request.QueryString["tofloor"];
            if (!string.IsNullOrEmpty(targetFloorParam) && int.TryParse(targetFloorParam, out int targetOriginalFloor) && targetOriginalFloor > 0)
            {
                // 初始化pageSize，避免使用未初始化的pageSize
                if (base.classVo.ismodel < 1L)
                {
                    this.pageSize = Convert.ToInt32(base.siteVo.MaxPerPage_Default);
                }
                else
                {
                    this.pageSize = Convert.ToInt32(base.classVo.ismodel);
                }
                if (this.pageSize <= 0) this.pageSize = 10;

                // 先加载帖子信息，因为需要bookVo进行页码计算
                if (this.id != "0" && KeLin.ClassManager.Tool.WapTool.IsNumeric(this.id))
                {
                    if (this.bbsbll == null) // 确保 bbsbll 已初始化
                    {
                        this.bbsbll = new wap_bbs_BLL(this.a);
                    }
                    this.bookVo = this.bbsbll.GetModel(long.Parse(this.id));
                    if (this.bookVo != null)
                    {
                        // 获取用户过滤设置（从cookie读取） - 这行似乎没有直接使用，但在原始逻辑中存在
                        // bool hideUselessCookie = Request.Cookies["hideUseless"]?.Value == "1";

                        // 系统也可能有默认过滤设置，所以综合判断是否应该过滤 - 这行似乎没有直接使用，但在原始逻辑中存在
                        // bool shouldFilter = ReplyHelper.ShouldApplySqlFilter(base.Request, this.bookVo);

                        // 计算目标楼层所在页码
                        int targetPage = CalculatePageForOriginalFloor(targetOriginalFloor);
                        if (targetPage > 0)
                        {
                            this.CurrentPage = targetPage; // 设置当前页为计算出的目标页
                            ViewState["ScrollToFloorID"] = targetOriginalFloor; // 传递给前端，用于JS滚动
                            ViewState["FromToFloor"] = true;

                            // 检查URL是否包含page参数
                            string currentPageParam = Request.QueryString["page"];
                            if (string.IsNullOrEmpty(currentPageParam) || currentPageParam != targetPage.ToString())
                            {
                                // 当前URL没有正确的page参数，需要重定向
                                // 构建重定向URL
                                string redirectUrl = Request.Url.AbsolutePath + "?";
                                var queryParams = new System.Collections.Specialized.NameValueCollection(Request.QueryString)
                                {
                                    ["page"] = targetPage.ToString() // 设置正确的页码
                                };

                                // 添加所有查询参数
                                bool isFirst = true;
                                foreach (string key in queryParams.AllKeys)
                                {
                                    if (!isFirst) redirectUrl += "&";
                                    redirectUrl += key + "=" + queryParams[key];
                                    isFirst = false;
                                }

                                Response.Redirect(redirectUrl, true); // 使用 true 来结束响应并避免进一步处理
                                // return; // 在Page_Load中调用此方法时，如果发生重定向，Page_Load本身需要能提前返回
                            }
                        }
                        else
                        {
                            // 只有在置顶和普通回帖都找不到时，才设置FilteredOutFloor
                            ViewState["FilteredOutFloor"] = targetOriginalFloor;
                            // 新增：调用新方法获取具体原因
                            ViewState["FilteredOutFloorReason"] = DetermineFilteredOutFloorReason(long.Parse(this.id), targetOriginalFloor);
                        }
                    }
                    // else: bookVo is null, do nothing further for tofloor, Page_Load will handle bookVo validation later.
                }
                // else: id is invalid or "0", do nothing further for tofloor.
            }
        }

        /// <summary>
        /// 根据给定的帖子ID和原始楼层号，查询数据库确定该楼层未被找到（或被过滤）的具体原因。
        /// </summary>
        /// <param name="bookId">帖子的ID。</param>
        /// <param name="targetOriginalFloor">目标原始楼层号。</param>
        /// <returns>一个字符串，表示楼层状态，如 "userdeleted", "admindeleted", "pending", "filtered", "notfound", "unknown"。</returns>
        private string DetermineFilteredOutFloorReason(long bookId, int targetOriginalFloor)
        {
            try
            {
                using (var conn = new System.Data.SqlClient.SqlConnection(PubConstant.GetConnectionString(this.a)))
                {
                    conn.Open();
                    string sql = @"
                        WITH AllReplies AS (
                            SELECT id, ROW_NUMBER() OVER (ORDER BY id ASC) AS OriginalFloor, isCheck
                            FROM wap_bbsre WITH (NOLOCK)
                            WHERE devid = @SiteId AND bookid = @BookId AND isCheck IN (0,1,2,3)
                        )
                        SELECT TOP 1 isCheck FROM AllReplies WHERE OriginalFloor = @TargetFloor
                    ";
                    using (var cmd = new System.Data.SqlClient.SqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", base.siteid);
                        cmd.Parameters.AddWithValue("@BookId", bookId);
                        cmd.Parameters.AddWithValue("@TargetFloor", targetOriginalFloor);
                        var result = cmd.ExecuteScalar();
                        if (result != null && result != System.DBNull.Value)
                        {
                            int checkStatus = Convert.ToInt32(result);
                            if (checkStatus == 2) return "userdeleted";
                            if (checkStatus == 3) return "admindeleted";
                            if (checkStatus == 1) return "pending";
                            if (checkStatus == 0) return "filtered"; // isCheck = 0 意味着正常但被过滤掉了
                            return "filtered"; // 其他情况或未明确的 isCheck 值, 默认视为过滤
                        }
                        else // 如果查询没有返回任何行，说明楼层号超出现有范围
                        {
                            return "notfound";
                        }
                    }
                }
            }
            catch
            {
                return "unknown"; // 查询出错
            }
        }

        /// <summary>
        /// 尝试加载并验证帖子信息。
        /// </summary>
        /// <param name="errorMessage">如果验证失败，则包含错误信息；否则为 null。</param>
        /// <param name="isAjaxRequest">指示当前请求是否为 AJAX 请求，用于错误消息中的重定向URL（如果适用）。</param>
        /// <returns>true 如果加载和验证成功，否则 false。</returns>
        private bool TryLoadAndValidateBook(out string errorMessage, bool isAjaxRequest)
        {
            errorMessage = null;
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(this.id))
            {
                errorMessage = "帖子ID参数为非数字！";
                return false;
            }

            // 确保 bbsbll 已初始化
            if (this.bbsbll == null)
            {
                this.bbsbll = new wap_bbs_BLL(this.a);
            }

            this.bookVo = this.bbsbll.GetModel(long.Parse(this.id));

            if (this.bookVo == null)
            {
                errorMessage = "帖子已删除，或不存在。";
                return false;
            }
            else if (this.bookVo.ischeck == 2L) // 回收站
            {
                errorMessage = "帖子在回收站中。";
                if (!isAjaxRequest) // 仅 PostBack 检查权限并可能重定向
                {
                    // 对于PostBack，如果帖子在回收站，需要管理员权限才能继续，否则会提示并可能重定向
                    // CheckManagerLvl 会处理权限不足的情况（可能抛出异常或重定向）
                    // 如果不希望中断流程，这里可能需要调整 CheckManagerLvl 的行为或调用方式
                    // 为保持原状，这里仅设置错误信息，具体的权限检查留给原调用处（如果它有的话）
                }
                return false; // 无论如何，帖子在回收站中都是一种验证失败的状态
            }
            else if (this.bookVo.ischeck == 1L) // 审核中
            {
                errorMessage = "帖子正在审核中！";
                if (!isAjaxRequest) // 仅 PostBack 检查权限
                {
                    if (!base.IsUserManager(userid, userVo.managerlvl, classVo.adminusername))
                    {
                        // 对于 PostBack，如果非管理员，则确实是验证失败
                        // errorMessage 已设置，由调用方处理
                    }
                    else
                    {
                        // 如果是管理员，对于 PostBack 路径，这不算验证失败，允许继续加载
                        errorMessage = null; // 清除错误信息，表示验证通过
                        return true;
                    }
                }
                return false; // AJAX 或非管理员 PostBack 均视为验证失败
            }
            else if (this.bookVo.book_classid.ToString() != base.classid)
            {
                errorMessage = "栏目ID不正确！";
                return false;
            }
            else if (this.bookVo.islock == 1L) // 锁定
            {
                errorMessage = "此帖已锁定！";
                return false;
            }
            else if (isAjaxRequest && this.bookVo.islock == 2L) // 已结束 (仅 AJAX 请求时阻止回复)
            {
                errorMessage = "此帖已结束，无法回复！";
                return false;
            }

            return true; // 所有检查通过
        }

        /// <summary>
        /// 加载并验证帖子信息 (用于 PostBack 请求)
        /// </summary>
        /// <returns>true 如果加载和验证成功，否则 false</returns>
        private bool LoadAndValidateBookVo()
        {
            if (!TryLoadAndValidateBook(out string errorMessage, isAjaxRequest: false))
            {
                string redirectUrl = ""; // 统一默认为空字符串

                // 特殊处理：帖子在回收站中时，仍需调用 CheckManagerLvl
                if (errorMessage == "帖子在回收站中。")
                {
                    // CheckManagerLvl 可能会自行处理重定向或中止流程
                    CheckManagerLvl("04", classVo.adminusername, "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
                    // 如果 CheckManagerLvl 未中止，ShowTipInfo 将使用 redirectUrl = ""
                }
                // 对于其他所有错误，redirectUrl 都保持为空字符串，由 ShowTipInfo 提供默认返回行为

                base.ShowTipInfo(errorMessage, redirectUrl);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 加载并验证帖子信息 (用于 AJAX 请求)
        /// </summary>
        /// <returns>true 如果加载和验证成功，否则 false (并已发送 AJAX 响应)</returns>
        private bool LoadAndValidateBookVoForAjax()
        {
            if (!TryLoadAndValidateBook(out string errorMessage, isAjaxRequest: true))
            {
                SendAjaxResponse("<div class=\"tip\">" + errorMessage + "</div>");
                return false;
            }
            return true;
        }

        // --- 新增：AJAX 响应辅助方法 ---
        private void SendAjaxResponse(string htmlContent)
        {
            Response.Clear();
            Response.ContentType = "text/html"; // FastPost.js 需要 HTML
            Response.Write(htmlContent);
            Response.End();
        }
        // --- 结束：AJAX 响应辅助方法 ---

        /// <summary>
        /// 计算指定原始楼层号在过滤后数据中的页码（C#过滤后）
        /// </summary>
        private int CalculatePageForOriginalFloor(int targetOriginalFloor)
        {
            if (this.bookVo == null || string.IsNullOrEmpty(this.id) || !long.TryParse(this.id, out long bookIdParsed))
            {
                return -1;
            }

            // 1. 优先查找置顶回帖
            // 确保 listVoTop 和 ReplyIdToOriginalFloorMap 已被 showclass 方法初始化
            // （在 tofloor 逻辑中，showclass 可能尚未完全执行，需要提前加载 listVoTop 和 ReplyIdToOriginalFloorMap 的相关部分）
            // 临时的解决方案是，如果这两个为空，先尝试加载它们
            if (this.listVoTop == null || this.ReplyIdToOriginalFloorMap == null)
            {
                // 尝试预加载置顶信息
                string tempSortOrder = (this.ot == "1") ? "ASC" : "DESC";
                this.listVoTop = ReplyHelper.GetTopReplies(base.siteid, bookIdParsed, this.a, tempSortOrder);
                this.ReplyIdToOriginalFloorMap = new Dictionary<long, int>();
                if (this.listVoTop != null && this.listVoTop.Any())
                {
                    var preLoadReplyRepository = new ReplyRepository(this.a); // 避免与后续replyRepository重名
                    List<long> tempTopReplyIds = this.listVoTop.Select(tr => tr.id).ToList();
                    if (tempTopReplyIds.Any())
                    {
                        Dictionary<long, int> topOriginalFloors = preLoadReplyRepository.GetOriginalFloorsByIds(base.siteid, bookIdParsed, tempTopReplyIds);
                        foreach (var kvp in topOriginalFloors)
                        {
                            if (!this.ReplyIdToOriginalFloorMap.ContainsKey(kvp.Key))
                            {
                                this.ReplyIdToOriginalFloorMap.Add(kvp.Key, kvp.Value);
                            }
                        }
                    }
                }
            }

            if (this.listVoTop != null && this.listVoTop.Any() && this.ReplyIdToOriginalFloorMap != null)
            {
                foreach (var topReply in this.listVoTop)
                {
                    if (this.ReplyIdToOriginalFloorMap.TryGetValue(topReply.id, out int topFloor))
                    {
                        if (topFloor == targetOriginalFloor)
                        {
                            return 1; // <--- 关键修改：直接返回1
                        }
                    }
                }
            }

            var replyRepository = new ReplyRepository(this.a);
            long currentUserIdForFiltering = (base.userid != "0" && long.TryParse(base.userid, out var tempUserId)) ? tempUserId : 0;
            bool hideUseless = ReplyHelper.ShouldApplySqlFilter(base.Request, this.bookVo);
            string sortOrder = (this.ot == "1") ? "ASC" : "DESC";

            // 拉取所有回复
            List<ReplyData> allReplies = replyRepository.GetRepliesPaged(
                bookIdParsed,
                this.siteid,
                1,
                99999,
                sortOrder,
                this.mainuserid,
                false,
                currentUserIdForFiltering,
                true // includeTopReplies: true，确保包含置顶楼层
            );

            // C#过滤
            List<ReplyData> filteredRepliesData = ReplyHelper.ApplySecondaryFilter(allReplies, hideUseless, currentUserIdForFiltering);

            // 找到目标楼层在过滤后数据中的索引
            int idx = filteredRepliesData.FindIndex(r => r.OriginalFloor == targetOriginalFloor);
            if (idx == -1)
            {
                return -1;
            }
            int page = (idx / (int)this.pageSize) + 1;
            return page;
        }

        /// <summary>
        /// 初始化 showclass 方法所需的分页、排序及用户过滤相关的参数。
        /// </summary>
        /// <param name="mainUserIdRequest">输出从请求中获取的 mainuserid。</param>
        /// <param name="isFilteringByUserRequest">输出是否按用户进行过滤的布尔值。</param>
        /// <returns>返回排序方式字符串 ("ASC" 或 "DESC")。</returns>
        private string InitializeShowClassParameters(out string mainUserIdRequest, out bool isFilteringByUserRequest)
        {
            mainUserIdRequest = base.GetRequestValue("mainuserid");
            // this.ot (排序触发器) 应该已经在 Page_Load 中被赋值并可作为成员变量 this.ot 访问
            string sortOrder = (this.ot == "1") ? "ASC" : "DESC";
            isFilteringByUserRequest = KeLin.ClassManager.Tool.WapTool.IsNumeric(mainUserIdRequest) && long.TryParse(mainUserIdRequest, out _);

            if (base.classVo.ismodel < 1L)
            {
                this.pageSize = Convert.ToInt32(base.siteVo.MaxPerPage_Default);
            }
            else
            {
                this.pageSize = Convert.ToInt32(base.classVo.ismodel);
            }
            if (this.pageSize <= 0) this.pageSize = 10;

            string pageStr = base.GetRequestValue("page");
            if (!long.TryParse(pageStr, out this.CurrentPage) || this.CurrentPage < 1)
            {
                this.CurrentPage = 1;
            }
            // CurrentPage < 1 的检查已包含在上一行

            return sortOrder;
        }

        /// <summary>
        /// 处理置顶回帖的逻辑，包括获取、从普通列表移除并在首页添加。
        /// </summary>
        /// <param name="bookIdParsed">当前帖子的ID。</param>
        /// <param name="sortOrder">排序方式。</param>
        /// <param name="isFilteringByUser">是否正在按用户筛选回帖。</param>
        /// <param name="replyRepository">ReplyRepository 实例。</param>
        private void ProcessTopRepliesLogic(long bookIdParsed, string sortOrder, bool isFilteringByUser, ReplyRepository replyRepository)
        {
            List<long> topReplyIds = new List<long>();
            // this.listVoTop 是成员变量，这里会直接修改它
            this.listVoTop = new List<wap_bbsre_Model>();

            if (!isFilteringByUser)
            {
                this.listVoTop = ReplyHelper.GetTopReplies(base.siteid, bookIdParsed, this.a, sortOrder);
                if (this.listVoTop != null && this.listVoTop.Any())
                {
                    topReplyIds = this.listVoTop.Select(tr => tr.id).ToList();
                    // 为置顶回帖获取并填充 OriginalFloor 到 Map (this.ReplyIdToOriginalFloorMap 是成员变量)
                    if (topReplyIds.Any())
                    {
                        Dictionary<long, int> topOriginalFloors = replyRepository.GetOriginalFloorsByIds(base.siteid, bookIdParsed, topReplyIds);
                        foreach (var kvp in topOriginalFloors)
                        {
                            if (!this.ReplyIdToOriginalFloorMap.ContainsKey(kvp.Key))
                            {
                                this.ReplyIdToOriginalFloorMap.Add(kvp.Key, kvp.Value);
                            }
                        }
                    }
                }
            }

            // 所有页都移除普通回帖中的置顶回帖 (this.listVo 是成员变量)
            if (this.listVo != null && topReplyIds.Any()) // 添加 this.listVo null 检查
            {
                var topIdsSet = new HashSet<long>(topReplyIds);
                this.listVo = this.listVo.Where(r => !topIdsSet.Contains(r.id)).ToList();
            }

            // 只有第一页插入置顶回帖 (this.listVo 是成员变量)
            if (this.CurrentPage == 1 && this.listVoTop != null && this.listVoTop.Any())
            {
                if (this.listVo == null) this.listVo = new List<wap_bbsre_Model>(); // 确保 listVo 已初始化
                this.listVo.InsertRange(0, this.listVoTop);
            }
        }

        public void ShowClass()
        {
            string sortOrder = InitializeShowClassParameters(out string localMainUserId, out bool localIsFilteringByUser);
            this.mainuserid = localMainUserId; // 将获取到的 mainuserid 赋给成员变量，如果后续仍需作为成员变量使用
            // bool isFilteringByUser = localIsFilteringByUser; // 如果后续需要用这个布尔值，可以这样接收

            // 初始化映射字典
            this.ReplyIdToOriginalFloorMap = new Dictionary<long, int>();

            try
            {
                var replyRepository = new ReplyRepository(this.a);
                long bookIdParsed = long.Parse(this.id);

                // 修复类型转换错误
                long currentUserIdParsed = 0;
                if (base.userid != "0" && long.TryParse(base.userid, out var tempUserId))
                {
                    currentUserIdParsed = tempUserId;
                }

                // 使用ReplyHelper判断是否需要过滤
                bool hideUseless = ReplyHelper.ShouldApplySqlFilter(base.Request, this.bookVo);

                // === 获取、过滤和分页回复 ===
                List<ReplyData> finalRepliesToShow = FetchAndFilterReplies(bookIdParsed, base.siteid, sortOrder, this.mainuserid, currentUserIdParsed, hideUseless, this.pageSize, replyRepository);

                // === 处理和准备用于显示的回复数据 ===
                ProcessAndPrepareReplyData(finalRepliesToShow, bookIdParsed, sortOrder, localIsFilteringByUser, replyRepository);

                // 生成分页链接
                GeneratePaginationHtml(localIsFilteringByUser);

                this.index = (this.CurrentPage - 1) * this.pageSize;
                this.kk = 1L;
            }
            catch
            {
                this.ERROR = "显示回帖列表时出错。";
                if (base.GetRequestValue("ajax") != "1")
                {
                    this.ShowClass(); // 注意：这里可能导致递归调用，如果 FetchAndFilterReplies 内部也可能抛错并被这里捕获
                }
            }
        }

        /// <summary>
        /// 处理和准备用于显示的回复数据，包括楼层映射、模型转换、置顶处理和用户信息加载。
        /// </summary>
        private void ProcessAndPrepareReplyData(List<ReplyData> repliesToShow, long bookId, string currentSortOrder, bool isFilteringByUser, ReplyRepository currentReplyRepository)
        {
            // 为当前页的普通回帖填充 OriginalFloor 到 Map
            PopulateFloorMapForPageReplies(repliesToShow);

            // 将 ReplyData 转换为 wap_bbsre_Model 并设置到 this.listVo
            ConvertAndSetReplyListModels(repliesToShow);

            // 处理置顶回帖逻辑
            // ProcessTopRepliesLogic 会修改 this.listVo 和 this.ReplyIdToOriginalFloorMap
            ProcessTopRepliesLogic(bookId, currentSortOrder, isFilteringByUser, currentReplyRepository);

            // 加载辅助性的回复数据，如用户信息和已删除楼层的映射
            LoadAuxiliaryReplyData(bookId, currentSortOrder);
        }

        /// <summary>
        /// 将 ReplyData 列表转换为 wap_bbsre_Model 列表，并设置到成员变量 this.listVo。
        /// </summary>
        /// <param name="repliesData">要转换的 ReplyData 列表。</param>
        private void ConvertAndSetReplyListModels(List<ReplyData> repliesData)
        {
            this.listVo = ReplyHelper.ConvertToBbsReModels(repliesData);
        }

        /// <summary>
        /// 为当前页面上显示的普通回帖（非置顶）填充其ID到原始楼层号的映射。
        /// </summary>
        /// <param name="pageReplies">当前页面上显示的回复数据列表。</param>
        private void PopulateFloorMapForPageReplies(List<ReplyData> pageReplies)
        {
            foreach (var replyData in pageReplies)
            {
                if (!this.ReplyIdToOriginalFloorMap.ContainsKey(replyData.Id))
                {
                    this.ReplyIdToOriginalFloorMap.Add(replyData.Id, replyData.OriginalFloor); // 注意：这里直接访问并修改成员变量 this.ReplyIdToOriginalFloorMap
                }
            }
        }

        /// <summary>
        /// 生成分页控件的HTML。
        /// </summary>
        /// <param name="isFilteringByUserRequest">是否按用户筛选回帖。</param>
        private void GeneratePaginationHtml(bool isFilteringByUserRequest)
        {
            string pageUrlParams = "book_re.aspx?action=class&siteid=" + base.siteid + "&classid=" + base.classid + "&id=" + this.id + "&ot=" + this.ot;
            if (isFilteringByUserRequest)
            {
                pageUrlParams += "&mainuserid=" + this.mainuserid;
            }

            string pageStyle = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 40);
            string baseUrlForPaging = pageUrlParams.Replace("&amp;", "&");
            this.linkURL = KeLin.ClassManager.Tool.WapTool.GetPageLink(base.ver, base.lang, Convert.ToInt32(this.total), this.pageSize, this.CurrentPage, baseUrlForPaging, pageStyle);
        }

        private List<ReplyData> FetchAndFilterReplies(long bookIdParsed, string siteid, string sortOrder, string mainuserid, long currentUserIdParsed, bool hideUseless, long pageSizeParam, ReplyRepository replyRepository)
        {
            List<ReplyData> allReplies = replyRepository.GetRepliesPaged(
                bookIdParsed,
                siteid,
                1,
                99999, // 拉取所有
                sortOrder,
                mainuserid,
                false, // 不再用SQL内容过滤
                currentUserIdParsed,
                true // includeTopReplies: true，确保包含置顶楼层
            );

            // C#层精确过滤
            List<ReplyData> filteredRepliesData = ReplyHelper.ApplySecondaryFilter(allReplies, hideUseless, currentUserIdParsed);

            // 计算总数和分页
            this.total = filteredRepliesData.Count;
            long totalPages = (this.total + pageSizeParam - 1) / pageSizeParam;
            if (totalPages == 0) totalPages = 1;

            string pageStr = base.GetRequestValue("page");
            if (!long.TryParse(pageStr, out this.CurrentPage) || this.CurrentPage < 1 || this.CurrentPage > totalPages)
            {
                this.CurrentPage = 1;
            }

            // 确保 CurrentPage 在有效范围内，如果 totalPages 为 0 (即没有回复)，CurrentPage 也应为 1
            if (this.CurrentPage < 1) this.CurrentPage = 1;
            if (this.CurrentPage > totalPages && totalPages > 0) this.CurrentPage = totalPages;


            // 分页
            int skipCount = (int)((this.CurrentPage - 1) * pageSizeParam);
            return filteredRepliesData.Skip(skipCount).Take((int)pageSizeParam).ToList();
        }

        /// <summary>
        /// 处理 PostBack 的回复请求
        /// </summary>
        public void Add()
        {
            ReplyResult replyResult = ProcessAddReplyLogic();

            // 根据结果设置 INFO 或 ERROR，用于页面显示
            this.INFO = replyResult.Status; // 直接使用状态码
            if (replyResult.Status != "OK" && !string.IsNullOrEmpty(replyResult.ErrorMessage))
            {
                // 可以考虑将错误信息也设置到某个公共变量，以便在页面显示
                // this.ERROR = replyResult.ErrorMessage; // 或者修改 ShowTipInfo 调用
            }
            if (replyResult.Status == "ERROR")
            {
                // this.ERROR 已经在 ProcessAddReplyLogic 中被赋值
            }

            // 对于 PostBack 请求，执行完逻辑后需要刷新页面显示
            this.ShowClass();
        }

        // --- 新增：处理 AJAX 回复请求的方法 ---
        private void HandleAjaxAddReply()
        {
            ReplyResult replyResult = ProcessAddReplyLogic();
            string tipHtml;

            // 根据 ProcessAddReplyLogic 返回的状态构造提示 HTML
            switch (replyResult.Status)
            {
                case "OK":
                    tipHtml = string.Format("<div class=\"tip\">{0}获得{1}:{2}，获得经验:{3}</div>",
                                            replyResult.ErrorMessage,
                                            base.siteVo.sitemoneyname, // 使用站点货币名称
                                            replyResult.EarnedMoney,
                                            replyResult.EarnedExperience);
                    break;
                case "NULL": // 内容过短
                case "TITLEMAX": // 内容过长
                case "REPEAT": // 重复内容
                case "WAITING": // 操作频繁
                case "MAX": // 达到上限
                case "LOCK": // 用户锁定
                case "ERROR_Secret": // 口令错误
                case "NOMONEY": // 金币不足
                case "CLOSED": // 回帖关闭
                case "NEED_LOGIN": // 需要登录
                case "PWERROR": // 密码错误
                case "REG_TIME_LIMIT": // 注册时间不足
                    // 对于各种已知的错误状态，直接使用 ProcessAddReplyLogic 返回的 ErrorMessage
                    tipHtml = string.Format("<div class=\"tip\">{0}</div>", replyResult.ErrorMessage);
                    break;
                case "ERROR": // 内部错误
                    // 给用户一个通用提示
                    tipHtml = string.Format("<div class=\"tip\">{0}</div>", replyResult.ErrorMessage);
                    break;
                default: // 其他未知帖子状态（理论上已被 LoadAndValidateBookVoForAjax 捕获）
                    tipHtml = string.Format("<div class=\"tip\">{0}</div>", replyResult.ErrorMessage ?? "无法回复此帖子。");
                    break;
            }

            // 发送响应
            SendAjaxResponse(tipHtml);
        }
        // --- 结束：处理 AJAX 回复请求的方法 ---

        /// <summary>
        /// 执行回帖前的各种前置条件检查（如权限、状态等）。
        /// </summary>
        /// <param name="errorCode">如果检查失败，返回错误码。</param>
        /// <param name="errorMessage">如果检查失败，返回错误消息。</param>
        /// <returns>true 如果所有检查通过，否则 false。</returns>
        private bool PerformPreReplyChecks(out string errorCode, out string errorMessage)
        {
            errorCode = null;
            errorMessage = null;

            // 1. 检查栏目是否关闭回帖
            if ("1".Equals(KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 3)))
            {
                errorCode = "CLOSED";
                errorMessage = "回帖功能已关闭";
                return false;
            }

            // 2. 匿名回帖检查及是否需要登录
            string allowAnonymous = KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 12);
            if (this.KL_BBSRE_Anonymous_Open != "1") // 全局匿名开关关闭时，强制不允许匿名
            {
                allowAnonymous = "0";
            }
            if (!"1".Equals(allowAnonymous) && base.userid == "0") // 如果不允许匿名，则必须登录
            {
                errorCode = "NEED_LOGIN";
                errorMessage = "请先登录后再操作";
                return false;
            }

            // 3. 回帖密码检查 (如果需要)
            // 注意: this.needpwFlag 已在 Page_Load 中初始化
            if (this.needpwFlag == "1")
            {
                // 如果需要密码，则不允许匿名 (更安全，也简化了逻辑，因为匿名用户无法提供密码进行验证)
                if (base.userid == "0")
                {
                    errorCode = "NEED_LOGIN";
                    errorMessage = "此操作需要登录并通过密码验证"; // 稍作修改，更明确
                    return false;
                }
                // 已登录用户检查其密码
                string needpw = base.GetRequestValue("needpw"); // 获取提交的密码
                if (string.IsNullOrEmpty(needpw) || PubConstant.md5(needpw).ToLower() != base.userVo.password.ToLower())
                {
                    errorCode = "PWERROR";
                    errorMessage = "管理密码错误";
                    return false;
                }
            }

            // 4. 注册时间限制检查
            string regTimeLimitMinutesStr = KeLin.ClassManager.Tool.WapTool.GetSiteDefault(base.siteVo.Version, 15);
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(regTimeLimitMinutesStr))
            {
                regTimeLimitMinutesStr = "0";
            }
            long regTimeLimitMinutesParsed = Convert.ToInt64(regTimeLimitMinutesStr);
            if (regTimeLimitMinutesParsed > 0L && base.userid != "0") // 仅对登录用户检查
            {
                long minutesSinceRegistration = KeLin.ClassManager.Tool.WapTool.DateDiff(DateTime.Now, base.userVo.RegTime, "mi");
                if (minutesSinceRegistration < regTimeLimitMinutesParsed)
                {
                    errorCode = "REG_TIME_LIMIT";
                    errorMessage = "您的注册时间小于" + regTimeLimitMinutesParsed + "分钟，请稍后再试！";
                    return false;
                }
            }

            return true; // 所有前置检查通过
        }

        /// <summary>
        /// 对回复提交进行核心业务规则校验。
        /// </summary>
        /// <param name="text">回复的内容。</param>
        /// <param name="errorCode">如果校验失败，返回错误码。</param>
        /// <param name="errorMessage">如果校验失败，返回错误消息。</param>
        /// <returns>true 如果所有校验通过，否则 false。</returns>
        private bool ValidateReplySubmission(string text, out string errorCode, out string errorMessage)
        {
            errorCode = null;
            errorMessage = null;

            // 1. 内容长度校验 (最少)
            if (text.Trim().Length < long.Parse(this.contentmax))
            {
                errorCode = "NULL";
                errorMessage = "回复内容最少" + this.contentmax + "字";
                return false;
            }
            // 2. 内容长度校验 (最多)
            if (this.content_max != "0" && text.Trim().Length > long.Parse(this.content_max))
            {
                errorCode = "TITLEMAX"; // 沿用旧代码的状态码
                errorMessage = "回复内容最多" + this.content_max + "字";
                return false;
            }
            // 3. 重复内容校验
            if (text.Equals(this.Session["content"]))
            {
                errorCode = "REPEAT";
                errorMessage = "请不要发重复内容";
                return false;
            }
            // 4. 操作频率校验
            if (base.isCheckIPTime(long.Parse(this.KL_CheckIPTime)))
            {
                errorCode = "WAITING";
                errorMessage = "请再过" + this.KL_CheckIPTime + "秒后操作";
                return false;
            }
            // 5. 回帖上限校验
            if (this.KL_CheckBBSreCount != "0" && !KeLin.ClassManager.Tool.WapTool.CheckUserBBSCount(base.siteid, base.userid, this.KL_CheckBBSreCount, "bbsre"))
            {
                errorCode = "MAX";
                errorMessage = "今天已达回帖上限 " + this.KL_CheckBBSreCount + " 条";
                return false;
            }
            // 6. 用户是否被锁定校验
            if (KeLin.ClassManager.Tool.WapTool.isLockuser(base.siteid, base.userid, base.classid) > -1L)
            {
                errorCode = "LOCK";
                errorMessage = "您已被禁止发言";
                return false;
            }
            // 7. 版块口令校验
            // Note: this.isNeedSecret is initialized in Page_Load
            if (this.isNeedSecret && base.GetRequestValue("secret") != base.classVo.topicID)
            {
                errorCode = "ERROR_Secret";
                errorMessage = "版块口令错误";
                return false;
            }
            // 8. 金币是否足够校验 (用于扣费情况)
            // Note: this.getmoney is initialized by InitializeReplySettings()
            if (this.getmoney.StartsWith("-") && base.userid != "0" && base.userVo.money + long.Parse(this.getmoney) < 0L)
            {
                errorCode = "NOMONEY";
                errorMessage = "您的" + base.siteVo.sitemoneyname + "不足，无法回复";
                return false;
            }

            return true; // 所有核心校验通过
        }

        /// <summary>
        /// 执行实际的回帖操作、数据库更新、发送通知等。
        /// </summary>
        /// <param name="text">回复内容。</param>
        /// <param name="faceValue">选择的表情值。</param>
        /// <param name="replyToOriginalFloorIdStr">回复的目标楼层/帖子ID字符串。</param>
        /// <param name="result">用于填充操作结果的 ReplyResult 对象。</param>
        private void ExecuteReplyPostingAndNotifications(string text, string faceValue, string replyToOriginalFloorIdStr, ReplyResult result)
        {
            // 首先，计算新回复的准确原始楼层号，用于消息通知链接
            wap_bbsre_BLL tempReplyBllForCount = new wap_bbsre_BLL(this.a);
            long countPreviousReplies = tempReplyBllForCount.GetListCount("bookid=" + long.Parse(this.id) + " AND devid=" + base.siteid);
            long newOriginalFloorForLink = countPreviousReplies + 1;

            this.Session["content"] = text; // 防止重复提交
            faceValue = faceValue.Replace("表情", "");
            if (faceValue.Trim().Length > 3 && faceValue.EndsWith(".gif", StringComparison.OrdinalIgnoreCase))
            {
                text = "[img]face/" + faceValue + "[/img]" + text;
            }

            long currentEarnedMoney; // 本次回帖实际获得的金币 (移除冗余初始化)
            long currentEarnedExp = long.Parse(this.getexpr); // 基础经验 (可能被栏目设置覆盖)
            long moneyChangeBase = long.Parse(this.getmoney); // 基础金币奖励或扣费

            // 计算打赏回帖奖励 (reShow)
            long rewardFromReshow = CalculateReshowReward();

            // 计算抢楼/踩楼奖励 (freeMoney)
            long rewardFromFreeMoney = CalculateFreeMoneyReward();

            // 构建 wap_bbsre_Model 对象
            string finalContent = text; // text 变量此时已包含表情处理
            long parsedUserId = long.Parse(base.userid); // 可能为 0
            long parsedClassId = long.Parse(base.classid);
            long parsedBookId = long.Parse(this.id);
            long parsedReplyToFloorId = long.Parse(replyToOriginalFloorIdStr);

            wap_bbsre_Model wap_bbsre_Model = BuildReplyModel(finalContent, parsedUserId, base.nickname, parsedClassId, parsedBookId, parsedReplyToFloorId, rewardFromFreeMoney, base.siteid, base.siteVo.isCheck);

            // 保存回帖
            new wap_bbsre_BLL(this.a).Add(wap_bbsre_Model);

            // 更新主贴信息
            UpdateMainPostAfterReply(parsedBookId, rewardFromFreeMoney);

            // --- 消息通知逻辑 ---
            string notificationContentBody = PrepareNotificationContent(text, newOriginalFloorForLink.ToString());

            // 决定是否通知帖子作者
            bool shouldNotifyAuthor = false;
            // 条件1: 栏目设置非"首帖回复不通知作者" (classVo.smallimg[9] != "1") 
            // 并且 这是帖子的第一个有效回复 (this.bookVo.book_re == 0L, 注意: book_re此时是执行Add之前的数量)
            // 并且 回复者不是作者本人
            if ((KeLin.ClassManager.Tool.WapTool.getArryString(base.classVo.smallimg, '|', 9) != "1" && this.bookVo.book_re == 0L && base.userid != "0" && base.userid != this.bookVo.book_pub.ToString()) ||
                // 条件2: 用户勾选了"通知楼主" (this.sendmsg == "1")
                // 并且 回复者不是作者本人
                ("1".Equals(this.sendmsg) && base.userid != "0" && base.userid != this.bookVo.book_pub.ToString()))
            {
                shouldNotifyAuthor = true;
            }

            if (shouldNotifyAuthor)
            {
                string authorNotificationTitle = (base.nickname + "回复了你的帖子").Replace("'", "''");
                SendNotificationMessage(this.bookVo.book_pub.ToString(), authorNotificationTitle, notificationContentBody);
            }

            // 决定是否通知被回复楼层的作者
            // repliedUserId 来自 this.fromuserid，已经在Page_Load中通过GetRequestValue获取，可能为空或"0"
            string repliedUserId = this.fromuserid;
            if ("1".Equals(this.sendmsg2) && KeLin.ClassManager.Tool.WapTool.IsNumeric(repliedUserId) && repliedUserId != "0" && base.userid != repliedUserId)
            {
                string repliedUserTitle = (base.nickname + " 回复了你的回复").Replace("'", "''");
                SendNotificationMessage(repliedUserId, repliedUserTitle, notificationContentBody);
            }
            // --- 结束消息通知逻辑 ---

            currentEarnedMoney = moneyChangeBase + rewardFromReshow + rewardFromFreeMoney;

            // 更新用户统计数据和日志
            UpdateUserStatsAndLogAfterReply(base.userid, base.userVo, base.nickname, currentEarnedMoney, currentEarnedExp, this.id, base.siteid);

            KeLin.ClassManager.Tool.WapTool.ClearDataBBSRe("bbsRe" + base.siteid + this.id);

            // --- 记录访问日志 (移动到这里，确保AJAX和PostBack都能记录) ---
            base.VisiteCount("回复了帖子:<a href=\"" + base.http_start + "bbs/book_view.aspx?siteid=" + base.siteid + "&amp;classid=" + base.classid + "&amp;id=" + this.id + "\">" + KeLin.ClassManager.Tool.WapTool.GetShowImg(this.bookVo.book_title, "200", "bbs") + "</a>");

            result.Status = "OK";
            result.EarnedMoney = currentEarnedMoney;
            result.EarnedExperience = currentEarnedExp;
            result.ErrorMessage = "回复成功！";
        }

        /// <summary>
        /// 更新用户统计数据（金币、经验、回帖数）并记录相关日志。
        /// </summary>
        /// <param name="userIdString">用户ID字符串。</param>
        /// <param name="currentUserVo">当前登录用户的 user_Model 对象。</param>
        /// <param name="nickname">用户昵称。</param>
        /// <param name="earnedMoney">获得的金币。</param>
        /// <param name="earnedExperience">获得的经验。</param>
        /// <param name="postIdString">帖子ID字符串（用于日志）。</param>
        /// <param name="siteId">站点ID。</param>
        private void UpdateUserStatsAndLogAfterReply(string userIdString, user_Model currentUserVo, string nickname, long earnedMoney, long earnedExperience, string postIdString, string siteId)
        {
            if (userIdString != "0" && currentUserVo != null) // 确保用户已登录且 currentUserVo有效
            {
                base.MainBll.UpdateSQL(string.Format("update [user] set [money]=([money]+{0}),expR=expR+{1},bbsReCount=bbsReCount+1 where siteid={2} and userid={3}", earnedMoney, earnedExperience, siteId, currentUserVo.userid));
                base.SaveBankLog(userIdString, "论坛回帖", earnedMoney.ToString(), userIdString, nickname, "回复帖子[" + postIdString + "]");
                base.Action_user_doit(2);
            }
        }

        /// <summary>
        /// 更新主帖的相关信息（如回复数、最后回复时间、剩余悬赏额）。
        /// </summary>
        /// <param name="bookId">主帖ID。</param>
        /// <param name="rewardFromFreeMoney">本次回复获得的抢楼/悬赏金额。</param>
        private void UpdateMainPostAfterReply(long bookId, long rewardFromFreeMoney)
        {
            string updateBbsSql = "update [wap_bbs] set book_re=book_re+1";
            // 仅当帖子不是沉帖模式 (isdown != 2) 且 当前排序不是强制按回复时间升序/降序时，才更新 reDate
            // orderby == "0" (默认排序，按 reDate 降序), orderby == "3" (按发帖时间升序), orderby == "4" (按发帖时间降序)
            // 当 orderby == "1" (按回复时间升序) 或 orderby == "2" (按回复时间降序) 时，reDate 的更新可能由其他逻辑控制或不应随意更改以维持排序准确性。
            // 然而，原始逻辑是 !(this.orderby == "1") && !(this.orderby == "2")，意味着只要不是这两种排序就更新。
            if (this.bookVo.isdown != 2L && !(this.orderby == "1") && !(this.orderby == "2"))
            {
                updateBbsSql += string.Format(", reDate='{0}'", DateTime.Now);
            }
            if (rewardFromFreeMoney > 0)
            {
                updateBbsSql += string.Format(", freeleftmoney=CASE WHEN freeleftmoney >= {0} THEN freeleftmoney - {0} ELSE 0 END", rewardFromFreeMoney);
            }
            updateBbsSql += " where id=" + bookId;
            base.MainBll.UpdateSQL(updateBbsSql);
        }

        /// <summary>
        /// 计算打赏回帖奖励 (reShow)
        /// </summary>
        /// <returns>打赏奖励金额</returns>
        private long CalculateReshowReward()
        {
            if (this.bookVo.reShow > 0L && base.userid != "0" && !KeLin.ClassManager.Tool.WapTool.isHasReplyToday(base.siteid, base.userid, this.id))
            {
                return this.bookVo.reShow;
            }
            return 0L;
        }

        /// <summary>
        /// 计算抢楼/踩楼奖励 (freeMoney)
        /// </summary>
        /// <returns>抢楼/踩楼奖励金额</returns>
        private long CalculateFreeMoneyReward()
        {
            long reward = 0L;
            if (this.bookVo.freeMoney > 0L && base.userid != "0")
            {
                string[] freeRuleParts = this.bookVo.freeRule.Split('_');
                if (freeRuleParts.Length >= 2)
                {
                    string[] floors = freeRuleParts[0].Split('|');
                    string[] moneys = freeRuleParts[1].Split('|');
                    // long nextFloorNumber = this.bookVo.book_re + 1L; // 下一个回帖是第几楼 (实际楼层在这里可能还未完全确定，如果依赖精确的 book_re)
                    // 注意：原始逻辑中 nextFloorNumber 是基于 this.bookVo.book_re + 1L 计算的。
                    // 然而，book_re 是在保存回帖 *之后* 才更新的。 
                    // 如果 freeRule 依赖的是 "即将成为的楼层号"，那么这个计算需要更精确，可能需要查询数据库中当前实际的回复数。
                    // 但为了保持与原逻辑一致（它在保存回帖之前就计算了这个值），我们暂时沿用 this.bookVo.book_re + 1L。
                    // 更好的做法可能是在调用此方法前，先获得一个准确的"下一个楼层号"。
                    // 但考虑到 ExecuteReplyPostingAndNotifications 开头已经计算了 newOriginalFloorForLink，或许可以用那个值。
                    // 不过 newOriginalFloorForLink 是原始楼层号，而 book_re 是有效回复数。
                    // 暂时保持和原逻辑一样的计算方式，但这是一个潜在的需要审视的点。
                    long nextFloorNumber = this.bookVo.book_re + 1L;

                    if (floors.Length == 1) // 固定奖励模式（不管是否为"*"）
                    {
                        if (this.bookVo.freeLeftMoney > 0L && !KeLin.ClassManager.Tool.WapTool.isHasReplyToday(base.siteid, base.userid, this.id) && moneys.Length > 0)
                        {
                            if (long.TryParse(moneys[0], out long fixedReward))
                            {
                                if (this.bookVo.freeLeftMoney > fixedReward)
                                {
                                    reward = fixedReward;
                                }
                                else
                                {
                                    reward = this.bookVo.freeLeftMoney;
                                }
                            }
                        }
                    }
                    else // 特定楼层奖励模式
                    {
                        for (int i = 0; i < floors.Length && i < moneys.Length; i++)
                        {
                            if (long.TryParse(floors[i], out long targetFloor) && nextFloorNumber == targetFloor && long.TryParse(moneys[i], out long floorReward))
                            {
                                reward = floorReward;
                                break;
                            }
                        }
                    }
                }
            }
            return reward;
        }

        /// <summary>
        /// 构建 wap_bbsre_Model 对象用于保存新回复。
        /// </summary>
        /// <param name="processedText">处理后的回复内容（已包含表情）。</param>
        /// <param name="userId">回复用户ID。</param>
        /// <param name="nickname">回复用户昵称。</param>
        /// <param name="classId">栏目ID。</param>
        /// <param name="bookId">主帖ID。</param>
        /// <param name="replyToFloorId">被回复的楼层/帖子ID。</param>
        /// <param name="freeMoneyReward">抢楼/踩楼奖励金额。</param>
        /// <param name="siteId">站点ID。</param>
        /// <param name="siteCheckStatus">站点默认审核状态。</param>
        /// <returns>构建好的 wap_bbsre_Model 实例。</returns>
        private wap_bbsre_Model BuildReplyModel(string processedText, long userId, string nickname, long classId, long bookId, long replyToFloorId, long freeMoneyReward, string siteId, long siteCheckStatus)
        {
            return new wap_bbsre_Model
            {
                devid = siteId,
                userid = userId, // 可能为 0
                nickname = nickname, // 匿名时可能为空
                classid = classId,
                bookid = bookId,
                content = processedText,
                redate = DateTime.Now,
                ischeck = siteCheckStatus, // 站点默认审核状态
                reply = replyToFloorId, // 回复的楼层 ID
                myGetMoney = freeMoneyReward // 初始化抢楼/特殊楼层奖励 (从参数获取)
            };
        }

        /// <summary>
        /// 核心回复逻辑处理方法
        /// </summary>
        /// <returns>包含处理结果的 ReplyResult 对象</returns>
        private ReplyResult ProcessAddReplyLogic()
        {
            InitializeReplySettings();

            // GetRequestValue for content, face, reply are done after pre-checks, 
            // as they are not strictly needed for these initial guard clauses.
            // However, needpw is fetched inside PerformPreReplyChecks.

            var result = new ReplyResult { Status = "UNKNOWN", EarnedMoney = 0, EarnedExperience = 0 };

            try
            {
                if (this.bookVo.islock == 2L)
                {
                    result.Status = "ENDED";
                    result.ErrorMessage = "此帖已结束，无法回复！";
                    return result;
                }

                // 执行前置条件检查
                if (!PerformPreReplyChecks(out string preCheckErrorCode, out string preCheckErrorMessage))
                {
                    result.Status = preCheckErrorCode;
                    result.ErrorMessage = preCheckErrorMessage;
                    return result;
                }

                // 获取核心参数 (content, face, reply_to_floor_id)
                // 这些参数主要用于后续的业务逻辑和数据构建
                string text = base.GetRequestValue("content");
                string faceValue = base.GetRequestValue("face");
                string replyToOriginalFloorIdStr = base.GetRequestValue("reply"); // 这是指回复的目标帖子的ID，在旧代码中用 text2
                if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(replyToOriginalFloorIdStr))
                {
                    replyToOriginalFloorIdStr = "0";
                }

                // --- 主要校验逻辑 ---
                if (!ValidateReplySubmission(text, out string validationErrorCode, out string validationErrorMessage))
                {
                    result.Status = validationErrorCode;
                    result.ErrorMessage = validationErrorMessage;

                    // --- 记录访问日志 (操作频繁的情况) ---
                    if (validationErrorCode == "WAITING")
                    {
                        base.VisiteCount("回复了帖子。但过于频繁。");
                    }

                    return result;
                }
                else
                {
                    // --- 执行回帖逻辑 --- (与原 add 方法一致)
                    ExecuteReplyPostingAndNotifications(text, faceValue, replyToOriginalFloorIdStr, result);
                }

                return result;
            }
            catch
            {
                // 记录错误但不影响正常流程
                result.Status = "ERROR";
                result.ErrorMessage = "处理回帖时发生内部错误。"; // 不暴露具体异常给前端
                this.ERROR = "处理回帖时发生内部错误。"; // 保留给 PostBack 使用
                return result;
            }
        }

        /// <summary>
        /// 输出新版回帖UI设置到前端JavaScript变量
        /// </summary>
        public string GetNewReplyUIJsVar()
        {
            return string.Format("<script type=\"text/javascript\">var serverSideNewReplyUIEnabled = {0};</script>", this.IsNewReplyUIEnabled.ToString().ToLower());
        }

        /// <summary>
        /// 加载并处理已删除的回复
        /// </summary>
        /// <param name="bookId">主帖ID</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="sortOrder">排序方式</param>
        /// <remarks>
        /// 此方法的主要目的：
        /// 1. 获取当前帖子下的已删除回复(isCheck=2/3)信息
        /// 2. 将这些已删除回复的ID和楼层号信息存入ReplyIdToOriginalFloorMap映射表
        /// 3. 确保页面可以正确处理对已删除回复的引用链接
        /// 4. 虽然已删除回复不会在列表中显示，但其楼层号信息必须保留以保证楼层引用的稳定性
        /// 
        /// 注意：此方法不会修改listVo（正常显示的回复列表），只更新映射信息
        /// </remarks>
        private void LoadAndProcessDeletedReplies(long bookId, string siteId, string sortOrder)
        {
            try
            {
                // 使用ReplyRepository来获取所有回复，然后过滤已删除的回复
                var replyRepository = new ReplyRepository(this.a);

                // 获取所有回复
                List<ReplyData> allReplies = replyRepository.GetRepliesPaged(
                    bookId,
                    siteId,
                    pageNumber: 1,
                    pageSize: 9999, // 足够大的数字
                    sortOrder: sortOrder,
                    mainUserId: null,
                    enableSqlFilter: false,
                    currentUserId: null,
                    includeTopReplies: true // 确保包含置顶楼层
                );

                // 过滤已删除的回复
                List<ReplyData> deletedReplies = allReplies.Where(r => r.CheckStatus == 2 || r.CheckStatus == 3).ToList();

                if (deletedReplies != null && deletedReplies.Any())
                {
                    // 更新楼层号映射
                    foreach (var reply in deletedReplies)
                    {
                        if (!this.ReplyIdToOriginalFloorMap.ContainsKey(reply.Id))
                        {
                            this.ReplyIdToOriginalFloorMap.Add(reply.Id, reply.OriginalFloor);
                        }
                    }
                }
            }
            catch
            {
                // 记录错误但不影响正常流程
            }
        }

        /// <summary>
        /// 加载与当前回复列表相关的辅助数据，例如用户信息和已删除回复的楼层映射。
        /// </summary>
        /// <param name="bookId">当前帖子的ID。</param>
        /// <param name="currentSortOrder">当前的排序方式。</param>
        private void LoadAuxiliaryReplyData(long bookId, string currentSortOrder)
        {
            if (this.listVo != null && this.listVo.Any())
            {
                this.userListVo_IDName = ReplyHelper.GetUserInfoForReplies(this.listVo, base.MainBll);

                // 加载包括已删除回复在内的完整数据到映射表
                // LoadAndProcessDeletedReplies 只更新 this.ReplyIdToOriginalFloorMap
                LoadAndProcessDeletedReplies(bookId, base.siteid, currentSortOrder);
            }
        }

        /// <summary>
        /// 准备用于回复通知的消息主体内容。
        /// </summary>
        /// <param name="originalReplyText">原始回复文本。</param>
        /// <param name="newOriginalFloorForLink">新回复的原始楼层号，用于生成查看链接。</param>
        /// <returns>格式化并为SQL安全处理后的消息主体字符串。</returns>
        private string PrepareNotificationContent(string originalReplyText, string newOriginalFloorForLink)
        {
            StringBuilder contentBuilder = new StringBuilder();
            // 使用原始的 text (未被表情处理过的) 来截取，与原逻辑一致
            contentBuilder.AppendFormat("回复时间：{0}<br/>回复内容：{1} <br/>", DateTime.Now, KeLin.ClassManager.Tool.WapTool.left(originalReplyText, 200).Replace("'", "''"));
            // 新增：两个链接
            string postId = this.bookVo != null ? this.bookVo.id.ToString() : this.id;
            string classId = this.bookVo != null ? this.bookVo.book_classid.ToString() : this.classid;
            string staticUrl = $"/bbs-{postId}.html";
            string replyUrl = string.Format("{0}bbs/book_re.aspx?siteid={1}&amp;classid={2}&amp;id={3}&amp;tofloor={4}&amp;fromuserid={5}",
                base.http_start, base.siteid, classId, postId, newOriginalFloorForLink, base.userid);
            contentBuilder.AppendFormat("<a href=\"{0}\">查看帖子主题</a> | <a href=\"{1}\">查看完整回复</a>", staticUrl, replyUrl);
            return contentBuilder.ToString();
        }

        /// <summary>
        /// 发送单条通知消息。
        /// </summary>
        /// <param name="toUserId">接收用户ID。</param>
        /// <param name="title">消息标题 (应已进行SQL转义)。</param>
        /// <param name="formattedMessageBody">已格式化并部分转义的消息主体。</param>
        private void SendNotificationMessage(string toUserId, string title, string formattedMessageBody)
        {
            // 基本的有效性检查 和 防止自己给自己发通知
            if (!KeLin.ClassManager.Tool.WapTool.IsNumeric(toUserId) || toUserId == "0" || base.userid == "0" || base.userid == toUserId)
                return;

            // 黑名单检查
            if (UserBlockingService.IsBlackUser(KelinkWAP_Check, toUserId, base.userid))
                return;

            // 确保整个消息体对于SQL是安全的 (尽管内部组件可能已经转义，这里再做一次以防万一)
            string safeFormattedMessageBody = formattedMessageBody.Replace("'", "''");
            string safeTitle = title.Replace("'", "''"); // title 也可能包含 '
            string safeNickname = base.nickname.Replace("'", "''");


            string sql = string.Format("insert into wap_message(siteid,userid,nickname,title,content,touserid,issystem)values({0},{1},N'{2}',N'{3}',N'{4}',{5},1)",
                base.siteid, base.userid, safeNickname, safeTitle, safeFormattedMessageBody, toUserId);
            base.MainBll.UpdateSQL(sql);
        }
    }
}