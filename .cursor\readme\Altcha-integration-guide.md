---
description: 
globs: 
alwaysApply: false
---
# ALTCHA 验证码集成指南：ASP.NET Web Forms 实践

本文档总结了在 ASP.NET Web Forms 项目中集成 ALTCHA 验证码作为 Cloudflare Turnstile 替代方案的经验、遇到的主要问题及其解决方案。

## 1. 集成目标

在 `WapLogin.aspx` 登录页面引入 ALTCHA 验证码机制，以增强安全性，并替代原有的 Cloudflare Turnstile 服务。

## 2. 核心实现架构

### 2.1. 后端服务 (Node.js)

-   使用 Node.js 和官方 `altcha-lib` 库搭建一个独立的 ALTCHA 服务。
-   该服务负责处理两个核心功能：
    -   **挑战生成**: 提供一个 API 端点 (例如 `/altcha-challenge`)，用于生成 ALTCHA 挑战数据。
    -   **挑战验证**: 提供一个 API 端点 (例如 `/verify-altcha`)，用于接收客户端提交的 `altcha_payload` 并进行验证。

### 2.2. ASP.NET 代理 (AltchaProxy.ashx)

-   为了避免直接将 Node.js 服务暴露给公网，并解决潜在的跨域问题，创建了一个 ASP.NET 一般处理程序 (`.ashx`) 作为代理。
-   `AltchaProxy.ashx` 负责接收前端对挑战数据的请求，然后将其转发给内部的 Node.js ALTCHA 服务，并将 Node.js 服务的响应返回给前端。
-   **注意**: 后续实践表明，如果 Node.js 服务本身已配置好 CORS (Cross-Origin Resource Sharing)，并且可以直接被客户端访问（例如通过反向代理统一域名），则此 `AltchaProxy.ashx` 可能不是必需的。前端可以直接请求 Node.js 服务的挑战接口。但在本项目中，最初采用了此代理。

### 2.3. 前端集成 (WapLogin.aspx)

-   在 `WapLogin.aspx` 页面中引入 ALTCHA 的 JavaScript 小部件 (`altcha.min.js`) 和相关的 CSS。
-   使用 `<altcha-widget>` HTML 元素嵌入验证码。关键属性配置包括：
    -   `challengeurl`: 指向获取挑战的 URL (最初指向 `AltchaProxy.ashx`，后可考虑直接指向 Node.js 服务端点)。
    -   `name`: **极其重要**，必须设置为后端期望接收的表单字段名，本项目统一为 `altcha_payload`。
    -   `language`: 设置为 `zh-cn` 以支持中文。
    -   `auto='onload'`: 页面加载时自动开始计算（如果适用）。
    -   `hidelogo`, `hidefooter`: 可选，用于隐藏 ALTCHA 的 logo 和页脚。
    -   `debug='false'`: 生产环境应关闭调试模式。

### 2.4. 后端验证逻辑 (WapLogin.aspx.cs)

-   在 `WapLogin.aspx.cs` 的 `Page_Load` 事件中，当 `action == "login"` 时执行验证。
-   从请求中获取 `altcha_payload`：`string altchaPayload = GetRequestValue("altcha_payload");`
-   将此 `altchaPayload` 通过 HTTP POST 请求发送到 Node.js ALTCHA 服务的验证端点。
-   解析 Node.js 服务的 JSON 响应，检查 `success` 字段以确定验证是否通过。

## 3. 遇到的主要问题与解决方案

### 3.1. 移动端浏览器 Web Crypto API 的 HTTPS 要求

-   **问题**: 在部分移动端浏览器（如 iOS Safari）上，ALTCHA 小部件可能无法正常工作或报错，提示 Web Crypto API 不可用。
-   **原因**: Web Crypto API 出于安全考虑，在非 HTTPS 环境下可能会受到限制或不可用。
-   **解决方案**: 确保 ALTCHA 服务和承载小部件的页面都通过 HTTPS 提供服务。在本地开发和测试时，可以使用 `localtunnel` 或类似工具创建临时的 HTTPS 隧道。

### 3.2. 移动端性能问题与优化

-   **问题**: ALTCHA 验证码的计算过程在某些移动设备上可能非常缓慢，导致用户体验不佳。
-   **解决方案**:
    1.  **移除 `workers` 限制**: `<altcha-widget>` 的 `workers` 属性如果设置为 `'1'`，会限制其仅使用一个 CPU 核心进行计算。移除此属性或确保其不为 `'1'`，可以让小部件利用设备的所有可用核心，加快计算速度。
    2.  **降低 `maxnumber` 参数**: ALTCHA 服务端配置中的 `maxnumber` 参数（或通过 `challengeurl` 传递给小部件的挑战中的 `maxnumber`）决定了计算的复杂度。默认值（如1,000,000）可能对移动端过高。将其显著降低（例如至 50,000 - 100,000 甚至更低，需根据目标设备性能权衡）可以大幅提升移动端验证速度。

### 3.3. 表单提交字段名不匹配

-   **问题**: 后端代码 `WapLogin.aspx.cs` 最初可能使用 `GetRequestValue("altcha")` 来获取验证载荷，而前端 `<altcha-widget>` 默认或被配置为提交名为 `altcha_payload` 的隐藏字段。
-   **原因**: 前后端期望的表单字段名称不一致。
-   **解决方案**: 统一字段名称。最稳妥的做法是：
    -   在 `<altcha-widget>` 上明确设置 `name="altcha_payload"`。
    -   在后端 `WapLogin.aspx.cs` 中使用 `GetRequestValue("altcha_payload")` 获取数据。

### 3.4. 登录逻辑错误导致"假成功"

-   **问题**: 即便验证码错误或账号密码不正确，页面依然提示登录成功。
-   **原因**: 在 `WapLogin.aspx.cs` 的 `Page_Load` 方法的末尾，存在一行代码 `INFO = "OK";`，它会无条件地将登录状态设置为成功，覆盖了之前所有验证失败的逻辑。
-   **解决方案**: 移除 `Page_Load` 方法末尾的 `INFO = "OK";`。确保 `INFO = "OK";` 仅在所有验证（包括人机验证和用户凭据验证）均成功通过后，在 `checkUser()` 方法内部的相应位置设置。

### 3.5. CSS 对齐与样式调整

-   **问题**: ALTCHA 小部件的复选框和文本在特定设备或浏览器（尤其是移动端 Safari）上可能出现对齐问题，未与登录表单的其他元素（如用户名、密码输入框前的图标）完美对齐。
-   **解决方案**:
    -   仔细检查并调整 `Altcha-custom.css`（或项目自定义的 ALTCHA 样式文件）。
    -   针对性地修改 `.altcha-main` 的 `padding` 和 `.altcha-checkbox` 的 `margin-left`。
    -   可能需要使用更具体的 CSS 选择器（如结合父容器的 ID 或类，例如 `#login .loginform .pad .altcha-main .altcha-checkbox`）来覆盖小部件的默认样式或确保样式优先级。
    -   充分利用浏览器的开发者工具进行实时调试和样式调整。

## 4. 关键配置与注意事项

-   **`web.config` 配置**:
    -   `ALTCHAEnabled`: (string "1" 或 "0") 控制是否启用 ALTCHA。
    -   `ALTCHAServiceUrl`: (string) Node.js ALTCHA 服务的基础 URL (e.g., `http://localhost:3000`)。
    -   `ALTCHAChallengePath`: (string) 挑战获取端点路径 (e.g., `/altcha-challenge`)。
    -   `ALTCHAVerifyPath`: (string) 挑战验证端点路径 (e.g., `/verify-altcha`)。
-   **Node.js 服务**: 必须正确配置并运行，能够处理挑战生成和验证请求。
-   **CORS**: 如果前端直接请求 Node.js 服务，确保 Node.js 服务已正确配置 CORS 策略以允许来自 ASP.NET 应用域的请求。
-   **安全性**: Node.js 服务本身应考虑安全措施，如速率限制，防止被滥用。
-   **错误处理**: 前后端都需要有健壮的错误处理机制，例如当 ALTCHA 服务不可用或返回非预期响应时，应向用户提供清晰的提示。
-   **调试**: 在 `<altcha-widget>` 上临时启用 `debug='true'` 有助于排查前端问题。完成后务必在生产环境设为 `false`。

## 5. 最终成果

通过上述步骤和问题排查，成功在 `WapLogin.aspx` 页面集成了 ALTCHA 验证码。该实现方案在桌面和移动设备上均能良好运行，提供了有效的安全防护，同时通过参数调整保证了用户体验。所有调试相关的代码和不必要的页面状态显示均已移除，形成了整洁的生产环境实现。

