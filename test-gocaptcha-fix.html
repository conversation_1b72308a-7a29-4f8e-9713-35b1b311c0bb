<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoCaptcha 容器修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        
        .test-btn {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        #gocaptcha-wrap {
            margin: 15px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 4px;
            text-align: center;
            color: #666;
            min-height: 50px;
            display: none;
        }
        
        #gocaptcha-wrap.show-turnstile {
            display: block;
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>GoCaptcha 容器修复测试</h1>
    
    <div class="test-section">
        <h2>测试场景 1：正常情况（容器存在）</h2>
        <div class="form-container">
            <form name="login">
                <div class="form-group">
                    <label for="username1">用户名</label>
                    <input type="text" id="username1" name="logname" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label for="password1">密码</label>
                    <input type="password" id="password1" name="logpass" placeholder="请输入密码">
                </div>
                
                <div id="gocaptcha-wrap">
                    GoCaptcha 容器已存在
                </div>
                <input type="hidden" id="gocaptcha-token" name="gocaptchaToken" value="">
                
                <button type="button" class="test-btn" onclick="testSwitchToTurnstile()">切换到 Turnstile</button>
                <button type="button" class="test-btn" onclick="testContainerCheck()">检查容器状态</button>
                <button type="submit" class="btn">登录</button>
            </form>
        </div>
        <div id="status1" class="status info">等待测试...</div>
    </div>
    
    <div class="test-section">
        <h2>测试场景 2：容器缺失情况</h2>
        <div class="form-container">
            <form name="login2">
                <div class="form-group">
                    <label for="username2">用户名</label>
                    <input type="text" id="username2" name="logname" placeholder="手机或ID号">
                </div>
                <div class="form-group">
                    <label for="password2">密码</label>
                    <input type="password" id="password2" name="logpass" placeholder="请输入密码">
                </div>
                
                <!-- 故意不包含 gocaptcha-wrap 容器 -->
                <input type="hidden" id="gocaptcha-token2" name="gocaptchaToken" value="">
                
                <button type="button" class="test-btn" onclick="testMissingContainer()">测试缺失容器</button>
                <button type="button" class="test-btn" onclick="createMissingContainer()">创建缺失容器</button>
                <button type="submit" class="btn">登录</button>
            </form>
        </div>
        <div id="status2" class="status info">等待测试...</div>
    </div>

    <script>
        // 模拟 GoCaptcha 初始化逻辑
        function testSwitchToTurnstile() {
            const originalWrap = document.getElementById('gocaptcha-wrap');
            const status = document.getElementById('status1');
            
            if (!originalWrap) {
                status.className = 'status error';
                status.textContent = '错误：备用验证码容器丢失，请刷新页面重试';
                return;
            }
            
            // 模拟切换到 Turnstile
            originalWrap.innerHTML = '';
            originalWrap.style.display = 'block';
            originalWrap.classList.add('show-turnstile');
            
            const turnstileDiv = document.createElement('div');
            turnstileDiv.className = 'cf-turnstile';
            turnstileDiv.innerHTML = '🔒 Cloudflare Turnstile 验证码<br><small>(备用方式，内联显示)</small>';
            
            originalWrap.appendChild(turnstileDiv);
            
            status.className = 'status success';
            status.textContent = '成功：已切换到 Turnstile 验证码，显示在原位置';
        }
        
        function testContainerCheck() {
            const originalWrap = document.getElementById('gocaptcha-wrap');
            const status = document.getElementById('status1');
            
            status.className = 'status info';
            status.innerHTML = `
                容器检查结果：<br>
                - 容器存在: ${!!originalWrap}<br>
                - 容器ID: ${originalWrap ? originalWrap.id : 'N/A'}<br>
                - 显示状态: ${originalWrap ? originalWrap.style.display : 'N/A'}<br>
                - CSS类: ${originalWrap ? originalWrap.className : 'N/A'}<br>
                - 内容: ${originalWrap ? originalWrap.innerHTML.substring(0, 50) + '...' : 'N/A'}
            `;
        }
        
        function testMissingContainer() {
            const originalWrap = document.getElementById('gocaptcha-wrap');
            const status = document.getElementById('status2');
            
            if (!originalWrap) {
                status.className = 'status error';
                status.textContent = '确认：gocaptcha-wrap 容器确实不存在';
            } else {
                status.className = 'status info';
                status.textContent = '意外：gocaptcha-wrap 容器存在（可能是全局的）';
            }
        }
        
        function createMissingContainer() {
            const form = document.forms['login2'];
            const tokenInput = document.getElementById('gocaptcha-token2');
            const status = document.getElementById('status2');
            
            if (!form || !tokenInput) {
                status.className = 'status error';
                status.textContent = '错误：无法找到表单或token输入框';
                return;
            }
            
            // 检查是否已经存在容器
            let existingWrap = form.querySelector('#gocaptcha-wrap');
            if (existingWrap) {
                status.className = 'status info';
                status.textContent = '容器已存在，无需创建';
                return;
            }
            
            // 创建新容器
            const newWrap = document.createElement('div');
            newWrap.id = 'gocaptcha-wrap';
            newWrap.style.cssText = 'width: 100%; margin: 0 auto; display: none;';
            newWrap.innerHTML = '新创建的 GoCaptcha 容器';
            
            // 插入到 token 输入框之前
            if (tokenInput.parentNode) {
                tokenInput.parentNode.insertBefore(newWrap, tokenInput);
                
                // 测试切换到 Turnstile
                newWrap.innerHTML = '';
                newWrap.style.display = 'block';
                newWrap.classList.add('show-turnstile');
                
                const turnstileDiv = document.createElement('div');
                turnstileDiv.className = 'cf-turnstile';
                turnstileDiv.innerHTML = '🔒 新创建容器中的 Turnstile 验证码';
                
                newWrap.appendChild(turnstileDiv);
                
                status.className = 'status success';
                status.textContent = '成功：已创建新容器并切换到 Turnstile 验证码';
            } else {
                status.className = 'status error';
                status.textContent = '错误：无法找到合适的插入位置';
            }
        }
        
        // 模拟表单提交拦截
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.forms;
            for (let i = 0; i < forms.length; i++) {
                forms[i].addEventListener('submit', function(event) {
                    event.preventDefault();
                    alert('表单提交被拦截（测试模式）');
                });
            }
        });
    </script>
</body>
</html>
