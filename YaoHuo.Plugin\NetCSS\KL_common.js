var xmlhttp = null; var KL_now_currpage = 0; var KL_page_total = 0; var KL_scoll_downEnd = 0; function KL_show_next(KL_total, KL_pagesize, KL_currpage, tourl, pagetype) {
    KL_page_total = parseInt(KL_total); var pagesize = parseInt(KL_pagesize); var currpage = parseInt(KL_currpage); if (KL_now_currpage == 0) { KL_now_currpage = currpage + 1; } else { KL_now_currpage = KL_now_currpage + 1; }
    document.getElementById('KL_show_loadimg').innerHTML = '<span id="loadimg"></span>'; document.getElementById('KL_show_tip').innerHTML = '正在加载(' + KL_now_currpage + '/' + KL_page_total + ')'; if ((KL_now_currpage) > KL_page_total) { document.getElementById('KL_show_loadimg').innerHTML = ''; document.getElementById('KL_show_tip').innerHTML = '没有更多了'; } else { tourl = tourl + '&' + pagetype + '=' + KL_now_currpage; LoadXML_YiBu(tourl); }
}
function LoadXML_YiBu(tourl) {
    try {
        if (window.XMLHttpRequest) { xmlhttp = new XMLHttpRequest(); if (xmlhttp.overrideMimeType) { xmlhttp.overrideMimeType("text/xml"); } } else if (window.ActiveXObject) { var activexName = ["MSXML2.XMLHTTP", "Microsoft.XMLHTTP", ""]; for (var i = 0; i < activexName.length; i++) { try { xmlhttp = new ActiveXObject(activexName[i]); break; } catch (e) { } } }
        xmlhttp.onreadystatechange = KL_CallBack; xmlhttp.open("GET", tourl, true); xmlhttp.send(null);
    } catch (e) { document.getElementById('KL_show_loadimg').innerHTML = '<span id="loadimg"></span>'; document.getElementById('KL_show_tip').innerHTML = '加载出错'; }
}

function KL_CallBack() {
    if (xmlhttp == null) {
        return;
    }
    if (xmlhttp.readyState == 4) {
        if (xmlhttp.status == 200) {
            var responseText = xmlhttp.responseText;
            var st = responseText.indexOf("<!--listS-->");
            var et = responseText.indexOf("<!--listE-->");
            if (st < 0 || et < 0) {
                document.getElementById('KL_show_loadimg').innerHTML = '';
                document.getElementById('KL_show_tip').innerHTML = '没有更多了';
            } else {
                responseText = responseText.substring(st + 12, et);
                document.getElementById('KL_show_next_list').style.display = 'block';
                document.getElementById('KL_show_next_list').innerHTML += responseText;
                if ((KL_now_currpage) > KL_page_total) {
                    document.getElementById('KL_show_loadimg').innerHTML = '';
                    document.getElementById('KL_show_tip').innerHTML = '没有更多了';

                    // 手动更新布局状态为新版
                    isCustomLayoutEnabled = true;
                    localStorage.setItem('customLayoutEnabled', isCustomLayoutEnabled);

                    // 更新切换按钮的文本
                    updateChangeLayoutButtonText();
                } else {
                    document.getElementById('KL_show_loadimg').innerHTML = '';
                    document.getElementById('KL_show_tip').innerHTML = '加载更多(' + (KL_now_currpage) + '/' + KL_page_total + ')';

                    // 仅当新版布局开启时才重新应用新布局
                    if (isCustomLayoutEnabled) {
                        // 重新应用新布局
                        applyNewLayoutToNewContent();
                    }
                }
            }
        }
    }
}

// 新的函数：重新应用新布局到新加载的内容
function applyNewLayoutToNewContent() {
    const newElements = document.querySelectorAll('.list-reply');
    newElements.forEach((newElement) => {
        const data = extractData(newElement);
        const newLayout = buildNewLayout(data);
        replaceContent(newElement, newLayout);

        // 调用SuperLink.js中的文本处理函数
        const textContentElements = newLayout.querySelectorAll(".bbscontent, .bubble, .retext");
        textContentElements.forEach((element) => {
            processTextContent(element);
        });
    });
}

// 在加载更多后调用此函数
function onMoreContentLoaded() {
    // 在加载新内容时手动为其添加hover的CSS规则
    applyHoverEffectToNewContent();
    // 还可以重新触发旧内容的CSS规则
    document.querySelectorAll('.dropdown').forEach(function (dropDownElem) {
        dropDownElem.classList.remove('hover-effect'); // 先移除可能已有的class
        void dropDownElem.offsetWidth; // 强制浏览器重新计算样式
        dropDownElem.classList.add('hover-effect'); // 重新添加class
    });
    // 仅当新版布局启用时，重新调用 applyNewLayoutToNewContent
    if (isCustomLayoutEnabled) {
        applyNewLayoutToNewContent();
    }
    // 重新调用 replyAny
    replyAny();
}

// ========== 通用异步加载器 (UAL) START ==========
var UAL_xmlhttp = null;
var UAL_currentPage = 0;
var UAL_totalPages = 0;
var UAL_config = {};

/**
 * 初始化通用异步加载器。
 * @param {object} options - 配置选项。
 * 必须包含的选项:
 *   triggerSelector: 触发加载更多内容的元素选择器。
 *   contentContainerSelector: 新内容将被追加到的DOM元素选择器。
 *   statusTextSelector: 显示加载状态文本的元素选择器。
 *   baseUrl: 获取更多内容的基础URL。
 *   itemsPerPage: 每页加载的项目数。
 *   totalItems: 总项目数，用于计算总页数。
 * 可选选项:
 *   initialPage: 当前已加载的页码 (从1开始)。默认为0，表示第一次加载将请求第1页。
 *   pageParamName: 分页参数名 (默认为 "page")。
 *   contentStartMarker: 内容提取的开始标记 (默认为 "<!--listS-->")。
 *   contentEndMarker: 内容提取的结束标记 (默认为 "<!--listE-->")。
 *   loadingIndicatorSelector: 加载图片/spinner区域的选择器 (可选)。
 *   loadingImageHtml: 加载指示器的HTML (默认为 '<span id="loadimg"></span>')。
 *   noMoreText: 没有更多内容时显示的文本 (默认为 "没有更多了")。
 *   errorText: 加载出错时显示的文本 (默认为 "加载出错")。
 *   statusTextFormatter: Function(pageToLoad, totalPages, state) 格式化状态文本。State 包括: 'initial', 'loading', 'loaded', 'noMore', 'error'。
 *   onContentLoaded: Callback function(newRawHtml, tempDivWithNewContent) 在内容被解析后、追加到DOM前执行。
 *   onSuccess: Callback function() 内容成功追加到DOM后执行。
 *   onError: Callback function(error) XHR出错时执行。
 *   extraParams: Object 包含要添加到URL的额外查询参数。
 */
function UAL_init(options) {
    UAL_config = {
        initialPage: 0, // 假设初始页码为 0，点击后请求第 1 页。
        pageParamName: "page",
        contentStartMarker: "<!--listS-->",
        contentEndMarker: "<!--listE-->",
        loadingImageHtml: '<span id="loadimg"></span>',
        noMoreText: "没有更多了",
        errorText: "加载出错",
        ...options
    };

    UAL_currentPage = UAL_config.initialPage;
    UAL_totalPages = Math.ceil(UAL_config.totalItems / UAL_config.itemsPerPage);
    if (UAL_totalPages === 0 && UAL_config.totalItems > 0) UAL_totalPages = 1; // 确保总项目大于0时总页数至少为1
    if (UAL_config.totalItems <= 0) UAL_totalPages = 0; // 总项目数为0时总页数为0

    const triggerElement = document.querySelector(UAL_config.triggerSelector);
    const statusTextElement = document.querySelector(UAL_config.statusTextSelector);

    if (triggerElement && statusTextElement) {
        if (UAL_currentPage >= UAL_totalPages && UAL_totalPages > 0) { // 没有更多页可加载或已加载完毕 (总页数大于0)
            statusTextElement.innerHTML = UAL_config.noMoreText;
            triggerElement.onclick = null;
            if (UAL_config.loadingIndicatorSelector) {
                const indicator = document.querySelector(UAL_config.loadingIndicatorSelector);
                if (indicator) indicator.innerHTML = "";
            }
        } else if (UAL_totalPages === 0) { // 没有项目
            statusTextElement.innerHTML = UAL_config.noMoreText;
            triggerElement.onclick = null;
        } else { // 还有页面可以加载
            let initialStatusText;
            if (UAL_config.statusTextFormatter) {
                initialStatusText = UAL_config.statusTextFormatter(UAL_currentPage + 1, UAL_totalPages, 'initial');
            } else {
                initialStatusText = `加载更多(${UAL_currentPage + 1}/${UAL_totalPages})`;
            }
            statusTextElement.innerHTML = initialStatusText;
            triggerElement.onclick = function () {
                UAL_loadNextPage();
                return false; // 阻止默认的链接跳转
            };
        }
    } else {
        // 触发或状态元素未找到的错误提示
        if (!triggerElement) console.error("UAL 错误: 未找到触发元素选择器:", UAL_config.triggerSelector);
        if (!statusTextElement) console.error("UAL 错误: 未找到状态文本元素选择器:", UAL_config.statusTextSelector);
    }
}

/**
 * 加载下一页内容。
 */
function UAL_loadNextPage() {
    let pageToLoad = UAL_currentPage + 1; // 计算将要加载的页码

    if (pageToLoad > UAL_totalPages && UAL_totalPages > 0) { // 已经加载到最后一页之后
        const loadingIndicator = UAL_config.loadingIndicatorSelector ? document.querySelector(UAL_config.loadingIndicatorSelector) : null;
        const statusTextElement = UAL_config.statusTextSelector ? document.querySelector(UAL_config.statusTextSelector) : null;
        if (loadingIndicator) loadingIndicator.innerHTML = "";
        if (statusTextElement) statusTextElement.innerHTML = UAL_config.noMoreText;
        const trigger = document.querySelector(UAL_config.triggerSelector);
        if (trigger) trigger.onclick = null;
        return; // 停止加载
    }

    const loadingIndicator = UAL_config.loadingIndicatorSelector ? document.querySelector(UAL_config.loadingIndicatorSelector) : null;
    const statusTextElement = UAL_config.statusTextSelector ? document.querySelector(UAL_config.statusTextSelector) : null;

    // 显示加载指示器和状态文本
    if (loadingIndicator) loadingIndicator.innerHTML = UAL_config.loadingImageHtml;
    if (statusTextElement) {
        let loadingText = (UAL_config.statusTextFormatter && UAL_config.statusTextFormatter(pageToLoad, UAL_totalPages, 'loading')) ||
            `正在加载(${pageToLoad}/${UAL_totalPages})`;
        statusTextElement.innerHTML = loadingText;
    }

    // 构建请求URL
    let requestUrl = UAL_config.baseUrl;
    const pageParamSeparator = requestUrl.includes("?") ? "&" : "?";
    requestUrl += pageParamSeparator + UAL_config.pageParamName + "=" + pageToLoad;

    // 添加额外参数
    if (UAL_config.extraParams) {
        for (const key in UAL_config.extraParams) {
            if (UAL_config.extraParams.hasOwnProperty(key)) {
                requestUrl += "&" + key + "=" + encodeURIComponent(UAL_config.extraParams[key]);
            }
        }
    }

    // 发送异步请求
    UAL_sendRequest(requestUrl);
}

/**
 * 发送异步请求加载内容。
 * @param {string} url - 请求的URL。
 */
function UAL_sendRequest(url) {
    try {
        if (window.XMLHttpRequest) {
            UAL_xmlhttp = new XMLHttpRequest();
            if (UAL_xmlhttp.overrideMimeType) UAL_xmlhttp.overrideMimeType("text/html");
        } else if (window.ActiveXObject) {
            var activexName = ["MSXML2.XMLHTTP", "Microsoft.XMLHTTP"];
            for (var i = 0; i < activexName.length; i++) {
                try { UAL_xmlhttp = new ActiveXObject(activexName[i]); break; } catch (e) { }
            }
        }
        if (!UAL_xmlhttp) {
            throw new Error("无法创建 XHR 对象。");
        }

        UAL_xmlhttp.onreadystatechange = UAL_requestCallback;
        UAL_xmlhttp.open("GET", url, true);
        UAL_xmlhttp.send(null);
    } catch (e) {
        console.error("UAL 发送请求错误:", e.message, "URL:", url);
        const loadingIndicator = UAL_config.loadingIndicatorSelector ? document.querySelector(UAL_config.loadingIndicatorSelector) : null;
        const statusTextElement = UAL_config.statusTextSelector ? document.querySelector(UAL_config.statusTextSelector) : null;
        if (loadingIndicator) loadingIndicator.innerHTML = "";
        if (statusTextElement) statusTextElement.innerHTML = UAL_config.errorText + (e.message ? ": " + e.message : "");
        if (UAL_config.onError) UAL_config.onError(e);
    }
}

/**
 * 处理异步请求响应。
 */
function UAL_requestCallback() {
    if (!UAL_xmlhttp || UAL_xmlhttp.readyState !== 4) return;

    const loadingIndicator = UAL_config.loadingIndicatorSelector ? document.querySelector(UAL_config.loadingIndicatorSelector) : null;
    const statusTextElement = UAL_config.statusTextSelector ? document.querySelector(UAL_config.statusTextSelector) : null;

    if (loadingIndicator) loadingIndicator.innerHTML = ""; // 请求完成后清除加载指示器

    if (UAL_xmlhttp.status === 200) {
        var responseText = UAL_xmlhttp.responseText;
        var st = responseText.indexOf(UAL_config.contentStartMarker);
        var et = responseText.indexOf(UAL_config.contentEndMarker);
        var newRawHtml = "";

        if (st !== -1 && et !== -1 && st < et) {
            newRawHtml = responseText.substring(st + UAL_config.contentStartMarker.length, et);
        }

        const contentContainer = document.querySelector(UAL_config.contentContainerSelector);
        var tempDiv = document.createElement('div');
        tempDiv.innerHTML = newRawHtml;

        // 检查是否有实际内容被解析出来
        var hasActualContent = Array.from(tempDiv.childNodes).some(node =>
            node.nodeType === Node.ELEMENT_NODE ||
            (node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== "")
        );

        if (hasActualContent && contentContainer) {
            // 在追加内容前执行回调
            if (UAL_config.onContentLoaded) {
                UAL_config.onContentLoaded(newRawHtml, tempDiv);
            }
            // 将新内容追加到容器
            while (tempDiv.firstChild) {
                contentContainer.appendChild(tempDiv.firstChild);
            }
            UAL_currentPage++; // 成功加载一页后，当前页码加一
            // 内容成功追加后执行回调
            if (UAL_config.onSuccess) UAL_config.onSuccess();
        } else {
            // 没有实际内容或容器未找到，假设没有更多内容了（如果我们本来期望有内容的话）
            if (UAL_currentPage < UAL_totalPages) { // 如果我们期望加载更多但没有内容返回
                UAL_currentPage = UAL_totalPages; // 强制设置为总页数，表示已到末尾
            }
        }

        // 更新UI状态文本
        if (UAL_currentPage >= UAL_totalPages && UAL_totalPages > 0) { // 已加载到总页数或超出 (总页数大于0)
            if (statusTextElement) statusTextElement.innerHTML = UAL_config.noMoreText;
            const trigger = document.querySelector(UAL_config.triggerSelector);
            if (trigger) trigger.onclick = null; // 禁用点击
        } else if (UAL_totalPages === 0) { // 没有项目
            if (statusTextElement) statusTextElement.innerHTML = UAL_config.noMoreText;
            const trigger = document.querySelector(UAL_config.triggerSelector);
            if (trigger) trigger.onclick = null; // 禁用点击
        }
        else { // 还有更多页面可以加载
            if (statusTextElement) {
                let statusMsg = (UAL_config.statusTextFormatter && UAL_config.statusTextFormatter(UAL_currentPage + 1, UAL_totalPages, 'loaded')) ||
                    `加载更多(${UAL_currentPage + 1}/${UAL_totalPages})`;
                statusTextElement.innerHTML = statusMsg;
            }
        }
    } else { // HTTP 状态非 200
        console.error("UAL XHR 错误. 状态:", UAL_xmlhttp.status, "URL:", UAL_xmlhttp.responseURL);
        if (statusTextElement) statusTextElement.innerHTML = UAL_config.errorText + " (HTTP " + UAL_xmlhttp.status + ")";
        if (UAL_config.onError) UAL_config.onError(new Error("HTTP 状态 " + UAL_xmlhttp.status));
    }
    UAL_xmlhttp = null; // 清理XHR对象
}
// ========== 通用异步加载器 (UAL) END ==========