﻿using KeLin.ClassManager;
using System;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class FriendList_del : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string string_11 = "";

		public string friendtype = "";

		public string id = "";

		public string backurl = "";

		public string INFO = "";

		public string page = "";

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			backurl = base.Request.QueryString.Get("backurl");
			id = base.Request.QueryString.Get("id");
			page = base.Request.QueryString.Get("page");
			friendtype = base.Request.QueryString.Get("friendtype");
			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			switch (action)
			{
				case "godelall":
					godelall();
					break;
				case "godel":
					godel();
					break;
			}
		}

		public void godel()
		{
			MainBll.UpdateSQL("delete  from wap_friends where siteid=" + long.Parse(siteid) + " and userid=" + long.Parse(userid) + " and id=" + long.Parse(id));
			INFO = "OK";
		}

		public void godelall()
		{
			MainBll.UpdateSQL("delete  from wap_friends where siteid=" + long.Parse(siteid) + " and userid=" + long.Parse(userid) + " and friendtype=" + long.Parse(friendtype));
			INFO = "OK";
			INFO = "OK";
		}
	}
}