```markdown
# Cursor Rules 核心技巧与应用指南

Cursor Rules 是 Cursor 编辑器中一项至关重要的功能，可以理解为**大模型的系统提示词 (System Prompt)**。它允许用户预设文本，在每次新开聊天上下文时自动置于头部，作为 AI 理解和响应的背景与约束。掌握 Rules 是区分 Cursor 普通用户和高阶玩家的关键，尤其对于 AI 编程更是不可或缺。

## 一、什么是 Cursor Rules？

*   **定义**：用户预设的文本，作为 AI 模型在特定聊天上下文中的行为准则和背景信息。
*   **作用**：引导或约束模型的输出，例如设定回复语言、代码风格、遵循特定规范等。
*   **重要性**：提升 AI 协作效率和准确性的核心工具。

## 二、全局规则与项目级规则

1.  **全局规则 (Global Rules)**
    *   **位置**：Cursor 编辑器右上角设置 -> 左侧 Rules -> 右侧 User Rules。
    *   **作用**：对所有项目生效。
    *   **示例**：设置 `always respond in 中文`，则所有 AI 回复都将是中文。
    *   **建议**：尽量减少全局规则，一般只设置回复语言等最通用的指令，以避免与项目规则冲突或产生不可控情况。

2.  **项目级规则 (Project Rules)**
    *   **位置**：
        *   早期：项目根目录下的 `.cursorrules` 文件。
        *   v0.46+：项目根目录下的 `.cursor/rules/` 文件夹内，支持多个 `.mdc` 后缀的规则文件。
    *   **创建**：可通过设置面板 "Add new rule" 或直接在 `rules` 文件夹下创建 `.mdc` 文件。
    *   **`.mdc` 文件**：基于 Markdown 格式，头部包含可视化操作的元信息（通过3个字段控制，也可切换为源码显示）。

    *   **项目规则的四种类型及优先级**：
        1.  **Always**：
            *   作用：项目内的全局规则，只要在该项目下提问就会遵循。
            *   优先级：**低于**全局规则。
        2.  **Auto Attached**：
            *   作用：当问答中引用的文件匹配右侧设置的正则表达式时，此规则生效。
            *   优先级：**低于**全局规则。
        3.  **Agent Requested**：
            *   作用：AI 根据用户问题和规则的 `description`（描述，必填）智能判断是否使用此规则。描述需精准概括规则内容。
            *   优先级：**低于**全局规则。
        4.  **Manual** (默认类型)：
            *   作用：仅当用户在输入框通过 `@Cursor Rules` 指令手动选择此规则时生效。
            *   优先级：**高于**全局规则（当被手动选中时）。

    *   **注意**：如果 Manual 类型的规则没有描述或自动匹配选项，设置面板会提示它永远不会被使用（除非手动选择）。

## 三、编写与管理规则

1.  **`.mdc` 文件头部信息**：
    *   Cursor 为 `.mdc` 文件的头部元信息做了可视化 UI，用于设置规则类型、正则、描述等。
    *   可以通过修改用户 JSON 设置（`CMD/Ctrl+Shift+P` -> `Open User Settings (JSON)`）来显示其源码形式。

2.  **AI 生成规则 (Generate Cursor Rules)**：
    *   通过聊天框 `/` 指令 -> `Generate Cursor Rules`。
    *   目前仍存在 BUG（如头部信息填充不全、文件反复创建删除等），生成的规则内容尚可，但自动配置能力有待提高。
    *   建议：生成后手动停止，再自行调整。

3.  **编写高质量 Rules 的结构建议**：
    *   **使用场景 (When to use this rule)**：明确规则的适用条件。
    *   **关键规则 (Key rules / Dos and Don'ts)**：清晰列出应该做什么，绝对不要做什么。
    *   **示例 (Examples)**：提供正反示例，帮助 AI 理解。
    *   **命名与组织**：可以制定规则文件的命名规范、存放位置等。

## 四、Rules 的高级玩法：构建工作流

1.  **从需求到产品的工作流示例**：
    *   **需求文档 (PRD)**：编写初步想法，让 AI 辅助完善。
    *   **原型界面**：基于 PRD，让 AI 生成原型。
    *   **技术方案/架构/数据库设计文档**：同理，AI 辅助生成与完善。
    *   **为每种文档创建 Rules**：约束生成格式、内容模板等。

2.  **构建工作流 Rule**：
    *   创建一个更宏观的 Rule，用于指导一系列任务的执行流程。
    *   例如：一个 Rule 规定当基于 PRD 执行任务时，自动拆解需求为子任务、创建待办清单、依次执行、标记完成、记录变更等。

3.  **逐步构建个性化工作流**：
    *   随着 Rules 增多，自然会形成针对特定任务的细化流程，这就是工作流。
    *   类似 V0, Bolt, Manus, 扣子空间等 L3级 AI 产品的内部机制。
    *   **不建议直接套用通用型工作流**：每个人的需求、技术栈、项目规模不同，通用工作流难以完全适配，甚至可能成为负担。
    *   **最佳方式**：
        *   先有使用 Rules 约束流程的意识。
        *   逐步编写和积累自己的 Rules。
        *   参考优秀通用工作流的思路和写法，汲取精华，融入自己的体系。
        *   这个过程不仅能提升 AI 编程能力，还能深化对 Agent 和 Workflow 概念的理解。
```