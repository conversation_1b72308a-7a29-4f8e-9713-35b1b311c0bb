# FriendList 页面 UI 现代化改造需求

## 1. 任务概述

对 `YaoHuo.Plugin` 项目中的 `FriendList.aspx` 页面进行 UI 现代化改造，使用 Handlebars.NET 替换旧的 ASP.NET Web Forms UI，并实现新旧 UI 可切换。

## 2. 背景与上下文

* 项目是基于 ASP.NET Web Forms 和 .NET Framework 4.8，使用 C# 7+。
* Handlebars.NET 集成已通过重设计的 `TemplateService.cs` 实现，并成功适配了包括 `MyFile.aspx`、`EditProfile.aspx`、`ModifyPW.aspx`、`ModifyHead.aspx` 在内的多个页面。
* CSS 文件集中管理在 `YaoHuo.Plugin/Template/CSS/` 目录下。
* 数据模型类应定义在 `YaoHuo.Plugin/Template/Models/` 目录下。
* 新的 UI 渲染遵循"先渲染页面主体，再将主体嵌入布局"的标准两步渲染模式，并通过 `TemplateService.RenderPageWithLayout` 方法统一调用。
* 用户界面通过 `ui_preference` Cookie 控制在新旧版本之间切换。

## 3. 改造范围与具体要求

本次改造主要涉及 `FriendList.aspx` (前端模板) 和 `FriendList.aspx.cs` (代码后置) 文件。

### 3.1 `FriendList.aspx.cs` (代码后置)

* **实现新旧 UI 切换逻辑**：在 `Page_Load` 方法中，应用标准的 `CheckAndHandleUIPreference` 和 `TryRenderWithHandlebars` 模式，根据用户偏好决定是否渲染新版 UI。
* **构建数据模型**：
  * 在 `YaoHuo.Plugin/Template/Models/` 目录下创建新的数据模型类，例如 `FriendListPageModel.cs`。
  * 该模型应包含渲染页面所需的所有数据，包括不同 `friendtype` 对应的列表数据 (`listVo`)、分页信息 (`linkURL`)、搜索框相关数据 (`key`, 表单action等)、以及页面消息 (`INFO`, `ERROR`) 等。
  * 模型结构应清晰，反映页面各部分的层级和数据需求（例如，可以包含一个列表属性用于存放每个好友/黑名单项的数据模型）。
  * 在 `showclass` 方法中，在获取完数据 (`listVo`, `linkURL` 等) 后，构建 `FriendListPageModel` 实例。
  * 在 `goaddfriend` 方法中，在处理完添加逻辑后，同样构建 `FriendListPageModel` 实例，以便在渲染新版 UI 时显示处理结果和列表。
  * **特别注意：** 目前后端不提供头像和在线状态数据，在构建数据模型时，需为这些字段使用占位符（例如空字符串或默认值）。发送私信功能暂时也不具备，为私信按钮的链接或相关数据使用占位符。
* **调用 `TemplateService`**：在 `RenderWithHandlebars` 方法中，调用 `TemplateService.RenderPageWithLayout` 方法，并传入：
  * 页面模板路径 (例如：`~/Template/Pages/FriendList.hbs`)
  * 构建好的 `FriendListPageModel` 实例
  * 页面标题 (根据 `friendtype` 动态设置，例如"我的好友"、"我的黑名单")
  * 适当配置的 `HeaderOptionsModel` (例如，此页面可能不需要显示 UI 切换按钮，根据设计决定)
  * 页面特定的 CSS 文件路径 (例如：`/Template/CSS/FriendList.css`，如果需要)
* **处理数据缺失**：后端目前不提供头像和在线状态数据。在构建数据模型时，为这些字段使用占位符（例如空字符串或默认值）。
* **处理"发送私信"按钮**：后端目前没有提供私信功能的URL。在构建数据模型时，为私信按钮的链接或相关数据使用占位符，前端模板应将其渲染为非功能性的占位元素。
* **处理 `PrepareForSQL` 错误**：在 `goaddfriend` 方法中，原始代码尝试对 `friendnickname` 使用了不存在的 `WapTool.PrepareForSQL` 方法。根据项目规范和安全原则，数据库操作应使用参数化查询。虽然更新操作已参数化，但插入操作 (`insert into wap_friends`) 未对 `friendnickname` 参数化，存在潜在风险。应将插入操作修改为使用参数化查询，将 `text` (即 `user_Model.nickname`) 作为参数传入，彻底消除 SQL 注入风险。

### 3.2 `FriendList.aspx` (Handlebars 模板 - `FriendList.hbs`)

* **创建 Handlebars 模板**：在 `YaoHuo.Plugin/Template/Pages/` 目录下创建 `FriendList.hbs` 文件。
* **基于参考 HTML**：参考提供的 `friends.html` 和 `blocklist.html` 文件，但**不完全照搬**。目标是**参考**这两个文件的布局和样式效果，创建一个统一的 `FriendList.hbs` 模板，通过 Handlebars 的条件逻辑 (`{{#if}}`, `{{#eq}}`) 根据 `friendtype` 在模板内部渲染不同的内容结构。
* **复用现有 CSS**：优先使用 `HandlebarsCommon.css` 和 `HandlebarsForm.css` 中的样式。为 `FriendList` 页面特有的样式创建 `FriendList.css` 文件。
* **渲染数据**：使用 Handlebars 语法 (`{{...}}`, `{{{...}}}`) 绑定 `FriendListPageModel` 中的数据，渲染列表项、用户信息、消息提示等。
* **渲染占位符**：为暂未提供的头像、在线状态、发送私信按钮等功能渲染视觉上的占位元素，明确告知这些是未来将实现的功能。
* **实现搜索框**：在 `friendtype=0` 时，在模板中渲染搜索表单，绑定 `key` 变量。
* **前端处理分页**：后端返回的分页 HTML (`linkURL`) 无法直接在新版 UI 中使用。需要在前端 Handlebars 模板中编写 JavaScript 代码，**解析后端 `linkURL` 字符串（其 HTML 结构已在你的要求中提供）**，提取分页链接、当前页、总页数、总条数等分页信息，并在新版 UI 中重新生成符合新样式结构的分页导航。这部分 JS 应包含在 `FriendList.hbs` 模板的 `<script>` 标签内或单独的 JS 文件中引入。
* **实现前端确认对话框**：参考旧版 `FriendList.aspx` 中的删除等操作逻辑，在 Handlebars 模板中或引入的 JS 文件中编写 JavaScript，使用 `confirm()` 或自定义模态框实现操作前的确认提示。

### 3.3 `YaoHuo.Plugin/Template/CSS/`

* **创建页面特定 CSS**：在 `YaoHuo.Plugin/Template/CSS/` 目录下创建 `FriendList.css` 文件，包含该页面独有的样式规则。

## 4. 测试要求

* 测试不同 `friendtype` 值（好友、黑名单、追求等）下页面的显示是否正确。
* 测试新旧 UI 切换功能是否正常。
* 测试搜索功能 (`friendtype=0`) 是否能在新版 UI 下工作。
* 测试前端处理的分页导航是否正确生成并工作。
* 测试删除等操作的前端确认提示是否弹出。
* 检查页面是否有样式问题，特别是新旧样式冲突或响应式问题。
* 在代码后置文件中确认数据模型构建是否正确，没有遗漏或错误的数据。
* 验证 `goaddfriend` 方法中的插入操作已正确使用参数化查询。

## 5. 成功标准

* `FriendList` 页面能够成功通过 Handlebars 渲染出新的 UI。
* 新 UI 在不同 `friendtype` 下能正确显示相应的数据（包括占位符）。
* 新旧 UI 切换功能工作正常。
* 分页导航通过前端 JS 成功重新生成并可交互。
* 删除等操作的前端确认提示工作正常。
* 没有明显的 UI 错误或样式问题。
* `goaddfriend` 方法中的插入操作使用参数化查询，消除了 SQL 注入风险。

---

*这份文档概述了 `FriendList` 页面现代化改造的主要需求和步骤。在实际开发中，可能需要根据具体情况进行调整和细化。*
