---
description: "C# 编码风格、文档规范和最佳实践，包括 C# 7+ 特性使用、命名约定、错误处理、性能优化和安全注意事项。"
globs: *.aspx,*.ascx,*.aspx.cs,*.ascx.cs,*.cs
alwaysApply: false
---
## C# Style, Docs & Best Practices
- 遵循现代C# 7+风格，推荐用表达式体、字符串插值、模式匹配、**内联`out`变量声明、解构赋值、元组**等特性提升代码简洁性和可读性。鼓励使用 C# 6.0 及以上版本提供的集合初始化器（包括**索引初始化器**）简化代码。
- 变量/方法/类命名请参见主规则。
- 单一职责，结构清晰，变量命名清楚。
    - **静态帮助类的使用场景和优势**: 对于那些完全无状态、不依赖任何实例成员 (`this` 或 `base`)、仅根据输入参数进行操作的纯工具方法，应优先考虑将其实现为静态帮助类（Static Helper Classes）中的静态方法。这样做的好处包括提高代码的可测试性、可重用性，以及更清晰的职责分离——即使这些方法本身逻辑简单或代码行数不多。确保此类静态方法的所有依赖都通过参数明确传入。
    - **字段只读性**: 对于在声明时或构造函数中赋值后不再改变的字段，应使用 `readonly` 修饰符 (如 IDE0044 建议)，以增强不变性和代码清晰度。
- 复杂逻辑需中英文注释，公共方法建议XML注释。
    - **重构后的文档更新**: 在进行代码重构，特别是当提取出新的方法或类时，必须确保为这些新的代码单元（尤其是 `public` 或 `internal` 成员，以及职责复杂的 `private` 成员）编写清晰、准确的XML文档注释 (`///`)。注释应详细说明其功能、每个参数的含义、返回值以及任何重要的行为或前置条件。
- 错误处理仅对预期异常用try-catch，异常需日志记录。
- 日志用System.Diagnostics.Debug.WriteLine，生产建议集成更完善日志。
- 所有用户输入需服务端校验。
- SQL操作必须参数化，严禁拼接SQL。
- 循环高效，避免不必要嵌套。
- 合理使用缓存。
- 所有敏感操作需校验用户身份和权限。
- 防范XSS、SQL注入。
- 仅允许.NET Framework 4.8兼容库。
- **代码简洁性与IDE建议**: 积极采纳IDE（如Visual Studio, Rider, Cursor）提供的代码风格和现代化建议（例如消除冗余赋值 IDE0059、简化集合初始化 IDE0028、使用 `nameof` 等），以保持代码的简洁、高效和与时俱进，前提是不违反项目核心约束和兼容性要求。

**新增规则点 (基于重构经验):**

- **重构与可测试性**: 代码重构的一个重要目标应包括提升其可测试性。在分解复杂方法或重新组织逻辑时，应有意识地减少代码对 `HttpContext`、页面状态（如 `ViewState`, `Session`）以及其他难以模拟的全局状态的直接依赖。优先提取那些可以被改造成纯函数（输入决定输出，无副作用）或易于通过参数注入依赖的逻辑单元。如果可能，将这些单元设计为静态方法或独立服务类的方法，以便于编写单元测试。

- 相关Web Forms结构、性能等请参见主规则。

### SQL 注入风险与参数化查询实践

在 FriendList.aspx 的开发过程中，我们在 `goaddfriend` 方法中发现了一个因**字符串拼接**用户输入而导致的**SQL 注入漏洞**。这是一个典型的安全风险，攻击者可以构造恶意输入来修改或窃取数据库信息。

**问题示例 (存在漏洞的代码思路)**:
```csharp
// 假设的、存在漏洞的代码片段
string userName = GetRequestValue("userName"); // 直接获取用户输入
string sql = "INSERT INTO Friends (UserName) VALUES ('" + userName + "')"; // 直接拼接，存在风险
// 执行 SQL...
// 如果 userName 输入是 'abc'); DROP TABLE Friends; --
// 最终 SQL 会变成 INSERT INTO Friends (UserName) VALUES ('abc'); DROP TABLE Friends; --')
```

**解决方案 (使用参数化查询)**:
彻底消除 SQL 注入风险的**唯一安全方式**是使用**参数化查询**。本项目应优先通过 BLL/DAL 层（KeLin.ClassManager）访问数据，并确保所有数据操作都使用参数化。

对于直接执行 SQL 的场景，必须使用 `System.Data.SqlClient.SqlParameter` 数组配合 `KeLin.ClassManager.ExUtility.DbHelperSQL.ExecuteNonQuery` 或类似接受参数数组的方法。

**修复后的代码示例 (goaddfriend 方法)**:
```csharp
// FriendList.aspx.cs 中的 goaddfriend 方法片段 (修复后)
string _n = GetRequestValue("n"); // 获取用户输入的待添加好友用户名

// ... 其他业务逻辑和输入校验 ...

// 使用参数化查询执行 INSERT 操作
string cmdText = "INSERT INTO FriendList (myid, youid, addTime, state, isdel, [bz], fen组) " +
                 "VALUES (@myid, @youid, @addTime, @state, @isdel, @bz, @fenZu)";

SqlParameter[] parameters = new SqlParameter[]
{
    new SqlParameter("@myid", SqlDbType.Int) { Value = userVo.ID },
    new SqlParameter("@youid", SqlDbType.Int) { Value = youVo.ID }, // youVo.ID 是经过查库获取的安全用户ID
    new SqlParameter("@addTime", SqlDbType.DateTime) { Value = DateTime.Now },
    new SqlParameter("@state", SqlDbType.Int) { Value = 0 }, // 假设 0 代表待确认状态
    new SqlParameter("@isdel", SqlDbType.Int) { Value = 0 }, // 0 代表未删除
    new SqlParameter("@bz", SqlDbType.NVarChar, 50) { Value = "" }, // 备注，此处示例为空
    new SqlParameter("@fenZu", SqlDbType.NVarChar, 50) { Value = "默认分组" } // 分组，此处示例为默认
    // 注意：如果用户输入用于备注等字段，也必须作为 SqlParameter 添加
};

int rowsAffected = KeLin.ClassManager.ExUtility.DbHelperSQL.ExecuteNonQuery(cmdText, parameters);

if (rowsAffected > 0)
{
    // 添加好友请求成功处理...
}
else
{
    // 添加好友请求失败处理...
}
```

**核心要点**:
*   永远不要将用户输入直接拼接到 SQL 字符串中。
*   使用 `SqlParameter` 对象为每个参数赋值，框架会自动处理值中的特殊字符，防止注入。
*   指定参数的数据类型 (`SqlDbType`) 和长度是良好的实践。
*   `ExecuteNonQuery` 是执行 INSERT, UPDATE, DELETE 语句的常用方法。

这条实践经验再次强调了在本项目中，**参数化查询是进行所有数据库写入和修改操作的强制要求。**






