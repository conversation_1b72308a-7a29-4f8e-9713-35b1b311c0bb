﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class FriendList_Mod : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string string_11 = "";

		public string friendtype = "";

		public string id = "";

		public string backurl = "";

		public string INFO = "";

		public string page = "";

		public string touserid = "";

		public wap_friends_Model bookVo = null;

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			id = GetRequestValue("id");
			touserid = GetRequestValue("touserid");
			page = GetRequestValue("page");
			friendtype = GetRequestValue("friendtype");
			backurl = GetRequestValue("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			wap_friends_BLL wap_friends_BLL = new wap_friends_BLL(string_10);
			if (id != "")
			{
				bookVo = wap_friends_BLL.GetModel(long.Parse(id));
			}
			else if (touserid != "")
			{
				bookVo = wap_friends_BLL.GetModel(" siteid=" + siteid + " and userid=" + userid + " and friendtype=0 and frienduserid=" + long.Parse(touserid));
			}
			if (bookVo == null)
			{
				ShowTipInfo("Ta还不是我的好友，请先添加为好友。", backurl);
			}
			if (bookVo.siteid != siteVo.siteid)
			{
				ShowTipInfo("非本站记录", backurl);
			}
			if (bookVo.userid != userVo.userid)
			{
				ShowTipInfo("Ta还不是我的好友，请先添加为好友。", backurl);
			}
			if (action == "gomod")
			{
				bookVo.friendusername = WapTool.Left(GetRequestValue("remark"), 30);
				wap_friends_BLL.Update(bookVo);
				INFO = "OK";
			}
		}
	}
}