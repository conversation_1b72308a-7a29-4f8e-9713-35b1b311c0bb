﻿using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using System;
using System.Data;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class CreateUser : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");
        public string ConnectionString => PubConstant.GetConnectionString(a);

        public string INFO = "";
        public string ERROR = "";
        public string action = "";
        public string queryUserid = "";
        public string queryResult = "";
        public string RandomPassword { get; private set; }
        public string CreatedUserId { get; private set; }
        public string UpdatePostStatus { get; private set; }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 验证登录和管理员权限
            IsLogin(userid, "");
            IsCheckManagerLvl("|00|", "");

            // 增加空值检查
            if (siteVo == null || userVo == null)
            {
                RedirectAndStop();
                return;
            }

            // 验证站长权限
            if (siteVo.siteid != userVo.userid)
            {
                RedirectAndStop();
                return;
            }

            try
            {
                needPassWordToAdmin();
                action = Request.Form["action"] ?? Request.QueryString["action"] ?? "";

                if (action == "query")
                {
                    queryUserid = GetRequestValue("queryuserid");
                    if (!string.IsNullOrEmpty(queryUserid))
                    {
                        string sqlStr = "SELECT username,nickname,RegTime,LastLoginTime,bbsReCount,bbsCount,LoginTimes FROM [user] WHERE userid=" + queryUserid;
                        DataSet ds = DbHelperSQL.ExecuteDataset(ConnectionString, CommandType.Text, sqlStr);

                        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                        {
                            DataRow row = ds.Tables[0].Rows[0];
                            string username = row["username"].ToString();
                            string nickname = row["nickname"].ToString();
                            DateTime regTime = Convert.ToDateTime(row["RegTime"]);
                            string regTimeStr = regTime.ToString("yyyy/MM/dd");
                            string lastLoginStr = row["LastLoginTime"] != DBNull.Value
                                ? Convert.ToDateTime(row["LastLoginTime"]).ToString("yyyy/MM/dd")
                                : "从未登录";
                            int totalSeconds = Convert.ToInt32(row["LoginTimes"]);
                            string onlineTimeStr = FormatOnlineTime(totalSeconds);
                            int bbsCount = Convert.ToInt32(row["bbsCount"]);
                            int bbsReCount = Convert.ToInt32(row["bbsReCount"]);

                            queryResult = string.Format(
                                "账号：{0}<br/>昵称：{1}<br/>回复数量：{2}<br/>发帖数量：{3}<br/>在线时间：{4}<br/>注册日期：{5}<br/>最后登录：{6}",
                                username,
                                nickname,
                                bbsReCount,
                                bbsCount,
                                onlineTimeStr,
                                regTimeStr,
                                lastLoginStr);
                        }
                        else
                        {
                            queryResult = "未找到该用户信息";
                        }
                    }
                }
                else if (action == "delete")
                {
                    string userid = Request.Form["userid"] ?? "";

                    if (string.IsNullOrEmpty(userid))
                    {
                        ERROR = "用户ID不能为空！";
                        return;
                    }

                    if (userid == siteVo.siteid.ToString())
                    {
                        ERROR = "不能删除站长账号！";
                        return;
                    }

                    try
                    {
                        string deleteReplySql = "DELETE FROM [wap_bbsre] WHERE userid=@userid";
                        DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, deleteReplySql,
                            new System.Data.SqlClient.SqlParameter("@userid", userid));

                        string deleteTopicSql = "DELETE FROM [wap_bbs] WHERE book_pub=@userid";
                        DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, deleteTopicSql,
                            new System.Data.SqlClient.SqlParameter("@userid", userid));

                        string deleteUserSql = "DELETE FROM [user] WHERE userid=@userid";
                        DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, deleteUserSql,
                            new System.Data.SqlClient.SqlParameter("@userid", userid));

                        INFO = "OK";
                        queryResult = $"<b style=\"color:red;\">用户<span style=\"padding: 2px;\">{userid}</span>删除成功</b>";
                    }
                    catch (Exception ex)
                    {
                        ERROR = "删除用户时发生错误：" + ex.Message;
                    }
                }
                else if (action == "gomod")
                {
                    string userid = GetRequestValue("userid");
                    string username = GetRequestValue("username");
                    string nickname = GetRequestValue("nickname");
                    string password = GetRequestValue("password");

                    if (string.IsNullOrEmpty(userid))
                    {
                        ERROR = "ID号不能为空！";
                        return;
                    }

                    int userIdNum;
                    if (!int.TryParse(userid, out userIdNum))
                    {
                        ERROR = "ID号必须是纯数字！";
                        return;
                    }

                    if (userid.Length < 3 || userIdNum < 300 || userid.Length > 9)
                    {
                        ERROR = "ID必须是3-9位数字，且不能小于300！";
                        return;
                    }

                    RandomPassword = password;
                    CreatedUserId = userid;
                    string qq = GetRequestValue("qq");

                    if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                    {
                        ERROR = "ID号、用户名和密码不能为空！";
                        return;
                    }

                    if (string.IsNullOrEmpty(nickname))
                    {
                        nickname = "妖友" + userid;
                    }

                    string getuserid = MainBll.isHasExistUserName(username);
                    if (getuserid != "0")
                    {
                        ERROR = "用户名已存在！";
                        return;
                    }

                    string insertSql = $@"SET IDENTITY_INSERT [USER] ON 
                                         INSERT INTO [user] (userid,siteid,username,nickname,password,managerlvl,sex,
                                                           MaxPerPage_Content,MaxPerPage_Default
                                                           {(string.IsNullOrEmpty(qq) ? "" : ",MailServerUserName")}) 
                                         VALUES ('{userid}',
                                                {siteid},
                                                '{username}',
                                                '{nickname}',
                                                '{PubConstant.md5(password)}',
                                                '02',
                                                '1',
                                                32767,
                                                15
                                                {(string.IsNullOrEmpty(qq) ? "" : $",'{qq}'")})
                                         SET IDENTITY_INSERT [USER] OFF";

                    DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, insertSql);

                    string money = GetRequestValue("money");

                    if (!string.IsNullOrEmpty(userid))
                    {
                        int moneyValue = 0;
                        if (int.TryParse(money, out moneyValue) && moneyValue > 0)
                        {
                            string vipSql;
                            if (moneyValue == 10000)
                            {
                                // 只设置妖晶，不赠送靓号勋章
                                vipSql = $"UPDATE [user] SET money ='{moneyValue}', moneyname = NULL WHERE userid={userid}";
                            }
                            else
                            {
                                // 赠送靓号勋章
                                vipSql = $"UPDATE [user] SET moneyname ='靓号.gif', money ='{moneyValue}' WHERE userid={userid}";
                            }
                            DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, vipSql);
                        }
                    }

                    if (!string.IsNullOrEmpty(userid))
                    {
                        try
                        {
                            // 先获取原始内容
                            string originalContent = "";
                            using (var cmd = new System.Data.SqlClient.SqlCommand("SELECT book_content FROM wap_bbs WHERE id=138352", new System.Data.SqlClient.SqlConnection(ConnectionString)))
                            {
                                cmd.Connection.Open();
                                originalContent = (string)cmd.ExecuteScalar();
                                System.Diagnostics.Debug.WriteLine($"原始内容: {originalContent}");
                            }

                            // 先尝试删除"ID号，"的格式
                            string updateSql = $"UPDATE wap_bbs SET book_content = REPLACE(book_content, '{userid}，', '') WHERE id = 138352";
                            System.Diagnostics.Debug.WriteLine($"执行第一次更新SQL: {updateSql}");
                            int affected = DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, updateSql);
                            System.Diagnostics.Debug.WriteLine($"第一次更新影响行数: {affected}");

                            // 获取第一次更新后的内容
                            string contentAfterFirstUpdate = "";
                            using (var cmd = new System.Data.SqlClient.SqlCommand("SELECT book_content FROM wap_bbs WHERE id=138352", new System.Data.SqlClient.SqlConnection(ConnectionString)))
                            {
                                cmd.Connection.Open();
                                contentAfterFirstUpdate = (string)cmd.ExecuteScalar();
                                System.Diagnostics.Debug.WriteLine($"第一次更新后内容: {contentAfterFirstUpdate}");
                            }

                            // 如果没有更新成功，说明可能是最后一个ID，尝试删除"，ID号"的格式
                            if (affected == 0 || contentAfterFirstUpdate.Contains(userid))
                            {
                                updateSql = $"UPDATE wap_bbs SET book_content = REPLACE(book_content, '，{userid}', '') WHERE id = 138352";
                                System.Diagnostics.Debug.WriteLine($"执行第二次更新SQL: {updateSql}");
                                affected = DbHelperSQL.ExecuteNonQuery(ConnectionString, CommandType.Text, updateSql);
                                System.Diagnostics.Debug.WriteLine($"第二次更新影响行数: {affected}");

                                // 获取第二次更新后的内容
                                using (var cmd = new System.Data.SqlClient.SqlCommand("SELECT book_content FROM wap_bbs WHERE id=138352", new System.Data.SqlClient.SqlConnection(ConnectionString)))
                                {
                                    cmd.Connection.Open();
                                    string finalContent = (string)cmd.ExecuteScalar();
                                    System.Diagnostics.Debug.WriteLine($"最终内容: {finalContent}");
                                }
                            }

                            // 检查是否还存在该ID
                            using (var cmd = new System.Data.SqlClient.SqlCommand($"SELECT book_content FROM wap_bbs WHERE id=138352 AND (book_content LIKE '%{userid}，%' OR book_content LIKE '%，{userid}%')", new System.Data.SqlClient.SqlConnection(ConnectionString)))
                            {
                                cmd.Connection.Open();
                                var result = cmd.ExecuteScalar();
                                System.Diagnostics.Debug.WriteLine($"检查是否还存在ID: {(result != null ? "是" : "否")}");
                            }

                            UpdatePostStatus = "成功从出售帖中删除ID号";
                        }
                        catch (Exception ex)
                        {
                            UpdatePostStatus = "帖子更新失败：" + ex.Message;
                            System.Diagnostics.Debug.WriteLine($"更新帖子内容失败: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                        }
                    }

                    INFO = "OK";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.Message;
            }
        }

        private string FormatOnlineTime(int totalSeconds)
        {
            TimeSpan ts = TimeSpan.FromSeconds(totalSeconds);

            int years = (int)(ts.TotalDays / 365);
            int months = (int)((ts.TotalDays % 365) / 30);
            int days = (int)(ts.TotalDays % 30);
            int hours = ts.Hours;

            if (years > 0)
            {
                return string.Format("{0}年{1}月", years, months);
            }
            else if (months > 0)
            {
                return string.Format("{0}月{1}天", months, days);
            }
            else if (days > 0)
            {
                return string.Format("{0}天{1}小时", days, hours);
            }
            else
            {
                return string.Format("{0}小时", hours);
            }
        }

        private void RedirectAndStop()
        {
            Response.Clear();
            Response.Redirect("/", false);
            Context.ApplicationInstance.CompleteRequest();
        }
    }
}